NODE_ENV=local

APP_KEY=NlpZOWlrZEI5WkF6akp1UDg0clJXOTZFSlJZTEhBVEM=
APP_HOST=api.assethub.localhost
OAUTH2_CLIENT_REGISTRATION_LOGOUT_REDIRECT_URI=http://localhost:5173/sign-in
OAUTH2_CLIENT_REGISTRATION_LOGIN_REDIRECT_URI=http://localhost:5173/callback
OAUTH2_CLIENT_REGISTRATION_LOGIN_SCOPE="openid profile email"

FRONTEND_SIGNUP_URI=http://localhost:5173/sign-up
FRONTEND_SIGNIN_URI=http://localhost:5173/sign-in
FRONTEND_QUOTE_URI=http://localhost:5173/quotes
FRONTEND_RESET_PASSWORD_URI=http://localhost:5173/reset-password

DATABASE_USER=user
DATABASE_PASSWORD=password
DATABASE_NAME=app
DATABASE_HOST=bs-db
DATABASE_PORT=5432

KEYCLOAK_BASE_URL=http://keycloak.localhost:8080
KEYCLOAK_PORT=8080
KEYCLOAK_HOST=keycloak.localhost
KEYCLOAK_ORIGINAL_HOST=bs-keycloak
KEYCLOAK_REALM=tesedi
KEYCLOAK_REALM_RSA_PUBLIC_KEY=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlc/fiTjIm8oNgyr0lYsOF9A/7pwn0qEhRRxHlfkKhABzqSL4wiHIzMgMnlqlV2g5G0frpWS+8bX6bnWjkhbjBsEdRfMrPbn6f6s/yomu9F2BKc93d2rceaiyjVhwRuWhoXdGRqPPWIACm3mLt74Egoes5tjdp0eVY9S3UcITycE4Kqm0BGYTQON4e0Dmq7yYFSXKkXxWRlywkzo427i8VkGiaAQKQ3xRY5m6OV1oBZTaclyRksE1TQ5kOpkbQjgSaGhTHklhS7jYQSETE+HLfCV/FMOmyH4nqGNEBJjjhfB8lFq/kIcpvk0371TQw0GaFMl0qqdPs5PtHXutbs7hnwIDAQAB
KEYCLOAK_CLIENT_ID=portal-web-app
KEYCLOAK_CLIENT_SECRET=PORTAL-WEB-APP-SECRET
KEYCLOAK_DATABASE_NAME=keycloak
KEYCLOAK_ADMIN_USER=admin
KEYCLOAK_ADMIN_PASSWORD=admin
KEYCLOAK_USER_EMAIL=<EMAIL>
KEYCLOAK_USER_PASSWORD=tesedi
KEYCLOAK_USER_FIRST_NAME=Test
KEYCLOAK_USER_LAST_NAME=User
APP_TEST_USER_ID=151f12a3-bbe7-4812-b97d-d0939d7757ed

EMAIL_HOST=bs-mailpit
EMAIL_PORT=1025
EMAIL_SECURE=false
EMAIL_USER=user
EMAIL_PASS=password
EMAIL_FROM=<EMAIL>

GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=admin

# Pagination settings
PAGINATION_DEFAULT_LIMIT=50
PAGINATION_MAX_LIMIT=100

IASSET_USERNAME=<EMAIL>
IASSET_PASSWORD=pYYjWnLL6vwch3T+
IASSET_DOMAIN=testtesediws

FRESHDESK_DOMAIN=testassethub
FRESHDESK_API_KEY=dqG72AQjMaFpxbnZGcpA

BREVO_ENABLED=true
BREVO_API_KEY=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
BREVO_TEST_EMAIL=<EMAIL>

# Storage
OBJECT_STORAGE_PROVIDER=minio
AZURE_STORAGE_CONNECTION_STRING="#"
MINIO_DOMAIN=http://127.0.0.1:9100
MINIO_ENDPOINT=bs-minio
MINIO_PORT=9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_SSL=false

DB_LISTENER_API_KEY=a0sdua0dhsad9sahd09as