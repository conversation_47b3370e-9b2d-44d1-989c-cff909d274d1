---
title: 'Invitation system'
description: 'Invitation system in the project'
---

We have two endpoints for inviting users to specific organizations and the platform itself:

- `POST /users/invite` - invite user
- `POST /users/apply-invitation` - apply invitation

## Invite user

User invitations can be created using `POST /users/invite` endpoint. This endpoint can be accessed by the user who has
administration permissions (`administer` or `org-administer`) over the organization.

Endpoint parameters:

- `firstName` (optional) - user's first name
- `lastName` (optional) - user's last name
- `email` **(required)** - Email that will receive an invitation
- `organizations` **(required)** - one or multiple organization IDs to which the user must be added

The endpoint works for new users and also for already existing users. In case if user does not exist on the platform
yet - he/she will receive an email with the registration link. The email will contain a registration link and a list of
all organizations the user has access to. Triggering the endpoint with the same data for the same user - will result in
sending the invitation email again - which is implemented in the case the invitation was lost and the user asks to
resend him/her an invitation.

If the user is already registered and by inviting him/her we are just providing him/her access to a new
organization - the mail will contain a login link (instead of a registration link) and a list of all organizations the
user has access to. Resending the same data to the invitation endpoint will work the same way as when the user is not
yet registered, but the email will contain a login link instead or a registration link.

Please note that while the invitation is created we are creating a user instance in Keycloak and marking it as disabled.
We are also adding a special attribute `disableReason` which should always be an `invitation` for new users. If the user
is disabled for a different reason it will be impossible to invite him/her or to apply an invitation for such user.

## Apply invitation

This endpoint - `POST /users/apply-invitation` - can be used to apply to the invitation. The endpoint is accessible
without authorization, but a special token is required to access it.

Endpoint parameters:

- `firstName` (optional) - user's first name
- `lastName` (optional) - user's last name
- `password` **(required)** - user password
- `passwordConfirm` **(required)** - password confirmation
- `token` **(required)** - one or multiple organization IDs to which the user must be added

The invitation application is another name for registration. It can be done only with a special token, which is sent
only to unregistered users, so registered users will not be able to perform the application twice.

If the invitation was sent by mistake ok you don’t want the user to join anymore you have to ways to act:

- If the user was already registered and this is just an invitation to a new organization, you go ko Realm’s users in Keycloak and remove the organization group from the user.
- If the user is a new user, who is going to be registered by invitation, you can simply delete the user.
