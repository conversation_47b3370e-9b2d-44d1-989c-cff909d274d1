---
title: 'Data validation'
description: 'This document describes how Zod library is used in project'
---

We are using Zod library for schema definition, models generation, data validation, and API reference definition.
Defining a schema allows you to generate models for different use cases, such as DTOs, API reference, validation, etc.

Please check official [Zod documentation](https://zod.dev/) for more information about the library.

We are using additional libraries to extend Zod functionality and improve it's support for NestJS. Here is a list of
additional libraries that are used in the project:

- [@anatine/zod-openapi](https://www.npmjs.com/package/@anatine/zod-openapi) - library that extends Zod functionality
  to support OpenAPI specification. It allows to generate API reference from Zod schema definitions.
- [@anatine/zod-nestjs](https://www.npmjs.com/package/@anatine/zod-nestjs) - library that extends Zod functionality to
  support NestJS. It allows to generate DTOs from Zod schema definitions.

Please also check code of `quotes` domain for examples of usage of Zod library, it contains full cycle of schema
definition, model generation, data validation and API reference definition.

## Schema definition

Schema definition is a simple process. You can define a schema using the `z.object` method. Here is an example of a simple schema definition:

```typescript
import { z } from 'zod';
import { extendApi } from '@anatine/zod-openapi';

const QuoteSchema = extendApi(
    z.object({
        id: extendApi(z.string().uuid(), {
            description: 'Quote ID',
        }),

        quoteNumber: extendApi(z.string().max(128), {
            description: 'Quote Number',
        }),

        expirationDate: extendApi(z.date().nullable().optional(), {
            description: 'Expiration Date',
        }),
    }),
    {
        title: 'Quote',
        description: 'Quote model',
    },
);
```

Please also note usage of `extendApi()` wrapper function. It allows to fully use OpenApi functionality. Please check
[NPM package page](https://www.npmjs.com/package/@anatine/zod-openapi) for more details.

## Model generation

Model generation is a simple process. You can generate a model using the `createZodDto` method. Here is an example of a
simple model generation:

```typescript
export class QuoteModel extends createZodDto(QuoteSchema) {}
```

As a result, you will get a DTO class that is possible to use in NestJS controllers and API Reference generation. Here
is an example on how to use it in a controller:

```typescript
@Post('test')
@ApiCreatedResponse({
    description: 'Test endpoint',
    type: QuoteModel,
})
async createQuoteDev(
    @Res() res: Response,
    @Body() body: QuoteCreateModel,
) {

    ...

}
```

Please note that `QuoteModel` is used as a type for `@ApiCreatedResponse` decorator and also `QuoteCreateModel` (another
model created from schema) is used as a type for `@Body` decorator.
