---
title: 'Authorization'
description: 'Authorization and authentication in the project'
---

## Keycloak

The project uses [Keycloak](https://www.keycloak.org/) as an Identity and Access Management solution. Keycloak instance
for local development is configured in the `docker-compose.yml`. The official Keycloak image `keycloak/keycloak` located
on `quay.io` is used. The Keycloak instance is configured using environment variables in the `docker-compose.yml` file.
Detailed information on which environment variables are available can be found
[here](https://www.keycloak.org/server/all-config). Keycloak requires a database to store its data, so it depends on the same Postgres database as the
project, but uses a different database.

Keycloak also includes an RSA certificate for token verification. The certificate is located in the
`docker/keycloak/realms/keys` directory and is injected into Keycloak container during it's first time initialization.
If you need to change certificate and apply it to Keycloak instance, you need to perform
[wiping](./getting-started#makefile) procedure.

Keycloak instance contains two realms:

- `master` - default realm created by Keycloak
- `tesedi` - realm which contains all users, clients, groups and permissions for the project

`Tesedi` realm is getting imported while Keycloak instance is being initialized first time on your development
environment.

## Organizations, Roles and Permissions

Project uses **Keycloak's Groups** of `tesedi` realm to manage user's organizations, **Keycloak's Subgroups** are used
to manage user's role inside of organization. Also **Keycloak's Roles** mapped to a **Keycloak's Group** or
**Keycloak's Subgroup** are used for user's permissions management.

Here is a table of mapping between Keycloak entities and project entities:

| Keycloak entity | Project entity |
| --------------- | -------------- |
| Group           | Organization   |
| Subgroup        | Role           |
| Role            | Permission     |

## Flow

The project uses **OAUTH2** protocol for authorization. Here is a diagram of authorization flow:

![Authorization diagram](./assets/authorization.svg)

Backend part is not storing sessions or tokens. It uses the same certificate as Keycloak to check token's validity.
Please follow [API Reference](./getting-started#api-reference) to which endpoints are used for authorization.

## Guards

We have implemented several guards to protect routes and resources in the project. Here is a list of guards that are
currently supported:

- `@AuthenticatedGuard()` **(global)** - checks if user is authenticated - based on token validity
- `@PermissionsGuard()` **(global)** - checks if user has required permissions to access resource

Please note that some of the guards are marked with `(global)` which means that they are applied to all routes in the
project. But every rule has exceptions, so please it's possible to disable guards for specific routes or controllers:

- `@DisableAuthenticatedGuard()` - disables `@AuthenticatedGuard()` for specific route or controller
- `@DisablePermissionsGuard()` - disables `@PermissionsGuard()` for specific route or controller

### Authentication guard

Authentication guard is represented by `@AuthenticatedGuard()` decorator. It checks if user is authenticated by checking
token's validity. If token is valid, user is authenticated and can access protected resources. This guard is applied
**globally** to all routes in the project, but it's possible to disable it for specific routes or controllers by using
`@DisableAuthenticatedGuard()` decorator.

### Role Based Access Control (RBAC) guard

TBA
