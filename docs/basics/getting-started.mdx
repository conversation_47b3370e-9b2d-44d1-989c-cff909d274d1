---
title: 'Getting Started'
description: 'This document contains information on how to make project ready to use on your local environment'
---

## Project initialization

To start the project, you need to have Docker installed on your machine. If you don't have it, you can download it from
the official website: [Docker](https://www.docker.com/).

Project contains a `Makefile` which allows you to easily make project ready to use with just one command:

```bash
$ make up
```

After running this command you will have all necessary services up and running.

## Makefile

Makefile contains also a set of other commands that can be used to manage the project. Here is a list of available commands:

- `make up` - starts the project
- `make down` - stops the project and removes all containers
- `make wipe` - stops the project, removes all containers and volumes
- `make terminal` - opens a terminal in the `app` container
- `make kc-terminal` - opens a terminal in the `keycloak` container (see [Keycloak](.) section)
- `make fix-permissions` - fixes permissions for files created from inside the container, can be result of running MikroORM CLI commands (see [Database and ORM](./getting-started#database-and-orm) section)

## Project services

After running `make up` command you will have the following docker services up and running:

- `bs-app` - Main application service, code is located in `app` directory and uses `NestJS` framework
- `bs-init` - Service that runs initialization scripts, like creating databases, tables, etc, uses the same image as `bs-app`
- `bs-db` - PostgreSQL database service
- `bs-keycloak` - Keycloak service, used for authentication and authorization (see [Database and ORM](./getting-started#database-and-orm) section)
- `bs-mailpit` - Mailpit service, used for simulation of SMTP server with UI
- `bs-prometheus` - Prometheus service, used for monitoring and metrics collection
- `bs-postgres-exporter` - PostgreSQL exporter service, used for exporting metrics from PostgreSQL database and then collecting them with Prometheus

## API reference

The project uses the [NestJS](https://nestjs.com/) framework to create RESTful APIs. You can find the API documentation
by accessing Reference page at `GET /reference` endpoint. If you are running project locally you can access it by
clicking [here](http://127.0.0.1:3000/reference).

## Code structure

It was decided to follow Domain Driven Design (DDD) principles in the project. The project is divided into several
domains, each domain is a separate module and has its own directory in the `src/domain` directory. Please see
[Domains](./domains) page for more details.

## Database and ORM

The project uses the [MikroORM](https://mikro-orm.io/) library as an Object-Relational Mapping (ORM). The library in
configured according to standard procedure described [here](https://docs.nestjs.com/recipes/mikroorm) and
[here](https://mikro-orm.io/docs/usage-with-nestjs)

The configuration of the MikroORM is located in the `src/configs/mikro-orm.config.ts` file. Migrations can be found in
the `src/migrations` directory. According to entities and repositories - they can be found in multiple places:

- `src/entities` - is a leftover directory from PoC phase of the project, those entities will be moved to domains in the future, we have no repositories created at this time for those entities
- `src/domain/*/entities` and `src/domain/*/repositories` - each domain has its own entities and repositories, you can find them in corresponding directory using provided paths.

### Entities, Migrations and Seeds

While developing new functionality you may need to create a new table in database to simplify this process you can use
MikroORM CLI. With MicroORM CLI you can create new entities, migrations and seeds. To run MikroORM CLI you need to
access terminal of app container by calling:

```
make terminal
```

And after that you can run MikroORM CLI commands. For example after creation of entity clas you may need to create
migration fo it. To do this you can run:

```
npx mikro-orm migration:create
```

This will create a migration class in `/src/migrations` directory. After that you can run this migration by calling:

```
npx mikro-orm migration:up
```

While there is no document with detailed description of MikroORM CLI commands you can always check available commands
by running:

```
npx mikro-orm
```

Which will result in:

```
Usage: mikro-orm <command> [options]

Commands:
  mikro-orm cache:clear             Clear metadata cache
  mikro-orm cache:generate          Generate metadata cache
  mikro-orm generate-entities       Generate entities based on current database
                                    schema
  mikro-orm database:create         Create your database if it does not exist
  mikro-orm database:import <file>  Imports the SQL file to the database
  mikro-orm seeder:run              Seed the database using the seeder class
  mikro-orm seeder:create <seeder>  Create a new seeder class
  mikro-orm schema:create           Create database schema based on current
                                    metadata
  mikro-orm schema:drop             Drop database schema based on current
                                    metadata
  mikro-orm schema:update           Update database schema based on current
                                    metadata
  mikro-orm schema:fresh            Drop and recreate database schema based on
                                    current metadata
  mikro-orm migration:create        Create new migration with current schema
                                    diff
  mikro-orm migration:up            Migrate up to the latest version
  mikro-orm migration:down          Migrate one step down
  mikro-orm migration:list          List all executed migrations
  mikro-orm migration:check         Check if migrations are needed. Useful for
                                    bash scripts.
  mikro-orm migration:pending       List all pending migrations
  mikro-orm migration:fresh         Clear the database and rerun all migrations
  mikro-orm debug                   Debug CLI configuration

Options:
      --config                  Set path to the ORM configuration file   [array]
      --contextName, --context  Set name of config to load out of the ORM
                                configuration file. Used when config file
                                exports an array or a function
                                                   [string] [default: "default"]
  -v, --version                 Show version number                    [boolean]
  -h, --help                    Show help                              [boolean]

Examples:
  mikro-orm schema:update --run  Runs schema synchronization
```

Please also not that we have aliases for some of listed command in `package.json` file.

## Troubleshooting

It's possible to reset whole project top the original state. To achieve this you need to run following command:

```
make wipe
```

But please note that this command will remove all containers and volumes created by the project. It will also drop all
databases created by the project. Basically everything will be wiped except code.
