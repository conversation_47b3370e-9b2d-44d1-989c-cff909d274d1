---
title: 'Metrics System'
description: 'Detailed information about the metrics system in the project'
---

The metrics system in our project is designed to monitor and analyze various aspects of the application performance and
usage. We use Prometheus for collecting and storing metrics data.

### Prometheus Configuration

Prometheus is configured to scrape metrics from various endpoints exposed by our application. The configuration file for
Prometheus is located at `/docker/prometheus/prometheus.yml`. Here is an example configuration:

```yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'app'
    static_configs:
      - targets: ['localhost:9090']
```

### Exposing Metrics

Our application exposes metrics at the `/metrics` endpoint. This endpoint is automatically scraped by Prometheus at
regular intervals. The metrics are collected using the `prom-client` wrapped by `@willsoto/nestjs-prometheus` library in
our application.

### Custom Metrics

In addition to the default metrics, we also define custom metrics to track specific application events and performance
indicators. Custom metric should be defined in `providers` section of module definition. Here is an example of how to
define a custom metric:

```typescript
@Module({
    imports: [],
    controllers: [],
    providers: [
        makeGaugeProvider({
            name: 'session_count',
            help: 'Number of sessions',
        }),
    ],
    exports: [],
})
```

After that you can inject the metric in your service and use it to update the metric value:

```typescript
@Injectable()
export class MetricsService {
    constructor(
        @InjectMetric("session_count") private sessionCountGauge: Gauge<string>,
    ) {
    }

    async updateSessionCount(): Promise<void> {
        try {
            this.sessionCountGauge.set(10);
        } catch (e) {
            console.error('Failed to update session count metric:', e);
            this.sessionCountGauge.remove();
        }
    }
}
```

You can check `nestjs-prometheus` [documentation](https://github.com/willsoto/nestjs-prometheus) for more metrics types
and options.
