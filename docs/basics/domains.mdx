---
title: 'Domains'
description: 'Detailed information about the domains in the project'
---

## Domains

The project is divided into several domains, each domain is a separate module and has its own directory in the
`src/domain` directory. Each domain contains entities, controllers and services that are related to the domain or
specific business entity.  Here is a list of domains we currently have:

- `common` - Common domain contains entities, controllers and services that are shared across multiple domains
- `quotes` - Quotes domain contains entities, controllers and services for managing quotes
- `assets` - Assets domain contains entities, controllers and services for managing assets
- `entities` - Entities domain contains entities, controllers and services for managing entities (resellers, customers, etc.)
- `organizations` - Organizations domain contains entities, controllers and services for managing organizations
- `users` - Users domain contains entities, controllers and services for managing users
- `metrics` - Metrics domain contains controllers and services for metrics collection

## Core domains

Project also contains core domains/modules that are used across the whole project and are located in non-standard directories:

- `auth` - Authentication and authorization domain (see [Authorization](./authorization) section) implements connection with Keycloak and provides authentication endpoints and implement authentication guards.
- `rbac` - Role-based access control domain (see [Authorization](./authorization) section) implements role-based access control and provides decorators for checking permissions, performs roles and permissions synchronization with Keycloak.
- App module - Main module of the project that contains global configuration and imports all other modules.

