---
title: 'Azure Container Registry'
description: 'Pushing docker images to ACR through GitHub Actions'
---

<Info>
  [Install Azure CLI](https://learn.microsoft.com/en-us/cli/azure/install-azure-cli) and
  [create a container registry](https://learn.microsoft.com/en-us/azure/container-registry/container-registry-get-started-azure-cli)
  before proceeding.
</Info>

We use a GitHub Action to build docker image and push to [Azure Container Registry](https://learn.microsoft.com/es-es/azure/container-registry/).

Check
[how to configure a GitHub Action to create a container instance](https://learn.microsoft.com/en-us/azure/container-instances/container-instances-github-action)
to get a detailed explanation of how to configure the GitHub Action. We don't need to create a container instance, we just need to build the docker image and push to ACR.

## Github workflow configuration

### Create credentials for Azure
Get the resource ID of your resource group.
```bash
groupId=$(az group show \
  --name <resource-group-name> \
  --query id \
  --output tsv)
```

Create a service principal with the scope of the resource group and the role of Contributor.
```bash
az ad sp create-for-rbac \
  --scope $groupId \
  --role Contributor \
  --sdk-auth \
  --name "ACR Service Principal [environment]"
```

### Assign roles to the service principal
Get the registry ID.
```bash
registryId=$(az acr show \
  --name <registry-name> \
  --resource-group <resource-group-name> \
  --query id --output tsv)
```

Assign ArcPush and ArcPull roles to the service principal.
```bash
az role assignment create \
  --assignee <service-principal-id> \
  --role AcrPush \
  --scope $registryId
```

```bash
az role assignment create \
  --assignee <service-principal-id> \
  --role AcrPull \
  --scope $registryId
```

### Save credentials and variables
Create secrets and variables on GitHub repository settings for the environment.

| Secret | Value |
|--------|-------|
| `AZURE_CREDENTIALS` | The entire JSON output from the service principal creation step |
| `REGISTRY_USERNAME` | The `clientId` from the JSON output from the service principal creation |
| `REGISTRY_PASSWORD` | The `clientSecret` from the JSON output from the service principal creation |

| Variable | Value |
|--------|-------|
| `REGISTRY_LOGIN_SERVER`	 | The login server name of your registry. Example: _myregistry.azurecr.io_ |

## Reset service principal credentials
Credentials expire after 1 year by default. Check
[how to reset service principal credentials](https://learn.microsoft.com/en-us/cli/azure/azure-cli-sp-tutorial-7)
to get further information.
