# Makefile

CYAN = \e[36m
RESET = \e[0m

NETWORK_NAME := bs-net

.PHONY: up
up: create-network
	@if [ ! -f ./docker/shell/.bash_history ]; then touch ./docker/shell/.bash_history; fi
	@if [ ! -f ./.env ]; then cp ./.env.example ./.env; fi
	@printf "$(CYAN)\n\nStarting all services$(RESET)\n"
	@if [ "$(NOCACHE)" = "1" ]; then echo "$(CYAN)Building without cache\n"; fi
	@if [ "$(NOCACHE)" = "1" ]; then docker compose build --no-cache; else docker compose build; fi
	@chmod +x ./docker/init.sh
	@chmod +x ./docker/**/init.sh
	@if [ "$(DAEMON)" = "1" ]; then docker compose up -d; else docker compose up; fi

.PHONY: nup
nup:
	make up NOCACHE=1

.PHONY: dup
dup:
	make up DAEMON=1

.PHONY: lup
lup: create-network
	@if [ ! -f ./docker/shell/.bash_history ]; then touch ./docker/shell/.bash_history; fi
	@if [ ! -f ./.env ]; then cp ./.env.example ./.env; fi
	@printf "$(CYAN)\n\nStarting all services$(RESET)\n"
	@if [ "$(NOCACHE)" = "1" ]; then echo "$(CYAN)Building without cache\n"; fi
	@if [ "$(NOCACHE)" = "1" ]; then docker compose -f docker-compose.local.yml build --no-cache; else docker compose -f docker-compose.local.yml build; fi
	@chmod +x ./docker/init.sh
	@chmod +x ./docker/**/init.sh
	@if [ "$(DAEMON)" = "1" ]; then docker compose -f docker-compose.local.yml up -d; else docker compose -f docker-compose.local.yml up; fi


.PHONY: create-network
create-network:
	@docker network inspect $(NETWORK_NAME) >/dev/null 2>&1 || (echo "Creating network $(NETWORK_NAME)" && docker network create $(NETWORK_NAME))

.PHONY: remove-empty-network
remove-empty-network:
	@if [ "$(shell docker network inspect $(NETWORK_NAME) --format='{{len .Containers}}')" = "0" ]; then \
		echo "Network $(NETWORK_NAME) is empty. Removing it..."; \
		docker network rm $(NETWORK_NAME); \
	else \
		echo "Network $(NETWORK_NAME) is not empty, not removing."; \
	fi

.PHONY: down
down:
	@if [ ! -f ./.env ]; then cp ./.env.example ./.env; fi
	docker compose down --remove-orphans --volumes --rmi local
	$(MAKE) remove-empty-network

.PHONY: wipe
wipe:
	@read -p "Are you sure you want to delete the .env file, database files, and docker containers? [y/N] " confirm && \
	if [ "$$confirm" = "y" ]; then \
		rm -f ./.env; \
		rm -f ./.keycloak_keys_update_lock; \
		rm -f ./.seed_lock; \
		rm -f ./src/migrations/.snapshot-app.json; \
		echo "We need elevated permissions to drop database files"; \
		sudo rm -rf ./docker/postgresql/data; \
		if [ "$$(docker ps -a | grep bs-keycloak)" ]; then docker rm -f bs-keycloak; fi; \
		if [ "$$(docker ps -a | grep bs-db)" ]; then docker rm -f bs-db; fi; \
		$(MAKE) down; \
		echo "Done."; \
	else \
		echo "Operation cancelled."; \
	fi

.PHONY: fix-permissions
fix-permissions:
	sudo chown ${USER}:${USER} -R * .*

.PHONY: terminal
terminal:
	docker compose -f docker-compose.yml exec --workdir /app bs-app bash

.PHONY: kc-terminal
kc-terminal:
	docker compose -f docker-compose.yml exec --workdir /opt/keycloak/bin bs-keycloak bash
