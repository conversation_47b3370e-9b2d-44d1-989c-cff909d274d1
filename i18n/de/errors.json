{"ORGANIZATION": {"INVALID_ORGANIZATION_ID": "Ungültige Organisations‑ID – {id}", "ORGANIZATION_ID_HEADER_NOT_PROVIDED": "Organisations‑ID ist erforderlich, aber wurde in den Headern nicht übermittelt", "INVALID_ORGANIZATION_ID_FORMAT": "Ungültiges Format der Organisations‑ID – {organizationId}", "ORGANIZATION_NOT_FOUND_BY_ID": "Organisation mit der ID {id} wurde nicht gefunden", "ORGANIZATION_NOT_FOUND": "Organisation nicht gefunden"}, "USER": {"USER_NOT_FOUND": "Benutzer nicht gefunden", "FAILED_TO_FETCH_USER_DATA": "Fehler beim Abrufen der Benutzerdaten", "USER_NOT_FOUND_OR_INACTIVE": "Benutzer nicht gefunden oder inaktiv", "USAGE_OF_DISABLING_REASON_PROHIBITED": "Verwendung des Grundes {reason} ist nicht erlaubt", "USER_NOT_FOUND_ON_KEYCLOAK": "Benutzer auf Keycloak nicht gefunden", "USER_UNKNOWN": "<PERSON><PERSON><PERSON><PERSON>", "USER_IS_DISABLED": "Benutzer ist deaktiviert", "UNABLE_TO_INVITE_USER": "Einladung des Benutzers nicht möglich, bitte kontaktieren Sie das Support‑Team"}, "AUTH": {"AUTHENTICATION_TOKEN_IS_MISSING": "Authentifizierungs‑Token fehlt", "FAILED_TO_AUTHENTICATE_USER_CONTEXT": "Authentifizierung des Benutzerkontexts fehlgeschlagen", "INVALID_OR_EXPIRED_AUTH_TOKEN": "Ungültiger oder abgelaufener Authentifizierungs‑Token", "INVALID_CREDENTIALS": "Ungültige Anmeldedaten", "SESSION_IS_NOT_ACTIVE": "Sitzung ist nicht aktiv", "INVALID_CURRENT_PASSWORD": "Ungültiges aktuelles Passwort", "PASSWORD_AND_CONFIRMATION_MISMATCH": "Neues Passwort und Bestätigung stimmen nicht überein", "TOKEN_NOT_FOUND": "Token nicht gefunden", "TOKEN_HAS_EXPIRED": "Token ist abgelaufen", "USER_REQUIRES_PASSWORD_RESET": "Benutzer benötigt Passwort-Reset - Reset-E-Mail wurde gesendet", "INVALID_REFRESH_TOKEN": "Ungültiges Aktualisierungstoken", "USER_DOEST_HAVE_ANY_ORGANIZATIONS": "Benutzer gehört keiner Organisation an."}, "COMMON": {"CURSOR_SHOULD_BE_A_NUMERIC_STRING": "<PERSON>ursor sollte eine Zeichenkette mit einer Ganzzahl sein", "ERROR_PROCESSING_REQUEST": "Fehler bei der Verarbeitung der Anfrage", "USER_ID_IS_NOT_PROVIDED": "Benutzer‑ID wurde nicht angegeben", "ORGANIZATIONS_ARE_REQUIRED": "Organisationen sind erforderlich", "VALIDATION_FAILED": "Validierung fehlgeschlagen"}, "ROLE": {"ROLE_NOT_FOUND": "Rolle nicht gefunden", "NOT_ALLOWED_TO_UPDATE_ROLE": "<PERSON>e dürfen diese Rolle nicht aktualisieren", "ROLE_IS_ASSIGNED_TO_USERS": "Rolle ist Benutzern zugewiesen"}, "ENTITY": {"ENTITY_NOT_FOUND": "Einheit nicht gefunden", "NO_PRIMARY_CONTACT": "<PERSON><PERSON> primärer Ansprechpartner für diese Einheit gefunden", "NOT_FOUND_BY_ID": "Einheit mit der ID {id} nicht gefunden"}, "CUSTOMER": {"CUSTOMER_NOT_FOUND": "Kunde nicht gefunden"}, "ASSETS": {"NOT_ASSETS_FOUND_FOR_EXPORT": "<PERSON><PERSON> Assets zum Export gefunden", "ASSET_NOT_FOUND_BY_SERIAL_NO": "Asset mit der Seriennummer {serialNo} nicht gefunden", "ASSET_NOT_FOUND_BY_SERIAL_NO_AND_SKU": "Asset mit der Seriennummer {serialNo} und Produkt‑SKU {sku} nicht gefunden", "PRODUCT_SKU_IS_EMPTY": "Produkt-SKU ist leer", "PRODUCT_SKU_DOES_NOT_EXIST": "Produkt-SKU existiert nicht", "SERVICE_LEVEL_SKU_IS_EMPTY": "Service-Level-SKU ist leer", "SERVICE_LEVEL_SKU_DOES_NOT_EXIST": "Service-Level-SKU existiert nicht", "SERVICE_GROUP_SKU_DOES_NOT_EXIST": "Servicegruppen-SKU existiert nicht", "SERVICE_GROUP_SKU_IS_EMPTY": "Servicegruppen-SKU ist leer"}, "CONTRACT": {"CONTRACT_NOT_FOUND_BY_ID": "Vertrag mit der ID {id} nicht gefunden", "CONTRACT_NOT_FOUND": "Vertrag nicht gefunden", "ALL_CONTRACTS_MUST_BELONG_TO_SAME_OWNER_ENTITY": "Alle Verträge müssen derselben Eigentümerentität gehören", "CONTRACTS_MUST_BELONG_TO_SAME_GROUP_ID": "Verträge müssen derselben Gruppen-ID gehören"}, "QUOTE": {"QUOTE_NOT_FOUND_BY_ID": "Angebot mit der ID {id} nicht gefunden", "QUOTE_NOT_FOUND": "Angebot nicht gefunden"}, "INVITATION": {"INVITATION_NOT_FOUND": "Einladung nicht gefunden", "INVITATION_ALREADY_ACCEPTED": "Einladung bereits angenommen", "INVITATION_FOR_USER_NOT_FOUND": "Einladung für Benutzer {userId} nicht gefunden"}, "FILES": {"NO_PARSER_FOR_FILE_FORMAT": "Dateiparser für das Format {format} ist nicht implementiert", "FAILED_TO_GET_STREAM_FOR_DOWNLOAD": "Fehler beim Abrufen des Streams für den Download"}}