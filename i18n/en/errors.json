{"ORGANIZATION": {"INVALID_ORGANIZATION_ID": "Invalid organization id - {id}", "ORGANIZATION_ID_HEADER_NOT_PROVIDED": "Organization ID is required but not provided in headers", "INVALID_ORGANIZATION_ID_FORMAT": "Invalid organization id format - {organizationId}", "ORGANIZATION_NOT_FOUND_BY_ID": "Organization with ID {id} not found", "ORGANIZATION_NOT_FOUND": "Organization not found"}, "USER": {"USER_NOT_FOUND": "User not found", "FAILED_TO_FETCH_USER_DATA": "Failed to fetch user data", "USER_NOT_FOUND_OR_INACTIVE": "User not found or inactive", "USAGE_OF_DISABLING_REASON_PROHIBITED": "The use of the reason {reason} is prohibited", "USER_NOT_FOUND_ON_KEYCLOAK": "User not found on keycloak", "USER_UNKNOWN": "User unknown", "USER_IS_DISABLED": "User is disabled", "UNABLE_TO_INVITE_USER": "Unable to invite user, please contact support team for assistance"}, "AUTH": {"AUTHENTICATION_TOKEN_IS_MISSING": "Authentication token is missing", "FAILED_TO_AUTHENTICATE_USER_CONTEXT": "Failed to authenticate user context", "INVALID_OR_EXPIRED_AUTH_TOKEN": "Invalid or expired authentication token", "INVALID_CREDENTIALS": "Invalid credentials", "SESSION_IS_NOT_ACTIVE": "Session is not active", "PASSWORD_AND_CONFIRMATION_MISMATCH": "New password and confirmation do not match", "INVALID_CURRENT_PASSWORD": "Invalid current password", "TOKEN_NOT_FOUND": "Token not found", "TOKEN_HAS_EXPIRED": "Token has expired", "USER_REQUIRES_PASSWORD_RESET": "User requires password reset - reset email has been sent", "INVALID_REFRESH_TOKEN": "Invalid refresh token", "USER_DOEST_HAVE_ANY_ORGANIZATIONS": "User does not have any organizations."}, "COMMON": {"CURSOR_SHOULD_BE_A_NUMERIC_STRING": "Cursor should be a string representing an integer", "ERROR_PROCESSING_REQUEST": "Error processing request", "USER_ID_IS_NOT_PROVIDED": "User id is not provided", "ORGANIZATIONS_ARE_REQUIRED": "Organizations are required", "VALIDATION_FAILED": "Validation failed"}, "ROLE": {"ROLE_NOT_FOUND": "Role not found", "NOT_ALLOWED_TO_UPDATE_ROLE": "You are not allowed to update this role", "ROLE_IS_ASSIGNED_TO_USERS": "Role is assigned to users"}, "ENTITY": {"ENTITY_NOT_FOUND": "Entity not found", "NO_PRIMARY_CONTACT": "No primary contact found for this entity", "NOT_FOUND_BY_ID": "Entity with id {id} not found"}, "CUSTOMER": {"CUSTOMER_NOT_FOUND": "Customer not found"}, "ASSETS": {"NOT_ASSETS_FOUND_FOR_EXPORT": "No assets found for export", "ASSET_NOT_FOUND_BY_SERIAL_NO": "Asset {serialNo} not found", "ASSET_NOT_FOUND_BY_SERIAL_NO_AND_SKU": "Asset with serial number {serialNo} and product SKU {sku} not found", "PRODUCT_SKU_IS_EMPTY": "Product SKU is empty", "PRODUCT_SKU_DOES_NOT_EXIST": "Product SKU does not exist", "SERVICE_LEVEL_SKU_IS_EMPTY": "Service level SKU is empty", "SERVICE_LEVEL_SKU_DOES_NOT_EXIST": "Service level SKU does not exist", "SERVICE_GROUP_SKU_DOES_NOT_EXIST": "Service group SKU does not exist", "SERVICE_GROUP_SKU_IS_EMPTY": "Service group SKU is empty"}, "CONTRACT": {"CONTRACT_NOT_FOUND_BY_ID": "Contract with id {id} not found", "CONTRACT_NOT_FOUND": "Contract not found", "ALL_CONTRACTS_MUST_BELONG_TO_SAME_OWNER_ENTITY": "All contracts must belong to the same owner entity", "CONTRACTS_MUST_BELONG_TO_SAME_GROUP_ID": "Contracts must belong to the same group ID"}, "QUOTE": {"QUOTE_NOT_FOUND_BY_ID": "Quote with id {id} not found", "QUOTE_NOT_FOUND": "Quote not found"}, "INVITATION": {"INVITATION_NOT_FOUND": "Invitation not found", "INVITATION_ALREADY_ACCEPTED": "Invitation already accepted", "INVITATION_FOR_USER_NOT_FOUND": "Invitation for user {userId} not found"}, "FILES": {"NO_PARSER_FOR_FILE_FORMAT": "File parser for {format} is not implemented", "FAILED_TO_GET_STREAM_FOR_DOWNLOAD": "Failed to get stream for download"}, "FEATURE": {"FEATURE_IS_DISABLED": "Feature is disabled"}}