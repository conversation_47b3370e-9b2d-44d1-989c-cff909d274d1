/* DO NOT EDIT, file generated by nestjs-i18n */

/* eslint-disable */
/* prettier-ignore */
import { Path } from "nestjs-i18n";
/* prettier-ignore */
export type I18nTranslations = {
    "errors": {
        "ORGANIZATION": {
            "INVALID_ORGANIZATION_ID": string;
            "ORGANIZATION_ID_HEADER_NOT_PROVIDED": string;
            "INVALID_ORGANIZATION_ID_FORMAT": string;
            "ORGANIZATION_NOT_FOUND_BY_ID": string;
            "ORGANI<PERSON>ATION_NOT_FOUND": string;
        };
        "USER": {
            "USER_NOT_FOUND": string;
            "FAILED_TO_FETCH_USER_DATA": string;
            "USER_NOT_FOUND_OR_INACTIVE": string;
            "USAGE_OF_DISABLING_REASON_PROHIBITED": string;
            "USER_NOT_FOUND_ON_KEYCLOAK": string;
            "USER_UNKNOWN": string;
            "USER_IS_DISABLED": string;
            "UNABLE_TO_INVITE_USER": string;
        };
        "AUTH": {
            "AUTHENTICATION_TOKEN_IS_MISSING": string;
            "FAILED_TO_AUTHENTICATE_USER_CONTEXT": string;
            "INVALID_OR_EXPIRED_AUTH_TOKEN": string;
            "INVALID_CREDENTIALS": string;
            "SESSION_IS_NOT_ACTIVE": string;
            "INVALID_CURRENT_PASSWORD": string;
            "PASSWORD_AND_CONFIRMATION_MISMATCH": string;
            "TOKEN_NOT_FOUND": string;
            "TOKEN_HAS_EXPIRED": string;
            "USER_REQUIRES_PASSWORD_RESET": string;
            "INVALID_REFRESH_TOKEN": string;
            "USER_DOEST_HAVE_ANY_ORGANIZATIONS": string;
        };
        "COMMON": {
            "CURSOR_SHOULD_BE_A_NUMERIC_STRING": string;
            "ERROR_PROCESSING_REQUEST": string;
            "USER_ID_IS_NOT_PROVIDED": string;
            "ORGANIZATIONS_ARE_REQUIRED": string;
            "VALIDATION_FAILED": string;
        };
        "ROLE": {
            "ROLE_NOT_FOUND": string;
            "NOT_ALLOWED_TO_UPDATE_ROLE": string;
            "ROLE_IS_ASSIGNED_TO_USERS": string;
        };
        "ENTITY": {
            "ENTITY_NOT_FOUND": string;
            "NO_PRIMARY_CONTACT": string;
            "NOT_FOUND_BY_ID": string;
        };
        "CUSTOMER": {
            "CUSTOMER_NOT_FOUND": string;
        };
        "ASSETS": {
            "NOT_ASSETS_FOUND_FOR_EXPORT": string;
            "ASSET_NOT_FOUND_BY_SERIAL_NO": string;
            "ASSET_NOT_FOUND_BY_SERIAL_NO_AND_SKU": string;
            "PRODUCT_SKU_IS_EMPTY": string;
            "PRODUCT_SKU_DOES_NOT_EXIST": string;
            "SERVICE_LEVEL_SKU_IS_EMPTY": string;
            "SERVICE_LEVEL_SKU_DOES_NOT_EXIST": string;
            "SERVICE_GROUP_SKU_DOES_NOT_EXIST": string;
            "SERVICE_GROUP_SKU_IS_EMPTY": string;
        };
        "CONTRACT": {
            "CONTRACT_NOT_FOUND_BY_ID": string;
            "CONTRACT_NOT_FOUND": string;
            "ALL_CONTRACTS_MUST_BELONG_TO_SAME_OWNER_ENTITY": string;
            "CONTRACTS_MUST_BELONG_TO_SAME_GROUP_ID": string;
        };
        "QUOTE": {
            "QUOTE_NOT_FOUND_BY_ID": string;
            "QUOTE_NOT_FOUND": string;
        };
        "INVITATION": {
            "INVITATION_NOT_FOUND": string;
            "INVITATION_ALREADY_ACCEPTED": string;
            "INVITATION_FOR_USER_NOT_FOUND": string;
        };
        "FILES": {
            "NO_PARSER_FOR_FILE_FORMAT": string;
            "FAILED_TO_GET_STREAM_FOR_DOWNLOAD": string;
        };
        "FEATURE": {
            "FEATURE_IS_DISABLED": string;
        };
    };
};
/* prettier-ignore */
export type I18nPath = Path<I18nTranslations>;
