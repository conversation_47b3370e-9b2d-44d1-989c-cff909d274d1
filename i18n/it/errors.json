{"ORGANIZATION": {"INVALID_ORGANIZATION_ID": "ID organizzazione non valida – {id}", "ORGANIZATION_ID_HEADER_NOT_PROVIDED": "ID organizzazione richiesta ma non fornita negli header", "INVALID_ORGANIZATION_ID_FORMAT": "Formato ID organizzazione non valido – {organizationId}", "ORGANIZATION_NOT_FOUND_BY_ID": "Organizzazione con ID {id} non trovata", "ORGANIZATION_NOT_FOUND": "Organizzazione non trovata"}, "USER": {"USER_NOT_FOUND": "Utente non trovato", "FAILED_TO_FETCH_USER_DATA": "Errore durante il recupero dei dati utente", "USER_NOT_FOUND_OR_INACTIVE": "Utente non trovato o inattivo", "USAGE_OF_DISABLING_REASON_PROHIBITED": "L'uso del motivo {reason} non è consentito", "USER_NOT_FOUND_ON_KEYCLOAK": "Utente non trovato in Keycloak", "USER_UNKNOWN": "Utente sconosciuto", "USER_IS_DISABLED": "Utente disabilitato", "UNABLE_TO_INVITE_USER": "Impossibile invitare l'utente, contattare il team di supporto"}, "AUTH": {"AUTHENTICATION_TOKEN_IS_MISSING": "Token di autenticazione mancante", "FAILED_TO_AUTHENTICATE_USER_CONTEXT": "Autenticazione del contesto utente non riuscita", "INVALID_OR_EXPIRED_AUTH_TOKEN": "Token di autenticazione non valido o scaduto", "INVALID_CREDENTIALS": "Credenziali non valide", "SESSION_IS_NOT_ACTIVE": "Sessione non attiva", "INVALID_CURRENT_PASSWORD": "Password attuale non valida", "PASSWORD_AND_CONFIRMATION_MISMATCH": "La nuova password e la conferma non corrispondono", "TOKEN_NOT_FOUND": "Token non trovato", "TOKEN_HAS_EXPIRED": "<PERSON><PERSON> scaduto", "USER_REQUIRES_PASSWORD_RESET": "L'utente deve reimpostare la password - Email di reimpostazione inviata", "INVALID_REFRESH_TOKEN": "Token di aggiornamento non valido", "USER_DOEST_HAVE_ANY_ORGANIZATIONS": "L'utente non appartiene a nessuna organizzazione."}, "COMMON": {"CURSOR_SHOULD_BE_A_NUMERIC_STRING": "Il cursore deve essere una stringa numerica intera", "ERROR_PROCESSING_REQUEST": "Errore durante l'elaborazione della richiesta", "USER_ID_IS_NOT_PROVIDED": "ID utente non fornito", "ORGANIZATIONS_ARE_REQUIRED": "Le organizzazioni sono richieste", "VALIDATION_FAILED": "Validazione fallita"}, "ROLE": {"ROLE_NOT_FOUND": "Ruolo non trovato", "NOT_ALLOWED_TO_UPDATE_ROLE": "Non sei autorizzato ad aggiornare questo ruolo", "ROLE_IS_ASSIGNED_TO_USERS": "Il ruolo è assegnato agli utenti"}, "ENTITY": {"ENTITY_NOT_FOUND": "Entità non trovata", "NO_PRIMARY_CONTACT": "Nessun referente principale trovato per questa entità", "NOT_FOUND_BY_ID": "Entità con ID {id} non trovata"}, "CUSTOMER": {"CUSTOMER_NOT_FOUND": "Cliente non trovato"}, "ASSETS": {"NOT_ASSETS_FOUND_FOR_EXPORT": "Nessun asset trovato per l'esportazione", "ASSET_NOT_FOUND_BY_SERIAL_NO": "Asset con numero di serie {serialNo} non trovato", "ASSET_NOT_FOUND_BY_SERIAL_NO_AND_SKU": "Asset con numero di serie {serialNo} e SKU del prodotto {sku} non trovato", "PRODUCT_SKU_IS_EMPTY": "SKU del prodotto vuoto", "PRODUCT_SKU_DOES_NOT_EXIST": "Lo SKU del prodotto non esiste", "SERVICE_LEVEL_SKU_IS_EMPTY": "SKU del livello di servizio vuoto", "SERVICE_LEVEL_SKU_DOES_NOT_EXIST": "Lo SKU del livello di servizio non esiste", "SERVICE_GROUP_SKU_DOES_NOT_EXIST": "Lo SKU del gruppo di servizi non esiste", "SERVICE_GROUP_SKU_IS_EMPTY": "SKU del gruppo di servizi vuoto"}, "CONTRACT": {"CONTRACT_NOT_FOUND_BY_ID": "<PERSON><PERSON><PERSON> con ID {id} non trovato", "CONTRACT_NOT_FOUND": "Contratto non trovato", "ALL_CONTRACTS_MUST_BELONG_TO_SAME_OWNER_ENTITY": "Tutti i contratti devono appartenere alla stessa entità proprietaria", "CONTRACTS_MUST_BELONG_TO_SAME_GROUP_ID": "I contratti devono appartenere allo stesso ID gruppo"}, "QUOTE": {"QUOTE_NOT_FOUND_BY_ID": "Preventivo con ID {id} non trovato", "QUOTE_NOT_FOUND": "Preventivo non trovato"}, "INVITATION": {"INVITATION_NOT_FOUND": "Invito non trovato", "INVITATION_ALREADY_ACCEPTED": "Invito gi<PERSON> accettato", "INVITATION_FOR_USER_NOT_FOUND": "Invito per l'utente {userId} non trovato"}, "FILES": {"NO_PARSER_FOR_FILE_FORMAT": "Parser per il formato {format} non implementato", "FAILED_TO_GET_STREAM_FOR_DOWNLOAD": "Impossibile ottenere il flusso per il download"}}