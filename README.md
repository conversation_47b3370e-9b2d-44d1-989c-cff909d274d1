# AssetHub Backend

## Project environments

- `local` - local environment
- `test` - testing environment'
- `dev` - development environment
- `staging` - staging environment
- `production` - production environment

## Project initialization

To start the project, you need to have Docker installed on your machine. If you don't have it, you can download it from 
the official website: [Docker](https://www.docker.com/).
```bash
$ make up
```

Check `.env` file for the keycloak admin and default user login information.  

## Project setup

```bash
$ npm install
```

## Compile and run the project

```bash
# development
$ npm run start

# watch mode
$ npm run start:dev

# production mode
$ npm run start:prod
```

## Run tests

```bash
# unit tests
$ npm run test

# e2e tests
$ npm run test:e2e

# test coverage
$ npm run test:cov
```

## Used scripts

- `create-multiple-postgresql-databases.sh` - script for creating multiple databases in PostgreSQL (GIT url - https://github.com/mrts/docker-postgresql-multiple-databases)

---
## Testing conventions
This section defines our testing standards to ensure high code quality and maintainable, well-tested software. All team members are expected to follow these guidelines when writing tests.

## 📌 Philosophy

- **Test behavior, not implementation**
- **High coverage is important, but meaningful tests come first**
- **Aim for 90%+ coverage across the project**
- **Write both unit and integration tests where appropriate**
- **Treat tests as production code — readable, maintainable, and reliable**

# 📁 Folder Structure

All test files must be placed in a `__tests__` folder colocated with the code being tested:   
``` 
src/
├── user/
│ ├── user.service.ts
│ └── __tests__/
│ └── user.service.spec.ts
```

All common mocks (guards, mail services, 3rd party services) must be placed in:
```
./
├── test/
│ ├── mocks
```


# 🧪 Types of Tests
- **Unit**
- **Integration**
- **E2E**

# 🧰 Tooling

```json
"scripts": {
    "test": "jest",
    "test:cov": "jest --coverage",
    "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand",
    "test:e2e": "jest --config ./test/jest-e2e.json",
    "test:watch": "jest --watch",
}
```

# 🧼 Naming Conventions
- **Unity/Integration test file:** ```*.spec.ts```
- **E2E test file:** ```*.e2e-spec.ts```
- **Describe blocks**: the method being tested
- **Test names**: express the intent clearly

Example:

```javascript 
describe('getUserById', () => {
  it('should return a user when found', () => {});
  it('should throw if user not found', () => {});
});
```

# 🧠 What to Test
## Services
- Every public method 
- All branches, edge cases, and failure paths

# Controllers
- HTTP response codes 
- Validation and error handling 
- Auth guards and interceptors

# Repositories
- Filters and query logic 
- Pagination and ordering

# Pipes / Guards
- All transform, validate, or canActivate logic

# 🛠 Mocking Guidelines
## Unit Tests
- Use jest.fn() to mock all external dependencies 
- Avoid over-mocking internal logic

## Integration Tests
- Use real modules via Test.createTestingModule 
- Use .overrideProvider() for specific service mocks only when needed

## 📊 Coverage Targets

To maintain a high-quality codebase, we require the following minimum test coverage levels across all layers:

| Layer          | Target Coverage |
|----------------|-----------------|
| Services       | 100%            |
| Controllers    | 100%            |
| Pipes & Guards | 100%            |
| Repositories   | 85–90%          |
| E2E Tests      | Key flows only  |

> **Note:** Repositories may be more complex due to database branching logic; focus on meaningful coverage of all query conditions and filters.

## ✅ Coverage Threshold Enforcement

We enforce coverage thresholds using Jest. Rulse are in package.json

```json
"coverageThreshold": {
  "global": {
    "branches": 80,
    "functions": 80,
    "lines": 80,
    "statements": 80,
  }
}
```

# 🔁 Test File Checklist
Every test file must:
- [ ] Cover success and failure scenarios
- [ ]  Use clear, descriptive names
- [ ] Avoid unnecessary mocks
- [ ] Be colocated in __tests__ directory

# 📎 Examples
```src/domain/entities/__tests__/entities.service.spec.ts```
---

# 🌍 Localization
- All localizations files are stored in the ```/i18n/**``` in separate folder for each language.
- Generated type for localization message path is located in ```/i18n/generated/i18n.generated.ts```. This file should not be changed manually. 
- All exceptions that user will see should be localized.
---
## Inspiration

- https://medium.com/keycloak/keycloak-express-openid-client-fabea857f11f
- https://github.com/austincunningham/keycloak-express-openid-client
- https://github.com/melikhov-dev/nest-openid-client-passport
- https://sdoxsee.github.io/blog/2020/02/05/cats-nest-nestjs-mongo-oidc.html
- https://github.com/nestjs/docs.nestjs.com/issues/99

## TODO

- We can update `@nestjs/swagger` to version 8 after dependencies of the package `@anatine/zod-nestjs` will be updated


## Resources

Check out a few resources that may come in handy when working with NestJS:

- Visit the [NestJS Documentation](https://docs.nestjs.com) to learn more about the framework.
- For questions and support, please visit our [Discord channel](https://discord.gg/G7Qnnhy).
- To dive deeper and get more hands-on experience, check out our official video [courses](https://courses.nestjs.com/).
- Visualize your application graph and interact with the NestJS application in real-time using [NestJS Devtools](https://devtools.nestjs.com).
- Need help with your project (part-time to full-time)? Check out our official [enterprise support](https://enterprise.nestjs.com).
- To stay in the loop and get updates, follow us on [X](https://x.com/nestframework) and [LinkedIn](https://linkedin.com/company/nestjs).
- Looking for a job, or have a job to offer? Check out our official [Jobs board](https://jobs.nestjs.com).

## Support

Nest is an MIT-licensed open source project. It can grow thanks to the sponsors and support by the amazing backers. If you'd like to join them, please [read more here](https://docs.nestjs.com/support).

## Stay in touch

- Author - [Kamil Myśliwiec](https://twitter.com/kammysliwiec)
- Website - [https://nestjs.com](https://nestjs.com/)
- Twitter - [@nestframework](https://twitter.com/nestframework)

## License

Nest is [MIT licensed](https://github.com/nestjs/nest/blob/master/LICENSE).
