## [1.39.1](https://github.com/Annuity-Management/assethub-backend/compare/v1.39.0...v1.39.1) (2025-07-22)


### Bug Fixes

* migrations ([b791d5c](https://github.com/Annuity-Management/assethub-backend/commit/b791d5cfd27abb71d0b3411bdb26a4af8066b113))

# [1.39.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.38.2...v1.39.0) (2025-07-22)


### Bug Fixes

* add migration for data fields ([#502](https://github.com/Annuity-Management/assethub-backend/issues/502)) ([9e5db04](https://github.com/Annuity-Management/assethub-backend/commit/9e5db04eb5359a57d63f9260d0c9500de0cfd883))
* **AH-466:** fix contract numbers parsing ([#503](https://github.com/Annuity-Management/assethub-backend/issues/503)) ([9a9958e](https://github.com/Annuity-Management/assethub-backend/commit/9a9958e87b0c15f2995c1af3c756e9a675c5d439))
* **AH-580:** fix password exception ([#481](https://github.com/Annuity-Management/assethub-backend/issues/481)) ([ca32274](https://github.com/Annuity-Management/assethub-backend/commit/ca322743b9f27ecb85dd32036fa6f4bb6325e6df))
* **AH-664:** inactivate org when no entities ([#479](https://github.com/Annuity-Management/assethub-backend/issues/479)) ([434971c](https://github.com/Annuity-Management/assethub-backend/commit/434971c977764e39de58b961741bb47bbad58f87))
* **AH-664:** incorrect length ([#491](https://github.com/Annuity-Management/assethub-backend/issues/491)) ([0568b2c](https://github.com/Annuity-Management/assethub-backend/commit/0568b2c89a87d6a62cf224657c6423ea9425dfaf))
* **AH-664:** org status fix ([#488](https://github.com/Annuity-Management/assethub-backend/issues/488)) ([4f6801a](https://github.com/Annuity-Management/assethub-backend/commit/4f6801a3e72a0537ac3b7bca29182efa50180c6e))
* **AH-683:** fix global search for quotes, contracts and assets ([#458](https://github.com/Annuity-Management/assethub-backend/issues/458)) ([d35401a](https://github.com/Annuity-Management/assethub-backend/commit/d35401a3dced89c35a2bf7c45a85ed6f6b343f7a))
* **AH-683:** fix global search for quotes, contracts and assets ([#458](https://github.com/Annuity-Management/assethub-backend/issues/458)) ([024933a](https://github.com/Annuity-Management/assethub-backend/commit/024933a645a502e1dfe8f232c443803c80366ee8))
* **AH-724:** brevo language ([#489](https://github.com/Annuity-Management/assethub-backend/issues/489)) ([a1c0b18](https://github.com/Annuity-Management/assethub-backend/commit/a1c0b18cda01bf96ebaf9581f77ae61fb265d015))
* **AH-XXX:** default org locale ([#462](https://github.com/Annuity-Management/assethub-backend/issues/462)) ([b0e1433](https://github.com/Annuity-Management/assethub-backend/commit/b0e1433cd72b4860f8d46a43c9e1811d7fdaded1))
* **AH-XXX:** drizzle journal ([#476](https://github.com/Annuity-Management/assethub-backend/issues/476)) ([3a52dd1](https://github.com/Annuity-Management/assethub-backend/commit/3a52dd1ec015f31a49ac51d2384569a174089506))
* Assign quote request for contract ([#509](https://github.com/Annuity-Management/assethub-backend/issues/509)) ([afd5dfe](https://github.com/Annuity-Management/assethub-backend/commit/afd5dfee415a6fc333f0ac6462f788b540f8ddbd))
* change log level ([d98ff2b](https://github.com/Annuity-Management/assethub-backend/commit/d98ff2bfcebc27e9fc9c2d53ec3040cf18d10054))
* migration ([761e75b](https://github.com/Annuity-Management/assethub-backend/commit/761e75b37636f834ab0c293f220c5c3d9a26a91b))
* Order organizations alphabetically ([#474](https://github.com/Annuity-Management/assethub-backend/issues/474)) ([7fe9dc8](https://github.com/Annuity-Management/assethub-backend/commit/7fe9dc8c9f06f8626c656b263dd7333203977157))
* typo ([#485](https://github.com/Annuity-Management/assethub-backend/issues/485)) ([8ff320c](https://github.com/Annuity-Management/assethub-backend/commit/8ff320c7a066ceefdbd41e806f6670fd43b35e34))


### Features

* **AH-466:** take language from dropdown ([#507](https://github.com/Annuity-Management/assethub-backend/issues/507)) ([8871799](https://github.com/Annuity-Management/assethub-backend/commit/887179979c6cbe10092a3366a358c39cceed2420))
* **AH-661:** handle null in cursor pagination. fix asset filters ([#501](https://github.com/Annuity-Management/assethub-backend/issues/501)) ([cec6451](https://github.com/Annuity-Management/assethub-backend/commit/cec645111d2c6a12c64fe5d830c7a58e829abef3))
* **AH-661:** return vendor list available for reseller only ([#495](https://github.com/Annuity-Management/assethub-backend/issues/495)) ([fe2e81b](https://github.com/Annuity-Management/assethub-backend/commit/fe2e81b642e46abcbcc8c2d64c840fd22e48867d))
* **AH-661:** update asset view contract item select ([#469](https://github.com/Annuity-Management/assethub-backend/issues/469)) ([bbc0d39](https://github.com/Annuity-Management/assethub-backend/commit/bbc0d39d40d88dd96b377f1b2497f23c6491cdbc))
* **AH-661:** update import create method. update vendor joining for quotes/contracts ([#473](https://github.com/Annuity-Management/assethub-backend/issues/473)) ([9a84068](https://github.com/Annuity-Management/assethub-backend/commit/9a84068f2406ef5bc355f40ddd3f18b39112a6cd))
* **AH-661:** update query for assets start/end dates ([#468](https://github.com/Annuity-Management/assethub-backend/issues/468)) ([2807bc2](https://github.com/Annuity-Management/assethub-backend/commit/2807bc2213d9ddbb8ce28ba9f9d3c893e795a16a))
* **AH-661:** update returning data for quotes, contracts and assets ([#456](https://github.com/Annuity-Management/assethub-backend/issues/456)) ([dd0734c](https://github.com/Annuity-Management/assethub-backend/commit/dd0734c353de41f8f67bcc605d59ab8c88b3e613))
* **AH-661:** update sorting and fitlering ([#483](https://github.com/Annuity-Management/assethub-backend/issues/483)) ([c92be8d](https://github.com/Annuity-Management/assethub-backend/commit/c92be8d8a1d2aa745a8aee74b49c8b39bd7ef404))
* **AH-661:** use nulls first/last for sorting ([#499](https://github.com/Annuity-Management/assethub-backend/issues/499)) ([db6b4de](https://github.com/Annuity-Management/assethub-backend/commit/db6b4de0491a4c5bb72f134ac83b7b0ef06a16c5))
* **AH-663:** org soft delete ([#445](https://github.com/Annuity-Management/assethub-backend/issues/445)) ([6a3b0d3](https://github.com/Annuity-Management/assethub-backend/commit/6a3b0d3f47a42bd2f79e27a790e389ebfe521cfd))
* **AH-667:** contract group notifications ([#490](https://github.com/Annuity-Management/assethub-backend/issues/490)) ([26c5133](https://github.com/Annuity-Management/assethub-backend/commit/26c5133e9a5d752da9ef704541bb434605ed4667))
* **AH-701:** contract import parser ([#460](https://github.com/Annuity-Management/assethub-backend/issues/460)) ([d97db62](https://github.com/Annuity-Management/assethub-backend/commit/d97db62b528d6ed340dbd8a247fc24217f782151))
* **AH-703:** update import validation ([#472](https://github.com/Annuity-Management/assethub-backend/issues/472)) ([dbd57ff](https://github.com/Annuity-Management/assethub-backend/commit/dbd57ffaf0eadc7ab6749dfc0336abd94b233aee))
* **AH-704:** implement contract and contract items creation endpoints ([#466](https://github.com/Annuity-Management/assethub-backend/issues/466)) ([ae968b1](https://github.com/Annuity-Management/assethub-backend/commit/ae968b1ae57d7100e67428b35de76a9b4b545bf6))
* **AH-709:** fix display of the vendor for related quotes/contracts ([#470](https://github.com/Annuity-Management/assethub-backend/issues/470)) ([f074ef9](https://github.com/Annuity-Management/assethub-backend/commit/f074ef986b8e31aef53359ea57cc0fe66c9f90f0))
* **AH-718:** update global search ([#504](https://github.com/Annuity-Management/assethub-backend/issues/504)) ([54a5a99](https://github.com/Annuity-Management/assethub-backend/commit/54a5a9990dc7d6f62d5e3bda61cfd33efa93923a))
* **AH-720:** change invitation roleId field to support several roles… ([#492](https://github.com/Annuity-Management/assethub-backend/issues/492)) ([bb4d57a](https://github.com/Annuity-Management/assethub-backend/commit/bb4d57a64c667b005ac2f6d606629f241e40922c))
* **AH-724:** brevo contact update ([#465](https://github.com/Annuity-Management/assethub-backend/issues/465)) ([862de89](https://github.com/Annuity-Management/assethub-backend/commit/862de899a32d5dbaca0c948539b26082f687d604))
* **AH-737:** feature flags ([#480](https://github.com/Annuity-Management/assethub-backend/issues/480)) ([16bcc6b](https://github.com/Annuity-Management/assethub-backend/commit/16bcc6b275f17a674efc1f29badf0d1765353452))
* **AH-739:** fix prices calculation ([#484](https://github.com/Annuity-Management/assethub-backend/issues/484)) ([065673c](https://github.com/Annuity-Management/assethub-backend/commit/065673c06b0a1f95b2c93c0d65481a3131c6f137))
* **AH-740:** update product repository mapping to model ([#494](https://github.com/Annuity-Management/assethub-backend/issues/494)) ([ead8ba0](https://github.com/Annuity-Management/assethub-backend/commit/ead8ba0d2892aa935f2c895bc94281ef1ee7cb56))
* **AH-755:** add created_at field in assets enrichments table ([#477](https://github.com/Annuity-Management/assethub-backend/issues/477)) ([c703ac3](https://github.com/Annuity-Management/assethub-backend/commit/c703ac331c1a107b6ede1a7e5026c403763908dc))
* **AH-757:** new seeder ([#510](https://github.com/Annuity-Management/assethub-backend/issues/510)) ([e799254](https://github.com/Annuity-Management/assethub-backend/commit/e799254fd5983530fa98c9ca9cec3394ddc9ad4c))
* **AH-772:** add grouped contract download feature flag ([#511](https://github.com/Annuity-Management/assethub-backend/issues/511)) ([f14706e](https://github.com/Annuity-Management/assethub-backend/commit/f14706ee72769f9d6c7097e94387802e1b70042e))
* **AH-XXX:** fix asset details ([#471](https://github.com/Annuity-Management/assethub-backend/issues/471)) ([a94592c](https://github.com/Annuity-Management/assethub-backend/commit/a94592c746b663507ca0ee80fd13e362300f6dbd))
* Assign Quote Request ticket in Freshdesk ([#493](https://github.com/Annuity-Management/assethub-backend/issues/493)) ([e022053](https://github.com/Annuity-Management/assethub-backend/commit/e022053e21d09764a216df4aa3dace91eb58f66e))
* Italian Translations ([#478](https://github.com/Annuity-Management/assethub-backend/issues/478)) ([99f9e9b](https://github.com/Annuity-Management/assethub-backend/commit/99f9e9bb5c5beff2a186235b3ba0b4452da5781d))


### Reverts

* Revert "feat(AH-718): update global search (#504)" (#505) ([f3dc2f8](https://github.com/Annuity-Management/assethub-backend/commit/f3dc2f8134258b55e3f5dbe27d744582abbdbe03)), closes [#504](https://github.com/Annuity-Management/assethub-backend/issues/504) [#505](https://github.com/Annuity-Management/assethub-backend/issues/505)

## [1.38.2](https://github.com/Annuity-Management/assethub-backend/compare/v1.38.1...v1.38.2) (2025-07-04)


### Bug Fixes

* logging ([#461](https://github.com/Annuity-Management/assethub-backend/issues/461)) ([0f71e58](https://github.com/Annuity-Management/assethub-backend/commit/0f71e58c9a657310a31de8b185a1db3af6ce8c31))

## [1.38.1](https://github.com/Annuity-Management/assethub-backend/compare/v1.38.0...v1.38.1) (2025-07-01)


### Bug Fixes

* normalize emails on auth use cases ([#459](https://github.com/Annuity-Management/assethub-backend/issues/459)) ([b742ae2](https://github.com/Annuity-Management/assethub-backend/commit/b742ae281a56893d88ec5b0cc27953e89fb0618a))

# [1.38.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.37.0...v1.38.0) (2025-06-30)


### Bug Fixes

* adjustments ([#454](https://github.com/Annuity-Management/assethub-backend/issues/454)) ([b1a5084](https://github.com/Annuity-Management/assethub-backend/commit/b1a50845bd46349d8e31c046029e542b7c1defd4))
* **AH-683:** fix contacts schema ([#453](https://github.com/Annuity-Management/assethub-backend/issues/453)) ([9382b6b](https://github.com/Annuity-Management/assethub-backend/commit/9382b6b48e0c5fd35082449cd1825a39f7cd4082))
* **AH-688:** fix trigger ([#448](https://github.com/Annuity-Management/assethub-backend/issues/448)) ([12f37b2](https://github.com/Annuity-Management/assethub-backend/commit/12f37b20ccd3b748c41d815f95ab53c7748c91cf))
* **AH-688:** fix ui status generation ([#438](https://github.com/Annuity-Management/assethub-backend/issues/438)) ([cbb4a2d](https://github.com/Annuity-Management/assethub-backend/commit/cbb4a2df8193fe085c88a87fb249af83dcc12643))
* **AH-713:** fix migrated user reset password ([#447](https://github.com/Annuity-Management/assethub-backend/issues/447)) ([088898d](https://github.com/Annuity-Management/assethub-backend/commit/088898d88b712054b521aa9a0a532f6ea572730d))
* **AH-714:** freshdesk reseller price ([#444](https://github.com/Annuity-Management/assethub-backend/issues/444)) ([cd8b81a](https://github.com/Annuity-Management/assethub-backend/commit/cd8b81ac7c0e250ebedddc7ecd3961e2fd1a5439))
* **AH-714:** quote approve email price ([#439](https://github.com/Annuity-Management/assethub-backend/issues/439)) ([c67ee7a](https://github.com/Annuity-Management/assethub-backend/commit/c67ee7a659bc44519f8d0dc7f1beb96cea684fc9))
* **AH-717:** resend invite and invite link fix ([#450](https://github.com/Annuity-Management/assethub-backend/issues/450)) ([5174f81](https://github.com/Annuity-Management/assethub-backend/commit/5174f81f31432c22bf3c45b9021508dfadde854e))
* **AH-XXX:** default user locale from invite ([#455](https://github.com/Annuity-Management/assethub-backend/issues/455)) ([b3a87f4](https://github.com/Annuity-Management/assethub-backend/commit/b3a87f4395d95cc8e3052fda996674129f4b5950))
* **AH-XXX:** register password hide ([#451](https://github.com/Annuity-Management/assethub-backend/issues/451)) ([43df1d9](https://github.com/Annuity-Management/assethub-backend/commit/43df1d95a6055316b4ed37fbee82052ebe0945e9))
* Exception on attempt to Decline Quote/ Request Change with Cancellation reason ([#452](https://github.com/Annuity-Management/assethub-backend/issues/452)) ([0fa9774](https://github.com/Annuity-Management/assethub-backend/commit/0fa97741bc61b7096394b2aa437a4d4a68f067b0))
* Expiration of invite Link after 24h ([#429](https://github.com/Annuity-Management/assethub-backend/issues/429)) ([03e4f3d](https://github.com/Annuity-Management/assethub-backend/commit/03e4f3d06046e66468bbeb399835c89a46d16022))
* local seeder import ([#427](https://github.com/Annuity-Management/assethub-backend/issues/427)) ([4867b4d](https://github.com/Annuity-Management/assethub-backend/commit/4867b4ddfceea6c068e84391a3761b301890f13a))


### Features

* **AH-449:** fix system crash on refresh with invalid token ([#431](https://github.com/Annuity-Management/assethub-backend/issues/431)) ([4c90c76](https://github.com/Annuity-Management/assethub-backend/commit/4c90c7689591b7319bb8b8a8065a522ed40ae689))
* **AH-476:** fix tests ([#434](https://github.com/Annuity-Management/assethub-backend/issues/434)) ([5a79cb0](https://github.com/Annuity-Management/assethub-backend/commit/5a79cb0e2a478dc5159ac0343fd687df819b371f))
* **AH-478:** backend error messages localization ([#424](https://github.com/Annuity-Management/assethub-backend/issues/424)) ([286cf2b](https://github.com/Annuity-Management/assethub-backend/commit/286cf2ba6f73952d8064817d28680e084aed7b01))
* **AH-478:** fix dockerfile ([#425](https://github.com/Annuity-Management/assethub-backend/issues/425)) ([04a5159](https://github.com/Annuity-Management/assethub-backend/commit/04a51595df0a879a87ea90aca5b4956f24468156))
* **AH-554:** implement user enabling endpoint ([#414](https://github.com/Annuity-Management/assethub-backend/issues/414)) ([df8da5f](https://github.com/Annuity-Management/assethub-backend/commit/df8da5f39356c98bee2489bac7e7a9df43f011d9))
* **AH-555:** update invited user removal logic ([#449](https://github.com/Annuity-Management/assethub-backend/issues/449)) ([a22baea](https://github.com/Annuity-Management/assethub-backend/commit/a22baea7f35f03a4529b1886ba21c4c69cf256ad))
* **AH-688:** fill missing ui status ([#440](https://github.com/Annuity-Management/assethub-backend/issues/440)) ([9cf09e1](https://github.com/Annuity-Management/assethub-backend/commit/9cf09e180d60583d2d8f1c7c12c06c57d4b190ca))
* **AH-688:** make triggers to be case insensitive ([#441](https://github.com/Annuity-Management/assethub-backend/issues/441)) ([291dfc2](https://github.com/Annuity-Management/assethub-backend/commit/291dfc206103a1dc1050153255ddbaf5a519979b))
* **AH-700:** ignore advanced permissions per organization user ([#443](https://github.com/Annuity-Management/assethub-backend/issues/443)) ([62a0f98](https://github.com/Annuity-Management/assethub-backend/commit/62a0f98eb9055a60f2ba8599b5b1d429ac4e359f))
* **AH-710:** exclude invalid contracts from global search ([#435](https://github.com/Annuity-Management/assethub-backend/issues/435)) ([b1c9276](https://github.com/Annuity-Management/assethub-backend/commit/b1c927645917071ccc7e92acb118d667bd5ac723))
* **AH-XXX:** fix localization files ([#446](https://github.com/Annuity-Management/assethub-backend/issues/446)) ([dd1501b](https://github.com/Annuity-Management/assethub-backend/commit/dd1501b4f4a80ac595f67fa5cab989f075055a10))
* disable dev endpoints for all envs except local ([#426](https://github.com/Annuity-Management/assethub-backend/issues/426)) ([1fbe226](https://github.com/Annuity-Management/assethub-backend/commit/1fbe22602cd034c6c4cd50756d374c9fd26f4b72))
* Dropdown for Cancellation/Lost reason ([#432](https://github.com/Annuity-Management/assethub-backend/issues/432)) ([20f1051](https://github.com/Annuity-Management/assethub-backend/commit/20f1051a08e728777873cb04b52534b502994c29))


### Reverts

* Revert "Revert "fix: Expiration of invite Link after 24h"" (#437) ([eff71b7](https://github.com/Annuity-Management/assethub-backend/commit/eff71b7a65a01d044dba96e00b0d322183b5207a)), closes [#437](https://github.com/Annuity-Management/assethub-backend/issues/437) [#429](https://github.com/Annuity-Management/assethub-backend/issues/429) [#433](https://github.com/Annuity-Management/assethub-backend/issues/433)
* Revert "fix: Expiration of invite Link after 24h (#429)" (#433) ([95f9f36](https://github.com/Annuity-Management/assethub-backend/commit/95f9f366a9b917154e67f8c439018907744479b8)), closes [#429](https://github.com/Annuity-Management/assethub-backend/issues/429) [#433](https://github.com/Annuity-Management/assethub-backend/issues/433)

# [1.37.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.36.5...v1.37.0) (2025-06-18)


### Bug Fixes

* **AH-666:** sales message formating ([#415](https://github.com/Annuity-Management/assethub-backend/issues/415)) ([4022102](https://github.com/Annuity-Management/assethub-backend/commit/40221029622cf1b462e481cc44781c23dfb8e020))
* **AH-670:** missing org module and service provider ([dd87faf](https://github.com/Annuity-Management/assethub-backend/commit/dd87faf2e4f899cf1247d9c14007d7f80ffc5ebb))
* **AH-671:** format reseller price and don't send assets on quote request ([#417](https://github.com/Annuity-Management/assethub-backend/issues/417)) ([cfb63de](https://github.com/Annuity-Management/assethub-backend/commit/cfb63dee226e43bbca11a069ce7be27edc9feb75))
* **AH-671:** reseller price not shown ([#416](https://github.com/Annuity-Management/assethub-backend/issues/416)) ([4c99070](https://github.com/Annuity-Management/assethub-backend/commit/4c990705fffb3b04f7d7a604345656c16fa2627b))
* **AH-682:** fix fetching assets by contract no ([#413](https://github.com/Annuity-Management/assethub-backend/issues/413)) ([0c09fc8](https://github.com/Annuity-Management/assethub-backend/commit/0c09fc871f518b143ef067a5e7f5570709e867a0))
* **AH-XXX:** adv perm query ([23b74c5](https://github.com/Annuity-Management/assethub-backend/commit/23b74c5b1757f3503e25b6acd4b9bcd24982895c))
* **AH-XXX:** allow same entity link ([c7894a6](https://github.com/Annuity-Management/assethub-backend/commit/c7894a6733cf3a9ecf17ee74917dfa8e78fea9ef))
* request quote for contract ([#419](https://github.com/Annuity-Management/assethub-backend/issues/419)) ([207f727](https://github.com/Annuity-Management/assethub-backend/commit/207f72774a31f47c2728756357229160d3296930))
* Translation on Decline quote ([#404](https://github.com/Annuity-Management/assethub-backend/issues/404)) ([561625b](https://github.com/Annuity-Management/assethub-backend/commit/561625b224861c0629aec12af6ff34aebd778002))


### Features

* add observability config ([#422](https://github.com/Annuity-Management/assethub-backend/issues/422)) ([c9bf19a](https://github.com/Annuity-Management/assethub-backend/commit/c9bf19ac78587f417f579fde73c5b0df335e6bfb))
* **AH-266:** implement endpoint for gettingasset warranty information ([56aff92](https://github.com/Annuity-Management/assethub-backend/commit/56aff92ee234fb32dba48a846d96364955e01194))
* **AH-266:** improvements ([afca9dc](https://github.com/Annuity-Management/assethub-backend/commit/afca9dc176e902d00640c9c762ce810ce5635a47))
* **AH-550:** add trim for user names update dto ([#411](https://github.com/Annuity-Management/assethub-backend/issues/411)) ([f433db6](https://github.com/Annuity-Management/assethub-backend/commit/f433db6dc2f3d0d8d9ba0f70121abc515900fe9f))
* **AH-550:** make first and last names required for user update dto ([903f817](https://github.com/Annuity-Management/assethub-backend/commit/903f8172ce4ea3543a02ad0e6a2d5bc4a34bcbee))
* **AH-550:** style fix ([146f4c5](https://github.com/Annuity-Management/assethub-backend/commit/146f4c5b9a9fd15694bd3f51a387c350a2900080))
* **AH-551:** implement end user search for quotes and contracts list endpoints ([#410](https://github.com/Annuity-Management/assethub-backend/issues/410)) ([7d06a7a](https://github.com/Annuity-Management/assethub-backend/commit/7d06a7a5b8d862af40c4b5ade1b71604cf55a4f9))
* **AH-566:** implement sorting by quantity for assets endpoint ([5a67010](https://github.com/Annuity-Management/assethub-backend/commit/5a670100d7e1f83572cd70fc420a4294fea3f38e))
* **AH-644:** add quote updatedAt trigger ([#412](https://github.com/Annuity-Management/assethub-backend/issues/412)) ([50c364b](https://github.com/Annuity-Management/assethub-backend/commit/50c364b3e4007d48fde28dd70e7068afe146ce72))
* **AH-662:** add search of quotes by quote items serial number ([0cd3b53](https://github.com/Annuity-Management/assethub-backend/commit/0cd3b53b751c9723e994d7f01c5613cdf7c5a0fa))
* **AH-665:** extend customer entity sorting ([85af9b3](https://github.com/Annuity-Management/assethub-backend/commit/85af9b32ff2a83a2268af122c09099fa26848cd4))
* **AH-665:** fix lint ([1073207](https://github.com/Annuity-Management/assethub-backend/commit/10732074207416047d66851aadc718b138d7c2fb))
* **AH-666:** sales message ([69b0d27](https://github.com/Annuity-Management/assethub-backend/commit/69b0d27d24bcfee027eea9652107e38994dd24fe))
* **AH-668:** fix password reset link ([7869d39](https://github.com/Annuity-Management/assethub-backend/commit/7869d3974f1837941c24ba2f0413c32e8dd93053))
* **AH-670:** new quote request confirmation email ([18b2bd2](https://github.com/Annuity-Management/assethub-backend/commit/18b2bd20b80cde9d4f0c90921867b195351550eb))
* **AH-671:** reseller price of quote in email ([93627a4](https://github.com/Annuity-Management/assethub-backend/commit/93627a4c4b97a7e275da77785a610abab1db09f4))
* **AH-678:** add tests. add default mocks. updated readme. enable te… ([#420](https://github.com/Annuity-Management/assethub-backend/issues/420)) ([8e1a291](https://github.com/Annuity-Management/assethub-backend/commit/8e1a2914ff0310c7b98cb94cd6df0b393d7b8b93))
* Translation improvements ([#418](https://github.com/Annuity-Management/assethub-backend/issues/418)) ([8ef2112](https://github.com/Annuity-Management/assethub-backend/commit/8ef2112612c909a99370eaaff47e816cfb985d24))

## [1.36.5](https://github.com/Annuity-Management/assethub-backend/compare/v1.36.4...v1.36.5) (2025-06-04)


### Bug Fixes

* migrations ([f6e27bc](https://github.com/Annuity-Management/assethub-backend/commit/f6e27bc06818defbcef605436f904467484f65a2))

## [1.36.4](https://github.com/Annuity-Management/assethub-backend/compare/v1.36.3...v1.36.4) (2025-06-04)


### Bug Fixes

* migrations ([bb1ef9c](https://github.com/Annuity-Management/assethub-backend/commit/bb1ef9c903d37c1f7790dada1501c52191d2acca))

## [1.36.3](https://github.com/Annuity-Management/assethub-backend/compare/v1.36.2...v1.36.3) (2025-06-04)


### Bug Fixes

* remove listener staging guards ([7a84ea9](https://github.com/Annuity-Management/assethub-backend/commit/7a84ea92b4ae61b22246218c947469240dda9aa2))

## [1.36.2](https://github.com/Annuity-Management/assethub-backend/compare/v1.36.1...v1.36.2) (2025-06-04)


### Bug Fixes

* connection establishment on staging ([d02a6e8](https://github.com/Annuity-Management/assethub-backend/commit/d02a6e8ece707df9aa94ad73eaa7cde334bb77cb))

## [1.36.1](https://github.com/Annuity-Management/assethub-backend/compare/v1.36.0...v1.36.1) (2025-06-04)


### Bug Fixes

* **AH-560:** contract assets sorting ([1f6fda9](https://github.com/Annuity-Management/assethub-backend/commit/1f6fda9b5a7445ac9712778fdc61029b6cd19441))

# [1.36.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.35.0...v1.36.0) (2025-06-04)


### Features

* **AH-654:** lint ([24237c6](https://github.com/Annuity-Management/assethub-backend/commit/24237c684d6787b6d5a6a72e449b7badb23182ad))
* **AH-654:** transform keycloak user validation error to nestjs exception ([c175a2a](https://github.com/Annuity-Management/assethub-backend/commit/c175a2a5b3ce2b363b051b12a697277273d5ffe5))

# [1.35.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.34.1...v1.35.0) (2025-06-04)


### Features

* **AH-605:** org locale ([44749ed](https://github.com/Annuity-Management/assethub-backend/commit/44749edbd570ef0f4ede49e8516a410126e36cda))

## [1.34.1](https://github.com/Annuity-Management/assethub-backend/compare/v1.34.0...v1.34.1) (2025-06-04)


### Bug Fixes

* quote emails ([#380](https://github.com/Annuity-Management/assethub-backend/issues/380)) ([0630641](https://github.com/Annuity-Management/assethub-backend/commit/0630641e618809aea4321bde3c7a7c7485b1c4c5))

# [1.34.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.33.0...v1.34.0) (2025-06-04)


### Features

* **AH-609:** update organization presenter ([5b319e9](https://github.com/Annuity-Management/assethub-backend/commit/5b319e97c08e51194654d241c23cedf5c947600c))

# [1.33.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.32.2...v1.33.0) (2025-06-04)


### Features

* **AH-653:** improve advanced permissions ([79d15e9](https://github.com/Annuity-Management/assethub-backend/commit/79d15e9072b12d848d854fc22f328fa2d2e90ed1))

## [1.32.2](https://github.com/Annuity-Management/assethub-backend/compare/v1.32.1...v1.32.2) (2025-06-04)


### Bug Fixes

* **AH-607:** make german defaul email locale ([702bc2c](https://github.com/Annuity-Management/assethub-backend/commit/702bc2ca2584fab190bc2069a40a40162b8c4b41))

## [1.32.1](https://github.com/Annuity-Management/assethub-backend/compare/v1.32.0...v1.32.1) (2025-06-03)


### Bug Fixes

* Improvements on contract request quote freshdesk ticket ([00b2458](https://github.com/Annuity-Management/assethub-backend/commit/00b2458ca7b0b92036fc436fbc11db428b02aba6))

# [1.32.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.31.0...v1.32.0) (2025-06-03)


### Features

* **AH-651:** Improve PDF download ([1cf9c21](https://github.com/Annuity-Management/assethub-backend/commit/1cf9c216bb82e8c13a2f201bd820775fe39b530c))

# [1.31.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.30.0...v1.31.0) (2025-06-03)


### Bug Fixes

* **AH-557:** limit global search to 1 asset ([05229aa](https://github.com/Annuity-Management/assethub-backend/commit/05229aab91281414e490d4215b1c78568545fb6d))


### Features

* **AH-593, AH-592:** fix sql query ([587e3ff](https://github.com/Annuity-Management/assethub-backend/commit/587e3ff569fe92558b6b83efb6b198d86c360d35))

# [1.30.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.29.1...v1.30.0) (2025-06-03)


### Features

* **AH-605:** multilang emails ([8ee4f39](https://github.com/Annuity-Management/assethub-backend/commit/8ee4f393dc8a623d7881cc34544f04f6716971c1))

## [1.29.1](https://github.com/Annuity-Management/assethub-backend/compare/v1.29.0...v1.29.1) (2025-06-03)


### Bug Fixes

* Round prices on  Assets table download ([4d5a8bb](https://github.com/Annuity-Management/assethub-backend/commit/4d5a8bb2511559e91bbb89534c27068d46aba5fb))

# [1.29.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.28.0...v1.29.0) (2025-06-03)


### Features

* **AH-640:** fix status enum ([9d1da7a](https://github.com/Annuity-Management/assethub-backend/commit/9d1da7a57178f0a14c9adac40534e270e9286ddb))

# [1.28.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.27.0...v1.28.0) (2025-06-03)


### Features

*  implementing Request Quote for Contract ([f1cd653](https://github.com/Annuity-Management/assethub-backend/commit/f1cd653d9b139f66e31e581e296a909a9b7bdacd))

# [1.27.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.26.0...v1.27.0) (2025-06-03)


### Features

* **AH-640:** fix status enum ([c35e616](https://github.com/Annuity-Management/assethub-backend/commit/c35e61621f22e926d9d67f6cc81027b0caaa9915))

# [1.26.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.25.0...v1.26.0) (2025-06-02)


### Features

* **AH-640:** fix ([5155033](https://github.com/Annuity-Management/assethub-backend/commit/5155033f2217b57b2963766cf4fb50073b38530f))

# [1.25.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.24.0...v1.25.0) (2025-06-02)


### Features

* **AH-640:** fix queries ([9b29347](https://github.com/Annuity-Management/assethub-backend/commit/9b293474a1c01d1ee17b13703be660b7f397a78a))

# [1.24.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.23.0...v1.24.0) (2025-06-02)


### Features

* **AH-640:** hide invalid quotes ([c0178ce](https://github.com/Annuity-Management/assethub-backend/commit/c0178ceab638518a5db0255826fbbe70bb302745))

# [1.23.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.22.0...v1.23.0) (2025-05-29)


### Bug Fixes

* fix sorting ([c6699b0](https://github.com/Annuity-Management/assethub-backend/commit/c6699b09f3d2764b1f3ffd9571ffc0b1ce3958c9))


### Features

* **AH-639:** add advanced permissioning to KPIs endpoint ([ccd5358](https://github.com/Annuity-Management/assethub-backend/commit/ccd535827eeface57786a9dc83c4633ba8c9e133))

# [1.22.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.21.0...v1.22.0) (2025-05-29)


### Features

* **AH-609:** advanced permissions dtos ([e227e05](https://github.com/Annuity-Management/assethub-backend/commit/e227e05f18491da8b7c8c465d0f9d1bdae438238))

# [1.21.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.20.0...v1.21.0) (2025-05-29)


### Features

* **AH-608:** add advanced permissioning flag to organization management endpoints ([34eac1c](https://github.com/Annuity-Management/assethub-backend/commit/34eac1c072de989360887992486d13c8bd4f37ec))
* **AH-629:** optimize assets by entity endpoint ([542a2d2](https://github.com/Annuity-Management/assethub-backend/commit/542a2d26597cf15fdc0807e61dc5425c75a8907b))

# [1.20.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.19.1...v1.20.0) (2025-05-28)


### Features

* **AH-593, AH-617:** advanced permissioning for entity assets and entity assets export ([552a88f](https://github.com/Annuity-Management/assethub-backend/commit/552a88feb3cfb5a5c0cd81d216e30b1c9eed5a53))
* **AH-593, AH-617:** advanced permissioning for entity contracts ([00828b4](https://github.com/Annuity-Management/assethub-backend/commit/00828b4c72d52310c029bee221b3a80dd7f558b1))
* **AH-593, AH-617:** advanced permissioning for entity quotes ([808575a](https://github.com/Annuity-Management/assethub-backend/commit/808575a785ce5fb107a5422210ba70b8950b1a47))
* **AH-593, AH-618:** advanced permissioning for asset contracts ([58110d1](https://github.com/Annuity-Management/assethub-backend/commit/58110d16462a748add7aa38d24298cba74a9c30b))
* **AH-593, AH-618:** advanced permissioning for asset quotes ([d588bd5](https://github.com/Annuity-Management/assethub-backend/commit/d588bd5d0ffc88a505e5fb04493b8c70098174a1))

## [1.19.1](https://github.com/Annuity-Management/assethub-backend/compare/v1.19.0...v1.19.1) (2025-05-28)

# [1.19.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.18.0...v1.19.0) (2025-05-28)


### Features

* **AH-593, AH-615:** advanced permissioning for quote assets and quote assets export ([96d7cf3](https://github.com/Annuity-Management/assethub-backend/commit/96d7cf37482f35305f1e6a914da18bd2626b3bb1))
* **AH-593, AH-615:** advanced permissioning for quote contracts ([d0c8af5](https://github.com/Annuity-Management/assethub-backend/commit/d0c8af5fb74580f21e62afea324e260d1731b4df))
* **AH-593, AH-615:** advanced permissioning for quote pdf ([9ba09f5](https://github.com/Annuity-Management/assethub-backend/commit/9ba09f536a58f8a0474b1ec2b06dce8b2bf91b57))
* **AH-593, AH-615:** advanced permissioning for quote request history ([94e656d](https://github.com/Annuity-Management/assethub-backend/commit/94e656d35568c296bad1f3de5027b25203d1e45a))
* **AH-593, AH-616:** advanced permissioning for contract quotes ([3164c75](https://github.com/Annuity-Management/assethub-backend/commit/3164c7500a0253cf8f77ae9a08982f24b7d9fe1e))
* **AH-593, AH-616:** advanced permissioning for quote assets, quote assets export and quote parties ([a233ba4](https://github.com/Annuity-Management/assethub-backend/commit/a233ba45007b2fb8922c69a7024c1e174e9f3f05))
* **AH-593, AH-616:** remove unused endpoint ([61ab7de](https://github.com/Annuity-Management/assethub-backend/commit/61ab7de053bae9c7fbefc607a2d3379bbdb280ff))

# [1.18.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.17.0...v1.18.0) (2025-05-27)


### Features

* **AH-558:** optimize quotes/contracts by asset select ([ccf788f](https://github.com/Annuity-Management/assethub-backend/commit/ccf788f1f3f997144ba1ba4ac42e76b1b32e452b))

# [1.17.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.16.0...v1.17.0) (2025-05-26)


### Features

* **AH-592:** multiple entities link to org ([187ef7f](https://github.com/Annuity-Management/assethub-backend/commit/187ef7f1410d582acf8b403fe46d19b9a993a14d))

# [1.16.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.15.4...v1.16.0) (2025-05-26)


### Bug Fixes

* **AH-XXX:** make quote_id in quote_request_histories nullable and store quote request data ([1d53458](https://github.com/Annuity-Management/assethub-backend/commit/1d534584016174368bf9cdffda1f01d31b42edfa))


### Features

* **AH-604:** extend asset details response ([3cb0a2e](https://github.com/Annuity-Management/assethub-backend/commit/3cb0a2e0025f97b6f7a8304d59ebfc47324beff3))

## [1.15.4](https://github.com/Annuity-Management/assethub-backend/compare/v1.15.3...v1.15.4) (2025-05-23)


### Reverts

* Revert "Revert "chore: update ESLint rules and refactor error handling (#326)"" ([a74f44d](https://github.com/Annuity-Management/assethub-backend/commit/a74f44d9f9dc13bd151c1df40977ed8175eaa7fe)), closes [#326](https://github.com/Annuity-Management/assethub-backend/issues/326)
* Revert "Revert "feat(AH-593): advanced permissioning for quote, contract, asset and contact details pages"" ([bfa7f64](https://github.com/Annuity-Management/assethub-backend/commit/bfa7f64cc55b4cdb0f531458eafef05002156880))

## [1.15.3](https://github.com/Annuity-Management/assethub-backend/compare/v1.15.2...v1.15.3) (2025-05-23)


### Bug Fixes

* restore missing endpoint ([46afc9b](https://github.com/Annuity-Management/assethub-backend/commit/46afc9b6dd330f49aecac2454fafa0edf41801f1))

## [1.15.2](https://github.com/Annuity-Management/assethub-backend/compare/v1.15.1...v1.15.2) (2025-05-23)


### Reverts

* Revert "chore: update ESLint rules and refactor error handling (#326)" ([43ed903](https://github.com/Annuity-Management/assethub-backend/commit/43ed903c968377c797226560c20d56fc83250bc8)), closes [#326](https://github.com/Annuity-Management/assethub-backend/issues/326)

## [1.15.1](https://github.com/Annuity-Management/assethub-backend/compare/v1.15.0...v1.15.1) (2025-05-23)

# [1.15.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.14.2...v1.15.0) (2025-05-23)


### Features

* **AH-593, AH-610, AH-615:** advanced permissioning for quote details and quote parties ([c201ef6](https://github.com/Annuity-Management/assethub-backend/commit/c201ef6905f09faf1a90b5afeb45d98553dd70ff))
* **AH-593, AH-611, AH-616:** advanced permissioning for contract details and contract pdf ([967613a](https://github.com/Annuity-Management/assethub-backend/commit/967613a5a0b29f5ae84092136ec49bc0ed147694))
* **AH-593, AH-612:** advanced permissioning for asset details ([5e0ddb5](https://github.com/Annuity-Management/assethub-backend/commit/5e0ddb50a951356d5c177b508d072210343bc038))
* **AH-593, AH-613:** advanced permissioning for contact details ([a01aa91](https://github.com/Annuity-Management/assethub-backend/commit/a01aa9111dff5501874fd07387e407e3d480d9df))

## [1.14.2](https://github.com/Annuity-Management/assethub-backend/compare/v1.14.1...v1.14.2) (2025-05-22)


### Bug Fixes

* Round Prices in excel export to 2 digits ([8a76c98](https://github.com/Annuity-Management/assethub-backend/commit/8a76c98fe3509f46a21489a770057c00e51fcc2e))

## [1.14.1](https://github.com/Annuity-Management/assethub-backend/compare/v1.14.0...v1.14.1) (2025-05-22)

# [1.14.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.13.1...v1.14.0) (2025-05-22)


### Bug Fixes

* **AH-546:** fix ([fe5c96c](https://github.com/Annuity-Management/assethub-backend/commit/fe5c96c83f7b77568ea8de7593e3ba19b4ea24b5))


### Features

* **AH-593:** implement advanced permissioning to customers list and to customers global search ([3017e3b](https://github.com/Annuity-Management/assethub-backend/commit/3017e3b3ef520ca46189c9e2b3b2ea192fc6bab7))

## [1.13.1](https://github.com/Annuity-Management/assethub-backend/compare/v1.13.0...v1.13.1) (2025-05-21)

# [1.13.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.12.3...v1.13.0) (2025-05-21)


### Bug Fixes

* **AH-546:** fix lock file ([af8d464](https://github.com/Annuity-Management/assethub-backend/commit/af8d464f62fd634249f1273ed4bbfe6bd90bec07))


### Features

* **AH-546:** implement brevo contacts creation ([c2c560e](https://github.com/Annuity-Management/assethub-backend/commit/c2c560e5b8d9330ea90b20daeb388f820adc49d2))

## [1.12.3](https://github.com/Annuity-Management/assethub-backend/compare/v1.12.2...v1.12.3) (2025-05-21)


### Bug Fixes

* **AH-603:** missing role permission ([5a8b50d](https://github.com/Annuity-Management/assethub-backend/commit/5a8b50d68866b97aad5df86eb44e17944fe37c15))

## [1.12.2](https://github.com/Annuity-Management/assethub-backend/compare/v1.12.1...v1.12.2) (2025-05-20)


### Bug Fixes

* **AH-584:** assets by contracts sorting ([b219dae](https://github.com/Annuity-Management/assethub-backend/commit/b219daeb2d7360e150bb15957d7a9e7d07b08134))

## [1.12.1](https://github.com/Annuity-Management/assethub-backend/compare/v1.12.0...v1.12.1) (2025-05-20)


### Bug Fixes

* **AH-591:** request quote ([e695bbf](https://github.com/Annuity-Management/assethub-backend/commit/e695bbfb9d8625e42f2d783dcac9bb188130bf6b))

# [1.12.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.11.0...v1.12.0) (2025-05-20)


### Features

* **AH-593:** fix ([4bfaf73](https://github.com/Annuity-Management/assethub-backend/commit/4bfaf7391aad06bc5915700bb0f1cbe66283e3a7))
* **AH-593:** implement advanced permissioning to quotes list and to quotes global search ([f67cfa8](https://github.com/Annuity-Management/assethub-backend/commit/f67cfa872c5ad6153908681e0e2011e4a3c89058))

# [1.11.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.10.13...v1.11.0) (2025-05-20)


### Bug Fixes

* **AH-593:** fix name ([7a72864](https://github.com/Annuity-Management/assethub-backend/commit/7a728641ece5556210900f0e66f6b935671a8c2c))


### Features

* **AH-593:** add adv_perms column to organization ([e4be307](https://github.com/Annuity-Management/assethub-backend/commit/e4be30776d52748478ec1153edcb5412048ba341))
* **AH-593:** add advanced permissioning to context ([e3a91ed](https://github.com/Annuity-Management/assethub-backend/commit/e3a91ed435101cfb12c4ff9a93aa89abb3885c60))
* **AH-593:** add lib for scheduling ([62703ec](https://github.com/Annuity-Management/assethub-backend/commit/62703ec8b7525967b35b4a82a2e1a1cef361a61e))
* **AH-593:** add view and missing indexes to migration ([f437e41](https://github.com/Annuity-Management/assethub-backend/commit/f437e410a814c1fb349dd7883b469a2f45729735))
* **AH-593:** implement advanced permissioning to contracts list and to contracts global search ([bbe14ca](https://github.com/Annuity-Management/assethub-backend/commit/bbe14ca39c7adcc0b5555bcacf8af44d0d5b41c2))
* **AH-593:** move class ([eed2c8a](https://github.com/Annuity-Management/assethub-backend/commit/eed2c8a3cba028f28201c9f7101f1330a8f0c5bf))
* **AH-593:** update seeder ([4e92ddb](https://github.com/Annuity-Management/assethub-backend/commit/4e92ddb44c1870866c4d431abfecbe2898bf5d93))

## [1.10.13](https://github.com/Annuity-Management/assethub-backend/compare/v1.10.12...v1.10.13) (2025-05-19)


### Bug Fixes

* **AH-548:** filtering on search by contract ([7bdc4cf](https://github.com/Annuity-Management/assethub-backend/commit/7bdc4cfd820c5121d7736173befdccbb63693119))

## [1.10.12](https://github.com/Annuity-Management/assethub-backend/compare/v1.10.11...v1.10.12) (2025-05-19)


### Bug Fixes

* **AH-591:** Request quote assets or file requirement ([b060d45](https://github.com/Annuity-Management/assethub-backend/commit/b060d45d335aadda7e256490753cff17d558ce8a))

## [1.10.11](https://github.com/Annuity-Management/assethub-backend/compare/v1.10.10...v1.10.11) (2025-05-19)


### Bug Fixes

* **AH-575:** block quotes and contracts details for non-owner reseller ([952e1b1](https://github.com/Annuity-Management/assethub-backend/commit/952e1b1cf7eb51657acccac6b8e9d2d3c69310b9))
* fix recepient email ([c1f71ff](https://github.com/Annuity-Management/assethub-backend/commit/c1f71ff7b2caf1e1726567e8b97c5d1188e28f40))

## [1.10.10](https://github.com/Annuity-Management/assethub-backend/compare/v1.10.9...v1.10.10) (2025-05-15)


### Bug Fixes

* update ui status ([84d0608](https://github.com/Annuity-Management/assethub-backend/commit/84d0608fb1d41d2f97540364e56e6db2e201231e))

## [1.10.9](https://github.com/Annuity-Management/assethub-backend/compare/v1.10.8...v1.10.9) (2025-05-15)


### Bug Fixes

* add logging to ([dd55320](https://github.com/Annuity-Management/assethub-backend/commit/dd5532023621da481e37fabfe9136b0791300323))

## [1.10.8](https://github.com/Annuity-Management/assethub-backend/compare/v1.10.7...v1.10.8) (2025-05-15)

## [1.10.7](https://github.com/Annuity-Management/assethub-backend/compare/v1.10.6...v1.10.7) (2025-05-15)

## [1.10.6](https://github.com/Annuity-Management/assethub-backend/compare/v1.10.5...v1.10.6) (2025-05-15)


### Bug Fixes

* user invitation ([b06d3fb](https://github.com/Annuity-Management/assethub-backend/commit/b06d3fb57f646acc9991e1c0a63e5f9ff76843c0))

## [1.10.5](https://github.com/Annuity-Management/assethub-backend/compare/v1.10.4...v1.10.5) (2025-05-15)


### Bug Fixes

* fix recepient email ([4437a27](https://github.com/Annuity-Management/assethub-backend/commit/4437a27d8456e6ccc3d3bb1d698baede67e575f8))
* invitation decoding ([07e625b](https://github.com/Annuity-Management/assethub-backend/commit/07e625bea5a69fa36110d348d731b8bcabaaa340))

## [1.10.4](https://github.com/Annuity-Management/assethub-backend/compare/v1.10.3...v1.10.4) (2025-05-15)


### Bug Fixes

* fix recepient email ([fc3e69a](https://github.com/Annuity-Management/assethub-backend/commit/fc3e69a4d570b8be54ce5a428285b6292923ff87))

## [1.10.3](https://github.com/Annuity-Management/assethub-backend/compare/v1.10.2...v1.10.3) (2025-05-15)


### Bug Fixes

* remove subjects from emails ([9558185](https://github.com/Annuity-Management/assethub-backend/commit/9558185e5548ef232b0068b5351e66dc352cca52))

## [1.10.2](https://github.com/Annuity-Management/assethub-backend/compare/v1.10.1...v1.10.2) (2025-05-15)


### Bug Fixes

* user invite ([2aa4d48](https://github.com/Annuity-Management/assethub-backend/commit/2aa4d484977a82059f6cb11309d330304835c822))

## [1.10.1](https://github.com/Annuity-Management/assethub-backend/compare/v1.10.0...v1.10.1) (2025-05-15)


### Bug Fixes

* **AH-573:** return iAsset quote/contract id request ([c9fda77](https://github.com/Annuity-Management/assethub-backend/commit/c9fda770acbc16939e7a5865a857711acceceda6))

# [1.10.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.9.0...v1.10.0) (2025-05-15)


### Bug Fixes

* **AH-573:** fix names ([a1fd476](https://github.com/Annuity-Management/assethub-backend/commit/a1fd476ebf30c30320270ac72387210967b1bb5e))
* **AH-573:** fix PDF download hanging by improving errors processing ([24bbd75](https://github.com/Annuity-Management/assethub-backend/commit/24bbd7524a142c1992dca033d1ac4a5471f8e823))
* **AH-573:** resolve merge conflicts ([2ff22b6](https://github.com/Annuity-Management/assethub-backend/commit/2ff22b6c99e13d31c8e12c5c70dd5cdb4a2258d0))


### Features

* **AH-573:** update schemas ([a9fd786](https://github.com/Annuity-Management/assethub-backend/commit/a9fd78623d85959c96f989054cada8518c8e5490))

# [1.9.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.8.1...v1.9.0) (2025-05-14)


### Bug Fixes

* **AH-543:** enable authentication guard for asset endpoints ([aac03dc](https://github.com/Annuity-Management/assethub-backend/commit/aac03dc55dd222b390dcd103ef666484eda6f598))


### Features

* **AH-543:** add permissions ([34d192f](https://github.com/Annuity-Management/assethub-backend/commit/34d192fc3ff6a333d00ee78b3927302fa18aa9a1))
* **AH-543:** fix asset serial number field ([a284736](https://github.com/Annuity-Management/assethub-backend/commit/a284736aa8161455beae3f7d2c88388cbcbd0192))
* **AH-543:** implement proper quotes and contracts grouping on asset page ([82a0764](https://github.com/Annuity-Management/assethub-backend/commit/82a0764f30a43b2fe973ec1e58f8fa2909581373))

## [1.8.1](https://github.com/Annuity-Management/assethub-backend/compare/v1.8.0...v1.8.1) (2025-05-14)


### Bug Fixes

* **AH-XXX:** filter invitation on users listing ([b1a925a](https://github.com/Annuity-Management/assethub-backend/commit/b1a925ab388bb5dc77f116901a913b93e4f39970))

# [1.8.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.7.2...v1.8.0) (2025-05-14)


### Bug Fixes

* **AH-XXX:** request quote change ([4a9eaec](https://github.com/Annuity-Management/assethub-backend/commit/4a9eaeca49b51063f04f363db5b35c751cd03ebe))


### Features

* **AH-531:** create indexes ([4de9455](https://github.com/Annuity-Management/assethub-backend/commit/4de945580786ea73526f769dba84bb074bc801ea))
* **AH-531:** improve contracts global search ([5c910e0](https://github.com/Annuity-Management/assethub-backend/commit/5c910e0b11f6a94bb7bf712c238f2bc200612cc7))
* **AH-531:** improve global search ([8e117c8](https://github.com/Annuity-Management/assethub-backend/commit/8e117c89d6e7a2873c3d856fa273779627dd3a43))
* **AH-531:** improve quotes global search ([17036a6](https://github.com/Annuity-Management/assethub-backend/commit/17036a63fc301ec5bc93b1de10dc905cd7eef737))
* **AH-531:** rename files and models to dtos ([b3bb7ed](https://github.com/Annuity-Management/assethub-backend/commit/b3bb7ed7613f695c4f429d6c2c436f15a0ea5646))
* **AH-531:** search with upper cased phrase ([a694999](https://github.com/Annuity-Management/assethub-backend/commit/a694999a7299215c743406face9632b9d869e1e8))

## [1.7.2](https://github.com/Annuity-Management/assethub-backend/compare/v1.7.1...v1.7.2) (2025-05-14)

## [1.7.1](https://github.com/Annuity-Management/assethub-backend/compare/v1.7.0...v1.7.1) (2025-05-14)


### Bug Fixes

* entity type ([898edee](https://github.com/Annuity-Management/assethub-backend/commit/898edee7f15cbb1e88822cb69af022a861d2965c))

# [1.7.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.6.4...v1.7.0) (2025-05-14)


### Features

* **AH-530:** Improve email notifications ([2efa8cf](https://github.com/Annuity-Management/assethub-backend/commit/2efa8cf3be89be5694a276fbf18b1961fcbf1e22))

## [1.6.4](https://github.com/Annuity-Management/assethub-backend/compare/v1.6.3...v1.6.4) (2025-05-13)


### Bug Fixes

* **AH-527:** fix sorting by end user ([8aec3e7](https://github.com/Annuity-Management/assethub-backend/commit/8aec3e724ad303aa2cc84ec34c5b0f13cae5a7ef))

## [1.6.3](https://github.com/Annuity-Management/assethub-backend/compare/v1.6.2...v1.6.3) (2025-05-13)


### Bug Fixes

* **AH-527:** fix sorting by end user ([bd9b41d](https://github.com/Annuity-Management/assethub-backend/commit/bd9b41d7d6361fb960c2bfd3204897ad26a03258))

## [1.6.2](https://github.com/Annuity-Management/assethub-backend/compare/v1.6.1...v1.6.2) (2025-05-13)


### Bug Fixes

* **AH-527:** fix sorting by end user ([eae8eb0](https://github.com/Annuity-Management/assethub-backend/commit/eae8eb0289e100c53a4e6ed28198337717f34d0c))

## [1.6.1](https://github.com/Annuity-Management/assethub-backend/compare/v1.6.0...v1.6.1) (2025-05-13)


### Bug Fixes

* add deployment retries ([d3b449c](https://github.com/Annuity-Management/assethub-backend/commit/d3b449cf0b704db137d09b365231f9520d8e2714))

# [1.6.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.5.1...v1.6.0) (2025-05-13)


### Bug Fixes

* deployment verification ([df011de](https://github.com/Annuity-Management/assethub-backend/commit/df011deb21a6a5cb443ffe832eeef09da08b60e6))


### Features

* **AH-527:** implement sorting by end user ([a22f758](https://github.com/Annuity-Management/assethub-backend/commit/a22f7585af68bf1e5d9eb91faae45a77ac300cfe))

## [1.5.1](https://github.com/Annuity-Management/assethub-backend/compare/v1.5.0...v1.5.1) (2025-05-13)


### Bug Fixes

* **AH-524:** fix bug ([1a0a7bf](https://github.com/Annuity-Management/assethub-backend/commit/1a0a7bf8e22bc280aba8a796174a01558e112c6d))

# [1.5.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.4.1...v1.5.0) (2025-05-13)


### Features

* **AH-524:** compact entity filter model ([c1d66d8](https://github.com/Annuity-Management/assethub-backend/commit/c1d66d86fa469eb10371dfce3c38709d5cb51838))
* **AH-524:** fix contracts filter schema ([ea024cf](https://github.com/Annuity-Management/assethub-backend/commit/ea024cfeadce11e3705e2075853f34b5e09ad8b7))
* **AH-524:** fix contracts filter schema ([d48f8e5](https://github.com/Annuity-Management/assethub-backend/commit/d48f8e5c9a383031fb0a46494bae4c3e99638a14))
* **AH-524:** fix quotes filter schema ([d6253a0](https://github.com/Annuity-Management/assethub-backend/commit/d6253a03e8a4e2dc56ec0979a767e8171187fb92))
* **AH-524:** implement assets global search ([3411e1e](https://github.com/Annuity-Management/assethub-backend/commit/3411e1e74db816fb2bf19b1d43dcc6de92dd3403))

## [1.4.1](https://github.com/Annuity-Management/assethub-backend/compare/v1.4.0...v1.4.1) (2025-05-13)


### Bug Fixes

* **AH-XXX:** assets by entity list ([c54f8a8](https://github.com/Annuity-Management/assethub-backend/commit/c54f8a8ce1f60990d801fb32d919a1d4913628f1))
* **AH-XXX:** lint errors ([8734c17](https://github.com/Annuity-Management/assethub-backend/commit/8734c17b47b3f805370886c8565ad6c836f4fc3e))

# [1.4.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.3.4...v1.4.0) (2025-05-13)


### Bug Fixes

* **AH-524:** fix line endings ([4ee7bce](https://github.com/Annuity-Management/assethub-backend/commit/4ee7bce260e2df118071e22bcc3052374a2fcdfc))


### Features

* **AH-524:** create missing indexes ([f1d30cc](https://github.com/Annuity-Management/assethub-backend/commit/f1d30cce9b2ac987eb2e0157f42faa034c183209))
* **AH-524:** implement global search for quotes, contracts and customers ([30d0fd4](https://github.com/Annuity-Management/assethub-backend/commit/30d0fd49c6d37f01330402f6dbeafce881e6c773))

## [1.3.4](https://github.com/Annuity-Management/assethub-backend/compare/v1.3.3...v1.3.4) (2025-05-13)


### Bug Fixes

* Filter adjustments ([8d4a4a6](https://github.com/Annuity-Management/assethub-backend/commit/8d4a4a6d8f2a61e11a0cdd6588fa63f1bb50d06f))
* updating translations keys ([92a457f](https://github.com/Annuity-Management/assethub-backend/commit/92a457f1341d3b14cee031a1b59c33788563a5cf))

## [1.3.3](https://github.com/Annuity-Management/assethub-backend/compare/v1.3.2...v1.3.3) (2025-05-12)


### Bug Fixes

* fix migrations ([06255d6](https://github.com/Annuity-Management/assethub-backend/commit/06255d6e792514e5fce26b3fd19f83a47e1162f2))

## [1.3.2](https://github.com/Annuity-Management/assethub-backend/compare/v1.3.1...v1.3.2) (2025-05-12)


### Bug Fixes

* fix migrations ([1bd05de](https://github.com/Annuity-Management/assethub-backend/commit/1bd05deef8d718e69f7c56d91896d84c1f26de71))

## [1.3.1](https://github.com/Annuity-Management/assethub-backend/compare/v1.3.0...v1.3.1) (2025-05-12)


### Bug Fixes

* fix migrations ([852c6fa](https://github.com/Annuity-Management/assethub-backend/commit/852c6fa5d17d7405e0e7ef75f74253919ba35505))

# [1.3.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.2.8...v1.3.0) (2025-05-12)


### Bug Fixes

* fix migrations ([954b091](https://github.com/Annuity-Management/assethub-backend/commit/954b0918f5272a0b5b693417b9f361adc72961ea))


### Features

* formatting ([1c182da](https://github.com/Annuity-Management/assethub-backend/commit/1c182daf0dcd46baa97bb9ed5baa338921496f99))

## [1.2.8](https://github.com/Annuity-Management/assethub-backend/compare/v1.2.7...v1.2.8) (2025-05-12)


### Bug Fixes

* remove subject from emails ([f9daefc](https://github.com/Annuity-Management/assethub-backend/commit/f9daefcb797aab60b1e7c42ce777571f249074ef))

## [1.2.7](https://github.com/Annuity-Management/assethub-backend/compare/v1.2.6...v1.2.7) (2025-05-12)

## [1.2.6](https://github.com/Annuity-Management/assethub-backend/compare/v1.2.5...v1.2.6) (2025-05-12)

## [1.2.5](https://github.com/Annuity-Management/assethub-backend/compare/v1.2.4...v1.2.5) (2025-05-12)

## [1.2.4](https://github.com/Annuity-Management/assethub-backend/compare/v1.2.3...v1.2.4) (2025-05-12)

## [1.2.3](https://github.com/Annuity-Management/assethub-backend/compare/v1.2.2...v1.2.3) (2025-05-12)


### Bug Fixes

* **AH-XXX:** asset read permission ([26b6b82](https://github.com/Annuity-Management/assethub-backend/commit/26b6b82da2eca75cac586b6ad4da529dcc421edf))

## [1.2.2](https://github.com/Annuity-Management/assethub-backend/compare/v1.2.1...v1.2.2) (2025-05-12)


### Bug Fixes

* add test console.log() ([e4f17bc](https://github.com/Annuity-Management/assethub-backend/commit/e4f17bc45ffb50f94dfff1339be1dfecd9824975))
* **AH-XXX:** asset read permission ([8eb4340](https://github.com/Annuity-Management/assethub-backend/commit/8eb4340da101d5e0305e6bda6bcf10b5cc88c074))

## [1.2.1](https://github.com/Annuity-Management/assethub-backend/compare/v1.2.0...v1.2.1) (2025-05-12)

# [1.2.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.1.3...v1.2.0) (2025-05-12)


### Bug Fixes

* fix ([11ff564](https://github.com/Annuity-Management/assethub-backend/commit/11ff5649634e201bac7b6dbe88476d5fc93607cd))
* fix migrations ([4a3f840](https://github.com/Annuity-Management/assethub-backend/commit/4a3f84056016b7e427cdbd87a75bf4328f92fc06))


### Features

* add missing migrations ([a64fd9f](https://github.com/Annuity-Management/assethub-backend/commit/a64fd9ffad7c921f6ae09e4ed09882f8c730a20b))
* improve queries ([8d88bd6](https://github.com/Annuity-Management/assethub-backend/commit/8d88bd6dbf69064525f81501a8ae81ab281c853e))
* improve queries ([8466481](https://github.com/Annuity-Management/assethub-backend/commit/84664811f4bd63a4594521a4e10656577e89f9a8))
* improve queries ([85f2724](https://github.com/Annuity-Management/assethub-backend/commit/85f272445a1c5d63007f1140d1b3273b0189d8d7))
* improve queries ([cd69e55](https://github.com/Annuity-Management/assethub-backend/commit/cd69e55957219b34bb7a4ff0375334e187db8e91))
* improve queries ([5cf11c1](https://github.com/Annuity-Management/assethub-backend/commit/5cf11c105319eb74c1c2046191779834b1b56a85))
* improve queries ([6180250](https://github.com/Annuity-Management/assethub-backend/commit/6180250dd40998e151b1686474d79b58984f6fae))
* run migrations on init ([5e5e131](https://github.com/Annuity-Management/assethub-backend/commit/5e5e131385d81cd82d2f2807570be7f7200ff9d6))
* switch migrations runner ([6a5aa78](https://github.com/Annuity-Management/assethub-backend/commit/6a5aa78985333d15f1450c6c7dd7c286fb8187eb))

## [1.1.3](https://github.com/Annuity-Management/assethub-backend/compare/v1.1.2...v1.1.3) (2025-05-12)


### Bug Fixes

* **AH-XXX:** end price customer on assets ([e671799](https://github.com/Annuity-Management/assethub-backend/commit/e6717997657b2d98a010d882a286afd8884df8f3))

## [1.1.2](https://github.com/Annuity-Management/assethub-backend/compare/v1.1.1...v1.1.2) (2025-05-12)

## [1.1.1](https://github.com/Annuity-Management/assethub-backend/compare/v1.1.0...v1.1.1) (2025-05-12)


### Bug Fixes

* move config outside the github action ([a3fef1e](https://github.com/Annuity-Management/assethub-backend/commit/a3fef1e8e5536c9d4c239d66f0a130e637c991e6))

# [1.1.0](https://github.com/Annuity-Management/assethub-backend/compare/v1.0.1...v1.1.0) (2025-05-12)


### Bug Fixes

* ci config ([cebffb1](https://github.com/Annuity-Management/assethub-backend/commit/cebffb1c64050f6353505ffa83d49b23885aa69e))


### Features

* Implement translation on schema ([2928548](https://github.com/Annuity-Management/assethub-backend/commit/292854881ac297706bd51ca37d9b7a8998cc764c))
