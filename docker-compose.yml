version: '3.8'

x-minio-common: &minio-common

services:
    bs-app:
        build:
            context: .
            target: development
        container_name: bs-app
        env_file:
            - './.env'
        restart: no
        ports:
            - '3000:3000'
        volumes:
            - ./:/app
            - ./docker/npm-cache/:/root/.npm
            - ./docker/shell/.bash_history:/root/.bash_history
        depends_on:
            bs-db:
                condition: service_healthy
            bs-keycloak:
                condition: service_healthy
            bs-init:
                condition: service_completed_successfully
        networks:
            bs-net:
                aliases:
                    - ${APP_HOST}

    bs-init:
        build:
            context: .
            target: builder
        command: /bin/sh -c "./docker/init.sh"
        container_name: bs-init
        env_file:
            - './.env'
        restart: no
        volumes:
            - ./:/app
        depends_on:
            bs-db:
                condition: service_healthy
            bs-keycloak:
                condition: service_healthy
        networks:
            - bs-net

    bs-db:
        image: postgres:16.4
        container_name: bs-db
        env_file:
            - './.env'
        environment:
            - POSTGRES_MULTIPLE_DATABASES="${DATABASE_NAME}","${KEYCLOAK_DATABASE_NAME}"
            - POSTGRES_USER=${DATABASE_USER}
            - POSTGRES_PASSWORD=${DATABASE_PASSWORD}
        ports:
            - 5432:5432
        volumes:
            - ./docker/postgresql/init.sh:/docker-entrypoint-initdb.d/create-multiple-postgresql-databases.sh
            - ./docker/postgresql:/var/lib/postgresql
        healthcheck:
            test: 'pg_isready -U ${DATABASE_USER} -d ${DATABASE_NAME}'
            interval: 2s
            timeout: 20s
            retries: 10
        networks:
            - bs-net

    bs-keycloak:
        image: quay.io/keycloak/keycloak:25.0
        container_name: bs-keycloak
        command: 'start-dev --import-realm'
        env_file:
            - './.env'
        environment:
            # Keycloak settings - https://www.keycloak.org/server/all-config
            - KC_HEALTH_ENABLED=true
            - KC_DB=postgres
            - KC_DB_URL_HOST=${DATABASE_HOST}
            - KC_DB_URL_DATABASE=${KEYCLOAK_DATABASE_NAME}
            - KC_DB_URL_PORT=${DATABASE_PORT}
            - KC_DB_USERNAME=${DATABASE_USER}
            - KC_DB_PASSWORD=${DATABASE_PASSWORD}
            - KEYCLOAK_ADMIN=${KEYCLOAK_ADMIN_USER}
            - KEYCLOAK_ADMIN_PASSWORD=${KEYCLOAK_ADMIN_PASSWORD}
            - KC_METRICS_ENABLED=true
        ports:
            - 8080:8080
            - 9000:9000
        volumes:
            - ./docker/keycloak/realms/realm-tesedi.json:/opt/keycloak/data/import/realm-tesedi.json
        healthcheck:
            test:
                [
                    'CMD-SHELL',
                    "exec 3<>/dev/tcp/127.0.0.1/9000;echo -e 'GET /health/ready HTTP/1.1\r\nhost: http://localhost\r\nConnection: close\r\n\r\n' >&3;if [ $? -eq 0 ]; then echo 'Healthcheck Successful';exit 0;else echo 'Healthcheck Failed';exit 1;fi;",
                ]
            interval: 30s
            timeout: 10s
            retries: 3
        depends_on:
            bs-db:
                condition: service_healthy
        networks:
            bs-net:
                aliases:
                    - ${KEYCLOAK_HOST}

    bs-mailpit:
        image: axllent/mailpit:latest
        container_name: bs-mailpit
        command: --smtp-auth-allow-insecure
        ports:
            - '1025:1025' # SMTP port
            - '8025:8025' # Web interface port
        environment:
            - MP_SMTP_AUTH=user:password
            - MP_SMTP_AUTH_ALLOW_INSECURE=true
        healthcheck:
            test: ['CMD', 'curl', '-f', 'http://localhost:8025']
            interval: 30s
            timeout: 10s
            retries: 3
        networks:
            - bs-net

    bs-minio:
        image: quay.io/minio/minio
        command: server /data --console-address ":9001"
        env_file:
            - './.env'
        container_name: bs-minio
        ports:
            - '9100:9000' # S3 API
            - '9101:9001' # Web Console
        environment:
            MINIO_ROOT_USER: ${MINIO_ACCESS_KEY}
            MINIO_ROOT_PASSWORD: ${MINIO_SECRET_KEY}
        healthcheck:
            test: ['CMD', 'curl', '-f', 'http://localhost:9100/minio/health/live']
            interval: 30s
            timeout: 20s
            retries: 3
        volumes:
            - bs-minio-data:/data
        networks:
            - bs-net

networks:
    bs-net:
        external: true

volumes:
    bs-minio-data:
