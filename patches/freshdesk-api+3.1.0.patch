diff --git a/node_modules/freshdesk-api/lib/utils.js b/node_modules/freshdesk-api/lib/utils.js
index a004078..779324a 100644
--- a/node_modules/freshdesk-api/lib/utils.js
+++ b/node_modules/freshdesk-api/lib/utils.js
@@ -150,15 +150,35 @@ async function makeRequest(method, auth, url, qs, data, cb) {
 		path: fullUrl.pathname
 	};
 
+	function processSubItems(fd, prefix, data, isArray = true, follow = true) {
+		for (const [key, value] of Object.entries(data)) {
+			const keyName = `${prefix}${(isArray ? `[]` : `[${key}]`)}`;
+
+			if (follow) {
+				if (Array.isArray(value)) {
+					processSubItems(fd, keyName, value, true, true);
+				} else if (typeof value === 'object' && value !== null) {
+					processSubItems(fd, keyName, value, false, true);
+				} else {
+					fd.append(keyName, value);
+				}
+			} else {
+				fd.append(keyName, value);
+			}
+		}
+	}
+
 	if (data) {
 		if ("attachments" in data && Array.isArray(data.attachments)) {
 			const fd = new FormData();
 
 			for (let [entryKey, entryValue] of Object.entries(data)) {
-				if (Array.isArray(entryValue)) {
-					for (let arrElement of entryValue) {
-						fd.append(entryKey + "[]", arrElement);
-					}
+				if (entryKey === "attachments") {
+					processSubItems(fd, entryKey, entryValue, true, false);
+				} else if (Array.isArray(entryValue)) {
+					processSubItems(fd, entryKey, entryValue, true, true);
+				} else if (typeof entryValue === 'object' && entryValue !== null) {
+					processSubItems(fd, entryKey, entryValue, false, true);
 				} else {
 					fd.append(entryKey, entryValue);
 				}
