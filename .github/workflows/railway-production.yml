name: Deploy to Railway Production

on:
    push:
        branches:
            - main
        paths-ignore:
            - '**/*.md'
            - 'docs/**'
            - 'CHANGELOG.md'

permissions:
    contents: write
    actions: write
    deployments: write
    issues: write

jobs:
    deploy-to-production:
        name: Deploy to Railway Production
        runs-on: ubuntu-latest
        timeout-minutes: 20
        environment:
            name: production
            url: https://assethub-backend-prod.up.railway.app
        steps:
            - name: Checkout
              uses: actions/checkout@v3
              with:
                  fetch-depth: 0
                  token: ${{ secrets.RELEASE_TOKEN }}

            - name: Setup Node.js
              uses: actions/setup-node@v3
              with:
                  node-version: '18'
                  cache: 'npm'

            - name: Cache dependencies
              uses: actions/cache@v3
              with:
                  path: ~/.npm
                  key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
                  restore-keys: |
                      ${{ runner.os }}-node-

            - name: Install dependencies
              run: npm ci

            - name: Determine next version
              id: semantic
              uses: cycjimmy/semantic-release-action@v4
              with:
                  semantic_version: 21
                  dry_run: true
              env:
                  GITHUB_TOKEN: ${{ secrets.RELEASE_TOKEN }}

            - name: Build project
              run: npm run build

            - name: Install Railway CLI
              run: npm install -g @railway/cli

            - name: Record previous deployment
              id: previous_deployment
              run: |
                  echo "Capturing information about current production deployment for potential rollback"
                  CURRENT_VERSION=$(curl -s https://assethub-backend-prod.up.railway.app || echo "unknown")
                  echo "PREVIOUS_VERSION=$CURRENT_VERSION" >> $GITHUB_OUTPUT
              continue-on-error: true

            - name: Deploy to Railway Production
              id: deploy
              env:
                  RAILWAY_TOKEN: ${{ secrets.RAILWAY_PROD_TOKEN }}
                  SVC_ID: c9039873-49d2-448e-85d7-ca43de675df7
              run: railway up --service $SVC_ID

            - name: Verify deployment
              id: verify
              run: |
                  echo "Waiting for deployment to stabilize..."
                  sleep 45
                  MAX_RETRIES=5
                  RETRY_COUNT=0

                  while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
                    STATUS_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://assethub-backend-prod.up.railway.app || echo "failed")
                    if [ "$STATUS_CODE" = "200" ]; then
                      echo "Deployment verified successfully!"
                      exit 0
                    fi
                    echo "Attempt $((RETRY_COUNT+1))/$MAX_RETRIES: Deployment not ready yet (status: $STATUS_CODE). Waiting..."
                    RETRY_COUNT=$((RETRY_COUNT+1))
                    sleep 15
                  done

                  echo "Deployment verification failed after $MAX_RETRIES attempts"
                  exit 1

            - name: Create release and tag
              if: success() && steps.semantic.outputs.new_release_published == 'true'
              uses: cycjimmy/semantic-release-action@v4
              with:
                  semantic_version: 21
              env:
                  GITHUB_TOKEN: ${{ secrets.RELEASE_TOKEN }}

            - name: Rollback on failure
              if: failure() && steps.deploy.outcome == 'failure'
              env:
                  RAILWAY_TOKEN: ${{ secrets.RAILWAY_PROD_TOKEN }}
                  SVC_ID: assethub-backend-prod
              run: |
                  echo "Deployment failed, attempting to rollback..."
                  railway rollback --service $SVC_ID
                  echo "Rollback initiated. Please verify system status."
              continue-on-error: true

            - name: Notify on success
              if: success()
              uses: rtCamp/action-slack-notify@v2
              env:
                  SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
                  SLACK_CHANNEL: deployments
                  SLACK_COLOR: good
                  SLACK_TITLE: Production Deployment Successful
                  SLACK_MESSAGE: |
                      Assethub backend successfully deployed to production
                      ${{ steps.semantic.outputs.new_release_published == 'true' && format('Version: {0}', steps.semantic.outputs.new_release_version) || 'No new release created' }}
              continue-on-error: true

            - name: Notify on failure
              if: failure()
              uses: rtCamp/action-slack-notify@v2
              env:
                  SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
                  SLACK_CHANNEL: deployments
                  SLACK_COLOR: danger
                  SLACK_TITLE: Production Deployment Failed
                  SLACK_MESSAGE: 'Assethub backend deployment to production failed. Rollback may have been initiated.'
              continue-on-error: true
