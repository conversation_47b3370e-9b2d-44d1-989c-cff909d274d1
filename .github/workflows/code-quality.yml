name: Code Quality Checks

on:
    pull_request:
        branches:
            - main
            - dev
        paths-ignore:
            - '**/*.md'
            - 'docs/**'

jobs:
    lint:
        name: <PERSON><PERSON>
        runs-on: ubuntu-latest
        steps:
            - name: Checkout
              uses: actions/checkout@v4

            - name: Setup Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: '18'
                  cache: 'npm'

            - name: Cache dependencies
              uses: actions/cache@v4
              with:
                  path: ~/.npm
                  key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
                  restore-keys: |
                      ${{ runner.os }}-node-

            - name: Install dependencies
              run: npm ci

            - name: Run ESLint
              run: npm run lint

            - name: Run Prettier check
              run: npm run format:check

    security:
        name: Security Scan
        runs-on: ubuntu-latest
        steps:
            - name: Checkout
              uses: actions/checkout@v4

            - name: Setup Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: '18'
                  cache: 'npm'

            - name: Install dependencies
              run: npm ci

            - name: Run npm audit
              run: npm audit --audit-level=high
              continue-on-error: true

            - name: <PERSON> Snyk to check for vulnerabilities
              uses: snyk/actions/node@master
              continue-on-error: true
              env:
                  SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
    test:
        name: Test
        runs-on: ubuntu-latest
        steps:
            - name: Checkout
              uses: actions/checkout@v4

            - name: Setup Node.js
              uses: actions/setup-node@v4
              with:
                   node-version: '18'
                   cache: 'npm'

            - name: Cache dependencies
              uses: actions/cache@v4
              with:
                  path: ~/.npm
                  key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
                  restore-keys: |
                      ${{ runner.os }}-node-

            - name: Install dependencies
              run: npm ci

            - name: Run tests with coverage
              run: npm run test:cov

            - name: Upload coverage report
              uses: codecov/codecov-action@v4
              with:
                  token: ${{ secrets.CODECOV_TOKEN }}
                  fail_ci_if_error: false
                  flags: unittests

    build:
        name: Build
        runs-on: ubuntu-latest
        steps:
            - name: Checkout
              uses: actions/checkout@v4

            - name: Setup Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: '18'
                  cache: 'npm'

            - name: Cache dependencies
              uses: actions/cache@v4
              with:
                  path: ~/.npm
                  key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
                  restore-keys: |
                      ${{ runner.os }}-node-

            - name: Install dependencies
              run: npm ci

            - name: Build project
              run: npm run build

            - name: Archive build artifacts
              uses: actions/upload-artifact@v4
              with:
                  name: build-output
                  path: dist/
                  retention-days: 5
