name: Deploy to Railway Staging

on:
    push:
        branches:
            - dev
        paths-ignore:
            - '**/*.md'
            - 'docs/**'
            - 'CHANGELOG.md'
            - 'package.json'

jobs:
    deploy-to-staging:
        name: Deploy to Railway Staging
        runs-on: ubuntu-latest
        timeout-minutes: 15
        if: ${{ !contains(github.event.head_commit.message, 'chore(release):') }}
        steps:
            - name: Checkout
              uses: actions/checkout@v3
              with:
                  fetch-depth: 0

            - name: Setup Node.js
              uses: actions/setup-node@v3
              with:
                  node-version: '18'
                  cache: 'npm'

            - name: Cache dependencies
              uses: actions/cache@v3
              with:
                  path: ~/.npm
                  key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
                  restore-keys: |
                      ${{ runner.os }}-node-

            - name: Install dependencies
              run: npm ci

            # - name: Run tests
            #   run: npm test
            #   continue-on-error: false

            - name: Build project
              run: npm run build

            - name: Install Railway CLI
              run: npm install -g @railway/cli

            - name: Deploy to Railway Staging
              id: deploy
              env:
                  RAILWAY_TOKEN: ${{ secrets.RAILWAY_STG_TOKEN }}
                  SVC_ID: ************************************
              run: railway up --service $SVC_ID

            - name: Verify deployment
              run: |
                  echo "Waiting for deployment to stabilize..."
                  sleep 30
                  MAX_RETRIES=5
                  RETRY_COUNT=0

                  while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
                    STATUS_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://assethub-backend-stg.up.railway.app || echo "failed")
                    if [ "$STATUS_CODE" = "200" ]; then
                      echo "Deployment verified successfully!"
                      exit 0
                    fi
                    echo "Attempt $((RETRY_COUNT+1))/$MAX_RETRIES: Deployment not ready yet (status: $STATUS_CODE). Waiting..."
                    RETRY_COUNT=$((RETRY_COUNT+1))
                    sleep 15
                  done

                  echo "Deployment verification failed after $MAX_RETRIES attempts"
                  exit 1
              continue-on-error: true

            - name: Notify on success
              if: success()
              uses: rtCamp/action-slack-notify@v2
              env:
                  SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
                  SLACK_CHANNEL: deployments
                  SLACK_COLOR: good
                  SLACK_TITLE: Staging Deployment Successful
                  SLACK_MESSAGE: 'Assethub backend successfully deployed to staging environment'
              continue-on-error: true

            - name: Notify on failure
              if: failure()
              uses: rtCamp/action-slack-notify@v2
              env:
                  SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
                  SLACK_CHANNEL: deployments
                  SLACK_COLOR: danger
                  SLACK_TITLE: Staging Deployment Failed
                  SLACK_MESSAGE: 'Assethub backend deployment to staging failed'
              continue-on-error: true
