{"permissions": {"allow": ["Bash(git diff:*)", "Bash(npm test:*)", "Bash(grep:*)", "Bash(gh pr view:*)", "Bash(gh pr diff:*)", "Bash(gh pr list:*)", "Bash(find:*)", "Bash(npm run lint:*)", "Bash(npm run:*)", "<PERSON><PERSON>(npx nestjs-i18n:*)", "Bash(rg:*)", "Bash(ls:*)", "Bash(node:*)", "<PERSON><PERSON>(chmod:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "WebFetch(domain:www.npmjs.com)", "Bash(git add:*)", "Bash(npx tsc:*)", "Bash(npx eslint:*)", "<PERSON><PERSON>(npx prettier:*)", "<PERSON><PERSON>(diff:*)", "Bash(rm:*)", "Bash(PGPASSWORD=password psql:*)", "mcp__serena__list_dir", "mcp__serena__initial_instructions", "mcp__serena__check_onboarding_performed", "mcp__serena__onboarding", "mcp__serena__list_memories", "mcp__serena__get_symbols_overview", "mcp__serena__search_for_pattern", "mcp__serena__find_file", "mcp__serena__write_memory"], "deny": []}}