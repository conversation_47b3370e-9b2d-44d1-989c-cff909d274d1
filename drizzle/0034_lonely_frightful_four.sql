CREATE OR REPLACE FUNCTION notify_data_changes()
RETURNS TRIGGER AS $$
DECLARE
    payload JSON;
    entity_name TEXT;
    org_id UUID;
    action_type event_log_action;
    changes_data JSONB;
    entity_id_val TEXT;
    old_data JSONB;
    new_data JSONB;
BEGIN
    entity_name := INITCAP(TRIM(TRAILING 's' FROM TG_TABLE_NAME));

    IF (TG_OP = 'INSERT' OR TG_OP = 'UPDATE') THEN
        BEGIN
            org_id := (NEW.organization_id)::UUID;
        EXCEPTION WHEN OTHERS THEN
            org_id := NULL;
        END;
    ELSIF (TG_OP = 'DELETE') THEN
        BEGIN
            org_id := (OLD.organization_id)::UUID;
        EXCEPTION WHEN OTHERS THEN
            org_id := NULL;
        END;
    END IF;

    IF (TG_OP = 'INSERT') THEN
        action_type := 'CREATED';
        entity_id_val := NEW.id;
        new_data := row_to_json(NEW)::JSONB - 'data';
        changes_data := json_diff(NULL, new_data);
        
        payload := json_build_object(
            'action', 'CREATED',
            'entityId', NEW.id,
            'entity', entity_name,
            'changes', changes_data
        );
    ELSIF (TG_OP = 'UPDATE') THEN
        action_type := 'UPDATED';
        entity_id_val := NEW.id;
        old_data := row_to_json(OLD)::JSONB - 'data';
        new_data := row_to_json(NEW)::JSONB - 'data';
        changes_data := json_diff(old_data, new_data);
        
        payload := json_build_object(
            'action', 'UPDATED',
            'entityId', NEW.id,
            'entity', entity_name,
            'changes', changes_data
        );
    ELSIF (TG_OP = 'DELETE') THEN
        action_type := 'DELETED';
        entity_id_val := OLD.id;
        old_data := row_to_json(OLD)::JSONB - 'data';
        changes_data := json_diff(old_data, NULL);
        
        payload := json_build_object(
            'action', 'DELETED',
            'entityId', OLD.id,
            'entity', entity_name,
            'changes', changes_data
        );
    END IF;

    BEGIN
        INSERT INTO event_log (
            entity,
            entity_id,
            action,
            changes,
            organization_id,
            created_at,
            status
        ) VALUES (
            entity_name,
            entity_id_val,
            action_type,
            changes_data,
            org_id,
            CURRENT_TIMESTAMP,
            'pending'
        );
    EXCEPTION WHEN OTHERS THEN
        RAISE WARNING 'Failed to insert into event_log: %', SQLERRM;
    END;

    PERFORM pg_notify('data_changes', payload::text);
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;
