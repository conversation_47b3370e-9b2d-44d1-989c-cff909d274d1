-- Current sql file was generated after introspecting the database
-- If you want to run this migration please uncomment this code before executing migrations

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'access_log_type') THEN
        CREATE TYPE "public"."access_log_type" AS ENUM('lost', 'invalid', 'success');
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'action_type') THEN
        CREATE TYPE "public"."action_type" AS ENUM('create', 'read', 'update', 'approve', 'reject', 'manage');
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'contact_status') THEN
        CREATE TYPE "public"."contact_status" AS ENUM('active', 'inactive');
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'contract_ui_status') THEN
        CREATE TYPE "public"."contract_ui_status" AS ENUM('Invalid', 'Active', 'Expiring', 'Expired', 'Terminated', 'Renewed');
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'entity_type') THEN
        CREATE TYPE "public"."entity_type" AS ENUM('Vendor', 'Distributor', 'Reseller', 'EndUser');
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'organization_status') THEN
        CREATE TYPE "public"."organization_status" AS ENUM('active', 'inactive');
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'quote_ui_status') THEN
        CREATE TYPE "public"."quote_ui_status" AS ENUM('Invalid', 'Open', 'Change Requested', 'Ordered', 'Lost');
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'resource_type') THEN
        CREATE TYPE "public"."resource_type" AS ENUM('quote', 'quote_item', 'contract', 'contract_item', 'entity', 'organization', 'user', 'role', 'contact', 'asset', 'platform', 'user_organization');
    END IF;
END $$;--> statement-breakpoint

---------------------------------------

CREATE TABLE IF NOT EXISTS "mikro_orm_migrations" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255),
	"executed_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "role_permissions" (
	"id" uuid PRIMARY KEY NOT NULL,
	"role_id" uuid NOT NULL,
	"permission_id" uuid NOT NULL,
	"created_at" timestamp with time zone NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "user_roles" (
	"id" uuid PRIMARY KEY NOT NULL,
	"user_id" uuid NOT NULL,
	"role_id" uuid NOT NULL,
	"created_at" timestamp with time zone NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "entities" (
	"id" text PRIMARY KEY NOT NULL,
	"type" "entity_type",
	"name" text,
	"owner_id" text,
	"data" jsonb
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "organization_entities" (
	"id" uuid PRIMARY KEY NOT NULL,
	"organization_id" uuid NOT NULL,
	"entity_id" text NOT NULL,
	"created_at" timestamp with time zone NOT NULL,
	"updated_at" timestamp with time zone NOT NULL,
	CONSTRAINT "organization_entities_organization_id_unique" UNIQUE("organization_id"),
	CONSTRAINT "organization_entities_entity_id_unique" UNIQUE("entity_id")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "contacts" (
	"id" text PRIMARY KEY NOT NULL,
	"entity_id" text NOT NULL,
	"data" jsonb,
	"status" "contact_status" DEFAULT 'inactive' NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "addresses" (
	"id" text PRIMARY KEY NOT NULL,
	"entity_id" text NOT NULL,
	"data" jsonb
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "organizations" (
	"id" uuid PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"parent_id" uuid,
	"created_at" timestamp with time zone NOT NULL,
	"updated_at" timestamp with time zone NOT NULL,
	"status" "organization_status" DEFAULT 'inactive' NOT NULL,
	"emailable" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "quote_request_histories" (
	"id" uuid PRIMARY KEY NOT NULL,
	"action" text NOT NULL,
	"request" jsonb NOT NULL,
	"quote_id" text NOT NULL,
	"triggered_by" jsonb NOT NULL,
	"created_at" timestamp with time zone NOT NULL,
	"updated_at" timestamp with time zone NOT NULL,
	CONSTRAINT "quote_request_histories_action_check" CHECK (action = ANY (ARRAY['APPROVAL'::text, 'DECLINE'::text, 'REQUEST_CHANGE'::text, 'REQUEST_QUOTE'::text]))
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "invitations" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"user_id" varchar(255) NOT NULL,
	"email" varchar(255) NOT NULL,
	"organization_ids" jsonb NOT NULL,
	"role_id" varchar(255) NOT NULL,
	"token" varchar(255) NOT NULL,
	"is_accepted" boolean DEFAULT false NOT NULL,
	"created_at" timestamp with time zone NOT NULL,
	"expires_at" timestamp with time zone NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "reset_tokens" (
	"id" uuid PRIMARY KEY NOT NULL,
	"token" text NOT NULL,
	"user_id" uuid NOT NULL,
	"created_at" timestamp with time zone NOT NULL,
	"updated_at" timestamp with time zone NOT NULL,
	"expires_at" timestamp with time zone NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "files" (
	"id" uuid PRIMARY KEY NOT NULL,
	"url" text NOT NULL,
	"created_at" timestamp with time zone NOT NULL,
	"updated_at" timestamp with time zone NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "roles" (
	"id" uuid PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" varchar(255),
	"organization_id" uuid NOT NULL,
	"created_at" timestamp with time zone NOT NULL,
	"updated_at" timestamp with time zone NOT NULL,
	"visible" boolean DEFAULT true NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "permissions" (
	"id" uuid PRIMARY KEY NOT NULL,
	"resource_type" "resource_type" NOT NULL,
	"action_type" "action_type" NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" varchar(255),
	"created_at" timestamp with time zone NOT NULL,
	"updated_at" timestamp with time zone NOT NULL,
	"disabled" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "users" (
	"id" uuid PRIMARY KEY NOT NULL,
	"keycloak_id" varchar(255) NOT NULL,
	"email" varchar(255) NOT NULL,
	"first_name" varchar(255),
	"last_name" varchar(255),
	"phone" varchar(255),
	"is_active" boolean DEFAULT false NOT NULL,
	"created_at" timestamp with time zone NOT NULL,
	"updated_at" timestamp with time zone NOT NULL,
	"last_login_at" timestamp with time zone,
	"locale" varchar(255) DEFAULT 'en' NOT NULL,
	"timezone" varchar(255) DEFAULT 'UTC' NOT NULL,
	CONSTRAINT "users_keycloak_id_unique" UNIQUE("keycloak_id"),
	CONSTRAINT "users_email_unique" UNIQUE("email")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "contracts" (
	"id" text PRIMARY KEY NOT NULL,
	"contract_no" text,
	"sar" text,
	"status" text,
	"group_id" text,
	"start_date" text,
	"end_date" text,
	"currency" text,
	"vendor_id" text,
	"distributor_id" text,
	"reseller_id" text,
	"end_user_id" text,
	"reseller_total_price" numeric(20, 4),
	"data" jsonb,
	"ui_status" "contract_ui_status",
	"end_customer_total_price" numeric(20, 4)
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "contract_items" (
	"id" text PRIMARY KEY NOT NULL,
	"item_no" integer,
	"contract_id" text,
	"vendor" text,
	"serial_no" text,
	"product_sku" text,
	"data" jsonb,
	"service_level_sku" text,
	"service_group_sku" text,
	"service_group_label" text,
	"reseller_price" numeric(20, 4),
	"end_customer_price" numeric(20, 4),
	"quantity" numeric(10, 0)
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "quotes" (
	"id" text PRIMARY KEY NOT NULL,
	"quote_no" text,
	"quote_type" text,
	"status" text,
	"group_id" text,
	"start_date" text,
	"end_date" text,
	"expiry_date" text,
	"currency" text,
	"vendor_id" text,
	"distributor_id" text,
	"reseller_id" text,
	"end_user_id" text,
	"data" jsonb,
	"ui_status" "quote_ui_status",
	"reseller_total_price" numeric(20, 4),
	"distributor_total_price" numeric(20, 4),
	"end_customer_total_price" numeric(20, 4)
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "quote_items" (
	"id" text PRIMARY KEY NOT NULL,
	"item_no" integer,
	"quote_id" text,
	"vendor" text,
	"serial_no" text,
	"product_sku" text,
	"data" jsonb,
	"source_contract_item_id" text,
	"resulting_contract_item_id" text,
	"service_level_sku" text,
	"service_group_sku" text,
	"service_group_label" text,
	"reseller_price" numeric(20, 4),
	"distributor_price" numeric(20, 4),
	"end_customer_price" numeric(20, 4),
	"quantity" numeric(10, 0)
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "access_log" (
	"id" serial PRIMARY KEY NOT NULL,
	"type" "access_log_type" NOT NULL,
	"url_path" text NOT NULL,
	"payload" jsonb,
	"header" jsonb NOT NULL,
	"error_message" text,
	"timestamp" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "file_quote_request_histories" (
	"file_id" uuid NOT NULL,
	"quote_request_history_id" uuid NOT NULL,
	CONSTRAINT "file_quote_request_histories_pkey" PRIMARY KEY("file_id","quote_request_history_id")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "products" (
	"vendor" text NOT NULL,
	"sku" text NOT NULL,
	"product_id" text,
	"description" text,
	"data" jsonb,
	"show_in_dropdown" boolean DEFAULT false NOT NULL,
	"weight" integer DEFAULT 0 NOT NULL,
	CONSTRAINT "products_pkey" PRIMARY KEY("vendor","sku"),
	CONSTRAINT "products_vendor_sku_unique" UNIQUE("vendor","sku")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "asset_enrichments" (
	"serial_number" text NOT NULL,
	"product_sku" text NOT NULL,
	"country_code" text,
	"is_enriched" boolean DEFAULT false NOT NULL,
	"updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"data" jsonb DEFAULT '{}'::jsonb NOT NULL,
	"coverage_status" varchar(255),
	CONSTRAINT "asset_enrichments_pkey" PRIMARY KEY("serial_number","product_sku")
);
--> statement-breakpoint

---------------------------------------

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'role_permissions_role_id_foreign' AND conrelid = 'role_permissions'::regclass::oid) THEN
        ALTER TABLE "role_permissions" ADD CONSTRAINT "role_permissions_role_id_foreign" FOREIGN KEY ("role_id") REFERENCES "public"."roles"("id") ON DELETE no action ON UPDATE cascade;
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'role_permissions_permission_id_foreign' AND conrelid = 'role_permissions'::regclass::oid) THEN
        ALTER TABLE "role_permissions" ADD CONSTRAINT "role_permissions_permission_id_foreign" FOREIGN KEY ("permission_id") REFERENCES "public"."permissions"("id") ON DELETE no action ON UPDATE cascade;
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'user_roles_user_id_foreign' AND conrelid = 'user_roles'::regclass::oid) THEN
        ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_user_id_foreign" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE cascade;
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'user_roles_role_id_foreign' AND conrelid = 'user_roles'::regclass::oid) THEN
        ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_role_id_foreign" FOREIGN KEY ("role_id") REFERENCES "public"."roles"("id") ON DELETE no action ON UPDATE cascade;
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'organization_entities_organization_id_foreign' AND conrelid = 'organization_entities'::regclass::oid) THEN
        ALTER TABLE "organization_entities" ADD CONSTRAINT "organization_entities_organization_id_foreign" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE no action ON UPDATE cascade;
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'organization_entities_entity_id_foreign' AND conrelid = 'organization_entities'::regclass::oid) THEN
        ALTER TABLE "organization_entities" ADD CONSTRAINT "organization_entities_entity_id_foreign" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE no action ON UPDATE cascade;
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'organizations_parent_id_foreign' AND conrelid = 'organizations'::regclass::oid) THEN
        ALTER TABLE "organizations" ADD CONSTRAINT "organizations_parent_id_foreign" FOREIGN KEY ("parent_id") REFERENCES "public"."organizations"("id") ON DELETE set null ON UPDATE cascade;
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'quote_request_histories_quote_id_foreign' AND conrelid = 'quote_request_histories'::regclass::oid) THEN
        ALTER TABLE "quote_request_histories" ADD CONSTRAINT "quote_request_histories_quote_id_foreign" FOREIGN KEY ("quote_id") REFERENCES "public"."quotes"("id") ON DELETE no action ON UPDATE cascade;
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'roles_organization_id_foreign' AND conrelid = 'roles'::regclass::oid) THEN
        ALTER TABLE "roles" ADD CONSTRAINT "roles_organization_id_foreign" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE no action ON UPDATE cascade;
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'file_quote_request_histories_file_id_foreign' AND conrelid = 'file_quote_request_histories'::regclass::oid) THEN
        ALTER TABLE "file_quote_request_histories" ADD CONSTRAINT "file_quote_request_histories_file_id_foreign" FOREIGN KEY ("file_id") REFERENCES "public"."files"("id") ON DELETE cascade ON UPDATE cascade;
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'file_quote_request_histories_quote_request_history_id_foreign' AND conrelid = 'file_quote_request_histories'::regclass::oid) THEN
        ALTER TABLE "file_quote_request_histories" ADD CONSTRAINT "file_quote_request_histories_quote_request_history_id_foreign" FOREIGN KEY ("quote_request_history_id") REFERENCES "public"."quote_request_histories"("id") ON DELETE cascade ON UPDATE cascade;
    END IF;
END $$;--> statement-breakpoint

---------------------------------------

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_contacts_e_id' AND tablename='contacts') THEN
        CREATE INDEX "idx_contacts_e_id" ON "contacts" USING btree ("entity_id" text_ops);
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'users_keycloak_id_index' AND tablename='users') THEN
        CREATE INDEX "users_keycloak_id_index" ON "users" USING btree ("keycloak_id" text_ops);
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'contracts_contract_no_index' AND tablename='contracts') THEN
        CREATE INDEX "contracts_contract_no_index" ON "contracts" USING btree ("contract_no" text_ops);
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'contracts_distributor_id_index' AND tablename='contracts') THEN
        CREATE INDEX "contracts_distributor_id_index" ON "contracts" USING btree ("distributor_id" text_ops);
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'contracts_end_user_id_index' AND tablename='contracts') THEN
        CREATE INDEX "contracts_end_user_id_index" ON "contracts" USING btree ("end_user_id" text_ops);
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'contracts_reseller_id_index' AND tablename='contracts') THEN
        CREATE INDEX "contracts_reseller_id_index" ON "contracts" USING btree ("reseller_id" text_ops);
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'contracts_ui_status_index' AND tablename='contracts') THEN
        CREATE INDEX "contracts_ui_status_index" ON "contracts" USING btree ("ui_status" enum_ops);
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'contracts_vendor_id_index' AND tablename='contracts') THEN
        CREATE INDEX "contracts_vendor_id_index" ON "contracts" USING btree ("vendor_id" text_ops);
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_contracts_r_id' AND tablename='contracts') THEN
        CREATE INDEX "idx_contracts_r_id" ON "contracts" USING btree ("reseller_id" text_ops);
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_contract_items_c_id' AND tablename='contract_items') THEN
        CREATE INDEX "idx_contract_items_c_id" ON "contract_items" USING btree ("contract_id" text_ops);
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_contract_items_p_sku' AND tablename='contract_items') THEN
        CREATE INDEX "idx_contract_items_p_sku" ON "contract_items" USING btree ("product_sku" text_ops);
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_contract_items_s_no' AND tablename='contract_items') THEN
        CREATE INDEX "idx_contract_items_s_no" ON "contract_items" USING btree ("serial_no" text_ops);
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_quotes_r_id' AND tablename='quotes') THEN
        CREATE INDEX "idx_quotes_r_id" ON "quotes" USING btree ("reseller_id" text_ops);
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'quotes_distributor_id_index' AND tablename='quotes') THEN
        CREATE INDEX "quotes_distributor_id_index" ON "quotes" USING btree ("distributor_id" text_ops);
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'quotes_end_user_id_index' AND tablename='quotes') THEN
        CREATE INDEX "quotes_end_user_id_index" ON "quotes" USING btree ("end_user_id" text_ops);
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'quotes_vendor_id_index' AND tablename='quotes') THEN
        CREATE INDEX "quotes_vendor_id_index" ON "quotes" USING btree ("vendor_id" text_ops);
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_quote_items_ecp' AND tablename='quote_items') THEN
        CREATE INDEX "idx_quote_items_ecp" ON "quote_items" USING btree ("end_customer_price" numeric_ops);
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_quote_items_p_sku' AND tablename='quote_items') THEN
        CREATE INDEX "idx_quote_items_p_sku" ON "quote_items" USING btree ("product_sku" text_ops);
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_quote_items_q_id' AND tablename='quote_items') THEN
        CREATE INDEX "idx_quote_items_q_id" ON "quote_items" USING btree ("quote_id" text_ops);
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_quote_items_rci' AND tablename='quote_items') THEN
        CREATE INDEX "idx_quote_items_rci" ON "quote_items" USING btree ("resulting_contract_item_id" text_ops);
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_quote_items_sci' AND tablename='quote_items') THEN
        CREATE INDEX "idx_quote_items_sci" ON "quote_items" USING btree ("source_contract_item_id" text_ops);
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_quote_items_sno' AND tablename='quote_items') THEN
        CREATE INDEX "idx_quote_items_sno" ON "quote_items" USING btree ("serial_no" text_ops);
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_quote_items_v_p_sku' AND tablename='quote_items') THEN
        CREATE INDEX "idx_quote_items_v_p_sku" ON "quote_items" USING btree ("vendor" text_ops,"product_sku" text_ops);
    END IF;
END $$;--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_quote_items_v_sl_sku' AND tablename='quote_items') THEN
        CREATE INDEX "idx_quote_items_v_sl_sku" ON "quote_items" USING btree ("vendor" text_ops,"service_level_sku" text_ops);
    END IF;
END $$;--> statement-breakpoint

---------------------------------------

DROP VIEW IF EXISTS "public"."v_documents";
DROP VIEW IF EXISTS "public"."v_assets";
DROP VIEW IF EXISTS "public"."v_quote_items";
DROP VIEW IF EXISTS "public"."v_contract_items";

CREATE OR REPLACE VIEW "public"."v_contract_items" AS (SELECT ci.id, ci.item_no, ci.contract_id, ci.vendor, ci.serial_no, ci.product_sku, ci.service_level_sku, ci.service_group_sku, ci.reseller_price, ci.end_customer_price, ci.quantity, ci.reseller_price * ci.quantity AS reseller_price_final, ci.end_customer_price * ci.quantity AS end_customer_price_final, ci.data, ci.service_group_label, cp.description AS product_name, sp.description AS service_name, c.contract_no, c.status AS contract_status, c.start_date, c.end_date, ae.coverage_status, c.currency, c.distributor_id, c.vendor_id, c.end_user_id, c.reseller_id FROM contract_items ci LEFT JOIN products cp ON cp.sku = ci.product_sku AND cp.vendor = ci.vendor LEFT JOIN products sp ON sp.sku = ci.service_level_sku AND sp.vendor = ci.vendor LEFT JOIN contracts c ON c.id = ci.contract_id LEFT JOIN asset_enrichments ae ON ae.serial_number = ci.serial_no AND ae.product_sku = ci.product_sku);--> statement-breakpoint
CREATE OR REPLACE VIEW "public"."v_quote_items" AS (SELECT qi.id, qi.item_no, qi.quote_id, qi.vendor, qi.serial_no, qi.product_sku, qi.service_level_sku, qi.service_group_sku, qi.reseller_price, qi.distributor_price, qi.end_customer_price, qi.quantity, qi.reseller_price * qi.quantity AS reseller_price_final, qi.distributor_price * qi.quantity AS distributor_price_final, qi.end_customer_price * qi.quantity AS end_customer_price_final, qi.data, qi.service_group_label, cp.description AS product_name, sp.description AS service_name, q.quote_no, q.status AS quote_status, q.start_date, q.end_date, ae.coverage_status, q.currency, q.distributor_id, q.vendor_id, q.end_user_id, q.reseller_id FROM quote_items qi LEFT JOIN products cp ON cp.sku = qi.product_sku AND cp.vendor = qi.vendor LEFT JOIN products sp ON sp.sku = qi.service_level_sku AND sp.vendor = qi.vendor LEFT JOIN quotes q ON q.id = qi.quote_id LEFT JOIN asset_enrichments ae ON ae.serial_number = qi.serial_no AND ae.product_sku = qi.product_sku);--> statement-breakpoint
CREATE OR REPLACE VIEW "public"."v_assets" AS (SELECT v_contract_items.vendor, v_contract_items.product_sku, v_contract_items.serial_no, v_contract_items.contract_no, v_contract_items.contract_status, NULL::text AS quote_no, NULL::text AS quote_status, v_contract_items.start_date, v_contract_items.end_date, v_contract_items.reseller_id FROM v_contract_items UNION SELECT v_quote_items.vendor, v_quote_items.product_sku, v_quote_items.serial_no, NULL::text AS contract_no, NULL::text AS contract_status, v_quote_items.quote_no, v_quote_items.quote_status, v_quote_items.start_date, v_quote_items.end_date, v_quote_items.reseller_id FROM v_quote_items);--> statement-breakpoint
CREATE OR REPLACE VIEW "public"."v_documents" AS (SELECT contracts.id, 'contract'::text AS type, contracts.contract_no AS document_no, contracts.status, contracts.vendor_id, contracts.distributor_id, contracts.reseller_id, contracts.end_user_id FROM contracts UNION SELECT quotes.id, 'quote'::text AS type, quotes.quote_no AS document_no, quotes.status, quotes.vendor_id, quotes.distributor_id, quotes.reseller_id, quotes.end_user_id FROM quotes);--> statement-breakpoint
