CREATE TABLE IF NOT EXISTS "email_templates" (
	"id" uuid PRIMARY KEY NOT NULL,
	"locale" varchar(2) NOT NULL,
	"name" varchar(255) NOT NULL,
  "provider" varchar(255) NOT NULL,
	"provider_id" varchar(255) NOT NULL,
	"created_at" timestamp with time zone NOT NULL,
	"updated_at" timestamp with time zone NOT NULL,
	UNIQUE ("locale", "name", "provider")
);
INSERT INTO email_templates (id, locale, name, provider, provider_id, created_at, updated_at) VALUES
('b7588523-a23b-464a-852f-bc8e3c255ec1', 'en', 'invitation', 'brevo', '9', '2025-06-03 11:56:02.4287+00', '2025-06-03 11:56:02.4287+00'),
('17378ba0-fa42-4c9c-b66c-b1a4c9d526a3', 'en', 'password-reset', 'brevo', '6', '2025-06-03 11:53:16.17924+00', '2025-06-03 11:53:16.17924+00'),
('81946561-fb7d-467f-a8dc-e27cbea0cfd9', 'en', 'quote-approval', 'brevo', '2', '2025-06-03 11:56:02.4287+00', '2025-06-03 11:56:02.4287+00'),
('7d9f1e56-d3dc-4b44-9c7b-5ebf46360014', 'en', 'quote-new-available', 'brevo', '1', '2025-06-03 11:56:02.4287+00', '2025-06-03 11:56:02.4287+00'),
('94d7496e-025d-4194-bdff-d314dd57515a', 'en', 'quote-updated', 'brevo', '3', '2025-06-03 11:56:02.4287+00', '2025-06-03 11:56:02.4287+00'),
('813a1760-2390-46eb-a4c3-3f7d48565f14', 'en', 'sign-up-invitation', 'brevo', '8', '2025-06-03 11:56:02.4287+00', '2025-06-03 11:56:02.4287+00')
ON CONFLICT (id) DO NOTHING;