import { sql } from 'drizzle-orm';
import { pgTable, serial, timestamp, varchar } from 'drizzle-orm/pg-core';

export * from '../src/domain/addresses/address.schema';
export * from '../src/domain/assets/asset.view.schema';
export * from '../src/domain/assets/asset-enrichment.schema';
export * from '../src/domain/common/access-log.schema';
export * from '../src/domain/common/drizzle/email-template.schema';
export * from '../src/domain/common/event-log.schema';
export * from '../src/domain/common/reset-token.schema';
export * from '../src/domain/contacts/contact.schema';
export * from '../src/domain/contracts/contract.schema';
export * from '../src/domain/contracts/contract-item.schema';
export * from '../src/domain/contracts/contract-item.view.schema';
export * from '../src/domain/contracts/contract-job.schema';
export * from '../src/domain/entities/document.view.schema';
export * from '../src/domain/entities/entity.schema';
export * from '../src/domain/feature-flags/feature-flag.schema';
export * from '../src/domain/files/file.schema';
export * from '../src/domain/import/import.schema';
export * from '../src/domain/organizations/organization.schema';
export * from '../src/domain/organizations/organization-entity.schema';
export * from '../src/domain/products/product.schema';
export * from '../src/domain/quotes/file-quote-request-history.schema';
export * from '../src/domain/quotes/quote.schema';
export * from '../src/domain/quotes/quote-item.schema';
export * from '../src/domain/quotes/quote-item.view.schema';
export * from '../src/domain/quotes/quote-request-history.schema';
export * from '../src/domain/users/invitation.schema';
export * from '../src/domain/users/permission.schema';
export * from '../src/domain/users/role.schema';
export * from '../src/domain/users/role-permission.schema';
export * from '../src/domain/users/user.schema';
export * from '../src/domain/users/user-role.schema';

//todo get rid of this after dropping mikro-orm
export const mikroOrmMigrations = pgTable('mikro_orm_migrations', {
    id: serial().primaryKey().notNull(),
    name: varchar({ length: 255 }),
    executedAt: timestamp('executed_at', { withTimezone: true, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`),
});
