ALTER TYPE "action_type" ADD VALUE IF NOT EXISTS 'export' AFTER 'reject';

------------------------------

DROP VIEW IF EXISTS "public"."v_assets";
CREATE OR REPLACE VIEW v_assets AS
SELECT
    v_contract_items.id,
    v_contract_items.vendor,
    v_contract_items.product_sku,
    v_contract_items.serial_no,
    v_contract_items.contract_no,
    v_contract_items.contract_status,
    NULL::text AS quote_no,
    NULL::text AS quote_status,
    v_contract_items.start_date,
    v_contract_items.end_date,
    v_contract_items.reseller_id,
    v_contract_items.end_user_id,
    v_contract_items.distributor_id,
    v_contract_items.contract_id,
    NULL::text AS quote_id,
    v_contract_items.product_name,
    v_contract_items.reseller_price_final,
    NULL::numeric AS distributor_price_final,
    v_contract_items.end_customer_price_final,
    v_contract_items.quantity,
    v_contract_items.currency,
    v_contract_items.coverage_status,
    v_contract_items.service_group_sku,
    v_contract_items.service_group_label,
    v_contract_items.service_level_sku,
    v_contract_items.service_name
FROM
    v_contract_items
UNION
SELECT
    v_quote_items.id,
    v_quote_items.vendor,
    v_quote_items.product_sku,
    v_quote_items.serial_no,
    NULL::text AS contract_no,
    NULL::text AS contract_status,
    v_quote_items.quote_no,
    v_quote_items.quote_status,
    v_quote_items.start_date,
    v_quote_items.end_date,
    v_quote_items.reseller_id,
    v_quote_items.end_user_id,
    v_quote_items.distributor_id,
    NULL::text AS contract_id,
    v_quote_items.quote_id,
    v_quote_items.product_name,
    v_quote_items.reseller_price_final,
    v_quote_items.distributor_price_final,
    v_quote_items.end_customer_price_final,
    v_quote_items.quantity,
    v_quote_items.currency,
    v_quote_items.coverage_status,
    v_quote_items.service_group_sku,
    v_quote_items.service_group_label,
    v_quote_items.service_level_sku,
    v_quote_items.service_name
FROM
    v_quote_items;

------------------------------

CREATE OR REPLACE FUNCTION notify_quote_changes()
    RETURNS TRIGGER AS $$
DECLARE
    payload JSON;
BEGIN
    IF (TG_OP = 'INSERT') THEN
        payload := json_build_object(
                'action', 'CREATED',
                'entityId', NEW.id,
                'entity', 'Quote',
                'before', NULL,
                'after', json_build_object(
                        'id', NEW.id,
                        'status', NEW.status,
                        'ui_status', NEW.ui_status
                         )
                   );
    ELSIF (TG_OP = 'UPDATE') THEN
        payload := json_build_object(
                'action', 'UPDATED',
                'entityId', NEW.id,
                'entity', 'Quote',
                'before', json_build_object(
                        'id', OLD.id,
                        'status', OLD.status,
                        'ui_status', OLD.ui_status
                          ),
                'after', json_build_object(
                        'id', NEW.id,
                        'status', NEW.status,
                        'ui_status', NEW.ui_status
                         )
                   );
    ELSIF (TG_OP = 'DELETE') THEN
        payload := json_build_object(
                'action', 'DELETED',
                'entityId', OLD.id,
                'entity', 'Quote',
                'before', json_build_object(
                        'id', OLD.id,
                        'status', OLD.status,
                        'ui_status', OLD.ui_status
                          ),
                'after', NULL
                   );
    END IF;

    PERFORM pg_notify('data_changes', payload::text);
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS quotes_data_change_trigger ON quotes;
CREATE TRIGGER quotes_data_change_trigger
    AFTER INSERT OR UPDATE OR DELETE ON quotes
    FOR EACH ROW
EXECUTE FUNCTION notify_quote_changes();

-----------------------------

CREATE OR REPLACE FUNCTION update_contracts_ui_status()
    RETURNS TRIGGER AS
$$
BEGIN
    IF NEW.status = 'Not Validated' OR NEW.status = 'Archived' THEN
        NEW.ui_status := 'Invalid';
    ELSIF NEW.status = 'Active' THEN
        NEW.ui_status := 'Active';
    ELSIF NEW.status = 'Expired' THEN
        NEW.ui_status := 'Expired';
    ELSIF NEW.status = 'Terminated' THEN
        NEW.ui_status := 'Terminated';
    ELSIF NEW.status = 'Renewed' THEN
        NEW.ui_status := 'Renewed';
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS contracts_status_update_trigger ON contracts;
CREATE TRIGGER contracts_status_update_trigger
    BEFORE UPDATE OF status
    ON contracts
    FOR EACH ROW
    WHEN (OLD.status IS DISTINCT FROM NEW.status)
EXECUTE FUNCTION update_contracts_ui_status();

-----------------------------

CREATE OR REPLACE FUNCTION update_quotes_ui_status()
    RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status IN ('In Preparation', 'Created', 'Preparation', 'Quote Ready', 'Cancelled',
                      'Archived', 'Renewed', 'Expired') THEN
        NEW.ui_status := 'Invalid';

    ELSIF NEW.status IN ('Customer Contact', 'Expires in 90 days', 'Expires in 60 days', 'Expires in 30 days', 'Active') THEN
        NEW.ui_status := 'Open';

    ELSIF NEW.status IN ('Ready to Order', 'Order Confirmed', 'Invoiced', 'Customer Order') THEN
        NEW.ui_status := 'Ordered';

    ELSIF NEW.status = 'Lost' THEN
        NEW.ui_status := 'Lost';
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS quotes_status_update_trigger ON quotes;
CREATE TRIGGER quotes_status_update_trigger
    BEFORE UPDATE OF status ON quotes
    FOR EACH ROW
    WHEN (OLD.status IS DISTINCT FROM NEW.status)
EXECUTE FUNCTION update_quotes_ui_status();
