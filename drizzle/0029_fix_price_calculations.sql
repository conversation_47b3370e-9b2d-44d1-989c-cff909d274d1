DROP VIEW "public"."v_assets"; --> statement-breakpoint
DROP VIEW "public"."v_contract_items";--> statement-breakpoint
DROP VIEW "public"."v_quote_items";--> statement-breakpoint

CREATE VIEW "public"."v_contract_items" AS (SELECT ci.id, ci.item_no, ci.contract_id, ci.vendor_id, ci.serial_no, ci.product_sku, ci.service_level_sku, ci.service_group_sku, ci.reseller_price, ci.end_customer_price, ci.quantity, ci.reseller_price AS reseller_price_final, ci.end_customer_price AS end_customer_price_final, ci.data, ci.service_group_label, cp.description AS product_name, sp.description AS service_name, c.contract_no, c.status AS contract_status, ae.coverage_status, c.currency, c.distributor_id, c.end_user_id, c.reseller_id, (ci.data ->> 'SupportLifeEndDate') AS support_life_end_date, (ci.data ->> 'ServiceStartDate') AS start_date, (ci.data ->> 'ServiceEndDate') AS end_date FROM contract_items ci LEFT JOIN products cp ON cp.sku = ci.product_sku AND cp.vendor_id = ci.vendor_id LEFT JOIN products sp ON sp.sku = ci.service_level_sku AND sp.vendor_id = ci.vendor_id LEFT JOIN contracts c ON c.id = ci.contract_id LEFT JOIN asset_enrichments ae ON ae.serial_number = ci.serial_no AND ae.product_sku = ci.product_sku);--> statement-breakpoint
CREATE VIEW "public"."v_quote_items" AS (SELECT qi.id, qi.item_no, qi.quote_id, qi.vendor_id, qi.serial_no, qi.product_sku, qi.service_level_sku, qi.service_group_sku, qi.reseller_price, qi.distributor_price, qi.end_customer_price, qi.quantity, qi.reseller_price AS reseller_price_final, qi.distributor_price AS distributor_price_final, qi.end_customer_price AS end_customer_price_final, qi.data, qi.service_group_label, cp.description AS product_name, sp.description AS service_name, q.quote_no, q.status AS quote_status, ae.coverage_status, q.currency, q.distributor_id, q.end_user_id, q.reseller_id, (qi.data ->> 'SupportLifeEndDate') AS support_life_end_date, (qi.data ->> 'StartDate') AS start_date, (qi.data ->> 'EndDate') AS end_date FROM quote_items qi LEFT JOIN products cp ON cp.sku = qi.product_sku AND cp.vendor_id = qi.vendor_id LEFT JOIN products sp ON sp.sku = qi.service_level_sku AND sp.vendor_id = qi.vendor_id LEFT JOIN quotes q ON q.id = qi.quote_id LEFT JOIN asset_enrichments ae ON ae.serial_number = qi.serial_no AND ae.product_sku = qi.product_sku);
CREATE OR REPLACE VIEW "public"."v_assets" AS (
    SELECT
        v_contract_items.id,
        v_contract_items.vendor_id,
        v_contract_items.product_sku,
        v_contract_items.serial_no,
        v_contract_items.contract_no,
        v_contract_items.contract_status,
        NULL::text AS quote_no,
        NULL::text AS quote_status,
        v_contract_items.start_date,
        v_contract_items.end_date,
        v_contract_items.reseller_id,
        v_contract_items.end_user_id,
        v_contract_items.distributor_id,
        v_contract_items.contract_id,
        NULL::text AS quote_id,
        v_contract_items.product_name,
        v_contract_items.reseller_price_final,
        NULL::numeric AS distributor_price_final,
        v_contract_items.end_customer_price_final,
        v_contract_items.quantity,
        v_contract_items.currency,
        v_contract_items.coverage_status,
        v_contract_items.service_group_sku,
        v_contract_items.service_group_label,
        v_contract_items.service_level_sku,
        v_contract_items.service_name,
        v_contract_items.support_life_end_date
    FROM
        v_contract_items
    UNION
    SELECT
        v_quote_items.id,
        v_quote_items.vendor_id,
        v_quote_items.product_sku,
        v_quote_items.serial_no,
        NULL::text AS contract_no,
        NULL::text AS contract_status,
        v_quote_items.quote_no,
        v_quote_items.quote_status,
        v_quote_items.start_date,
        v_quote_items.end_date,
        v_quote_items.reseller_id,
        v_quote_items.end_user_id,
        v_quote_items.distributor_id,
        NULL::text AS contract_id,
        v_quote_items.quote_id,
        v_quote_items.product_name,
        v_quote_items.reseller_price_final,
        v_quote_items.distributor_price_final,
        v_quote_items.end_customer_price_final,
        v_quote_items.quantity,
        v_quote_items.currency,
        v_quote_items.coverage_status,
        v_quote_items.service_group_sku,
        v_quote_items.service_group_label,
        v_quote_items.service_level_sku,
        v_quote_items.service_name,
        v_quote_items.support_life_end_date
    FROM
        v_quote_items
        );--> statement-breakpoint

