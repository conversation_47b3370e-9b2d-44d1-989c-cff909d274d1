ALTER TABLE "organizations" ADD COLUMN "adv_perms" boolean DEFAULT false NOT NULL;

CREATE MATERIALIZED VIEW IF NOT EXISTS public.mv_adv_permissions AS
WITH
    c_latest AS (
        SELECT DISTINCT ON (contracts.reseller_id, contracts.end_user_id)
            contracts.reseller_id,
            contracts.end_user_id,
            contracts.start_date,
            (
                SELECT contact.value ->> 'EmailAddress'::text
                FROM jsonb_array_elements(
                    CASE
                        WHEN jsonb_typeof(contracts.data -> 'ContractContacts'::text) = 'array'::text
                        THEN contracts.data -> 'ContractContacts'::text
                        ELSE '[]'::jsonb
                    END
                ) contact(value)
                WHERE (
                    contact.value ->> 'EntityId'::text) = contracts.reseller_id
                    AND ((contact.value ->> 'Active'::text)::boolean)
                    AND COALESCE(contact.value ->> 'EmailAddress'::text, ''::text) <> ''::text
                ORDER BY (contact.value ->> 'EmailAddress'::text)
                LIMIT 1
            ) AS email_address

        FROM contracts
        ORDER BY contracts.reseller_id, contracts.end_user_id, contracts.start_date DESC
    ),

    q_latest AS (
        SELECT DISTINCT ON (q.reseller_id, q.end_user_id)
            q.reseller_id,
            q.end_user_id,
            q.start_date,
            cn.data ->> 'EmailAddress'::text AS email_address
        FROM quotes q
        JOIN contacts cn ON cn.id = (q.data ->> 'CustomerContactId'::text)
        WHERE COALESCE(cn.data ->> 'EmailAddress'::text, ''::text) <> ''::text
        ORDER BY q.reseller_id, q.end_user_id, q.start_date DESC),

    combined AS (
        SELECT
            c_latest.reseller_id,
            c_latest.end_user_id,
            c_latest.start_date,
            c_latest.email_address
        FROM c_latest
        UNION ALL
        SELECT
            q_latest.reseller_id,
            q_latest.end_user_id,
            q_latest.start_date,
            q_latest.email_address
        FROM q_latest
    )

    SELECT
        DISTINCT ON (combined.reseller_id, combined.end_user_id) combined.reseller_id,
        combined.end_user_id,
        combined.start_date,
        combined.email_address,
        u.id AS user_id,
        u.keycloak_id
    FROM combined
    LEFT JOIN users u ON lower(u.email::text) = lower(combined.email_address)
    ORDER BY combined.reseller_id, combined.end_user_id, combined.start_date DESC;

CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_adv_permissions ON public.mv_adv_permissions (reseller_id, end_user_id);
