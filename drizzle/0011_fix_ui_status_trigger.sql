-- Custom SQL migration file, put your code below! --
DROP TRIGGER IF EXISTS contracts_status_update_trigger ON contracts;
CREATE TRIGGER contracts_status_update_trigger
    BEFORE INSERT OR UPDATE OF status
    ON contracts
    FOR EACH ROW
EXECUTE FUNCTION update_contracts_ui_status();

DROP TRIGGER IF EXISTS quotes_status_update_trigger ON quotes;
CREATE TRIGGER quotes_status_update_trigger
    BEFORE INSERT OR UPDATE OF status
    ON quotes
    FOR EACH ROW
EXECUTE FUNCTION update_quotes_ui_status();
