CREATE TABLE "contract_jobs" (
	"id" text PRIMARY KEY NOT NULL,
	"group_id" text,
	"status" text,
	"expires_at" timestamp with time zone,
	"data" jsonb,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE INDEX "idx_contract_jobs_g_id" ON "contract_jobs" USING btree ("group_id" text_ops);--> statement-breakpoint
CREATE INDEX "idx_contract_jobs_s_t" ON "contract_jobs" USING btree ("status" text_ops);--> statement-breakpoint