ALTER TABLE "contracts" ADD COLUMN "created_at" timestamp with time zone DEFAULT now() NOT NULL;--> statement-breakpoint
ALTER TABLE "contracts" ADD COLUMN "updated_at" timestamp with time zone DEFAULT now() NOT NULL;--> statement-breakpoint
ALTER TABLE "contract_items" ADD COLUMN "created_at" timestamp with time zone DEFAULT now() NOT NULL;--> statement-breakpoint
ALTER TABLE "contract_items" ADD COLUMN "updated_at" timestamp with time zone DEFAULT now() NOT NULL;--> statement-breakpoint
ALTER TABLE "quotes" ADD COLUMN "created_at" timestamp with time zone DEFAULT now() NOT NULL;--> statement-breakpoint
ALTER TABLE "quote_items" ADD COLUMN "created_at" timestamp with time zone DEFAULT now() NOT NULL;--> statement-breakpoint
ALTER TABLE "quote_items" ADD COLUMN "updated_at" timestamp with time zone DEFAULT now() NOT NULL;--> statement-breakpoint
--> statement-breakpoint
CREATE TRIGGER update_contracts_timestamp
    BEFORE UPDATE ON contracts
    FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
--> statement-breakpoint
CREATE TRIGGER update_contract_items_timestamp
    BEFORE UPDATE ON contract_items
    FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
--> statement-breakpoint
CREATE TRIGGER update_quotes_timestamp
    BEFORE UPDATE ON quotes
    FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
--> statement-breakpoint
CREATE TRIGGER update_quote_items_timestamp
    BEFORE UPDATE ON quote_items
    FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
