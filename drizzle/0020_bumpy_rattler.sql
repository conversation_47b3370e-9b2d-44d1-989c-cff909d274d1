-- Safely recreate import_status enum with updated values
DO $$
BEGIN
    -- Check if enum exists and handle appropriately
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'import_status') THEN
        -- Check if it has the old 'parsed' value or already has correct values
        IF EXISTS (SELECT 1 FROM pg_enum WHERE enumlabel = 'parsed' AND enumtypid = 'import_status'::regtype) THEN
            -- Has old values, need to recreate
            ALTER TABLE "imports" ALTER COLUMN "import_status" DROP DEFAULT;
            ALTER TABLE "imports" ALTER COLUMN "import_status" SET DATA TYPE text;
            DROP TYPE "public"."import_status";
            CREATE TYPE "public"."import_status" AS ENUM('created', 'validated', 'imported');
            ALTER TABLE "imports" ALTER COLUMN "import_status" SET DATA TYPE import_status USING "import_status"::text::import_status;
            ALTER TABLE "imports" ALTER COLUMN "import_status" SET DEFAULT 'created';
        ELSIF NOT EXISTS (SELECT 1 FROM pg_enum WHERE enumlabel = 'created' AND enumtypid = 'import_status'::regtype) THEN
            -- Enum exists but doesn't have correct values, recreate
            ALTER TABLE "imports" ALTER COLUMN "import_status" DROP DEFAULT;
            ALTER TABLE "imports" ALTER COLUMN "import_status" SET DATA TYPE text;
            DROP TYPE "public"."import_status";
            CREATE TYPE "public"."import_status" AS ENUM('created', 'validated', 'imported');
            ALTER TABLE "imports" ALTER COLUMN "import_status" SET DATA TYPE import_status USING "import_status"::text::import_status;
            ALTER TABLE "imports" ALTER COLUMN "import_status" SET DEFAULT 'created';
        END IF;
        -- If enum already has correct values, do nothing
    ELSE
        -- Enum doesn't exist, create it
        CREATE TYPE "public"."import_status" AS ENUM('created', 'validated', 'imported');
    END IF;
END
$$;

