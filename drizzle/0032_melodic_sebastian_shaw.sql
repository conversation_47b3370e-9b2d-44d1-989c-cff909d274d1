ALTER TABLE "event_log" ADD COLUMN "changes" jsonb;
ALTER TABLE "event_log" DROP COLUMN "before_data";
ALTER TABLE "event_log" DROP COLUMN "after_data";

CREATE OR REPLACE FUNCTION json_diff(old_data JSONB, new_data JSONB)
RETURNS JSONB AS $$
DECLARE
    key TEXT;
    result JSONB := '{}'::JSONB;
    old_value JSONB;
    new_value JSONB;
BEGIN
    IF old_data IS NULL THEN
        RETURN jsonb_build_object('type', 'created', 'data', new_data);
    END IF;
    
    IF new_data IS NULL THEN
        RETURN jsonb_build_object('type', 'deleted', 'data', old_data);
    END IF;
    
    result := jsonb_build_object('type', 'updated', 'changed_fields', '{}'::JSONB);
    
    FOR key IN SELECT jsonb_object_keys(new_data)
    LOOP
        old_value := old_data->key;
        new_value := new_data->key;
        
        IF old_value IS NULL OR old_value != new_value THEN
            result := jsonb_set(
                result,
                ARRAY['changed_fields', key],
                jsonb_build_object(
                    'old', old_value,
                    'new', new_value
                )
            );
        END IF;
    END LOOP;
    
    FOR key IN SELECT jsonb_object_keys(old_data)
    LOOP
        IF NOT new_data ? key THEN
            result := jsonb_set(
                result,
                ARRAY['changed_fields', key],
                jsonb_build_object(
                    'old', old_data->key,
                    'new', NULL
                )
            );
        END IF;
    END LOOP;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION notify_data_changes()
RETURNS TRIGGER AS $$
DECLARE
    payload JSON;
    entity_name TEXT;
    org_id UUID;
    action_type event_log_action;
    changes_data JSONB;
    entity_id_val TEXT;
    old_data JSONB;
    new_data JSONB;
BEGIN
    entity_name := INITCAP(TRIM(TRAILING 's' FROM TG_TABLE_NAME));

    IF (TG_OP = 'INSERT' OR TG_OP = 'UPDATE') THEN
        BEGIN
            org_id := (NEW.organization_id)::UUID;
        EXCEPTION WHEN OTHERS THEN
            org_id := NULL;
        END;
    ELSIF (TG_OP = 'DELETE') THEN
        BEGIN
            org_id := (OLD.organization_id)::UUID;
        EXCEPTION WHEN OTHERS THEN
            org_id := NULL;
        END;
    END IF;

    IF (TG_OP = 'INSERT') THEN
        action_type := 'CREATED';
        entity_id_val := NEW.id;
        new_data := row_to_json(NEW)::JSONB;
        changes_data := json_diff(NULL, new_data);
        
        payload := json_build_object(
            'action', 'CREATED',
            'entityId', NEW.id,
            'entity', entity_name,
            'changes', changes_data
        );
    ELSIF (TG_OP = 'UPDATE') THEN
        action_type := 'UPDATED';
        entity_id_val := NEW.id;
        old_data := row_to_json(OLD)::JSONB;
        new_data := row_to_json(NEW)::JSONB;
        changes_data := json_diff(old_data, new_data);
        
        payload := json_build_object(
            'action', 'UPDATED',
            'entityId', NEW.id,
            'entity', entity_name,
            'changes', changes_data
        );
    ELSIF (TG_OP = 'DELETE') THEN
        action_type := 'DELETED';
        entity_id_val := OLD.id;
        old_data := row_to_json(OLD)::JSONB;
        changes_data := json_diff(old_data, NULL);
        
        payload := json_build_object(
            'action', 'DELETED',
            'entityId', OLD.id,
            'entity', entity_name,
            'changes', changes_data
        );
    END IF;

    BEGIN
        INSERT INTO event_log (
            entity,
            entity_id,
            action,
            changes,
            organization_id,
            created_at,
            status
        ) VALUES (
            entity_name,
            entity_id_val,
            action_type,
            changes_data,
            org_id,
            CURRENT_TIMESTAMP,
            'pending'
        );
    EXCEPTION WHEN OTHERS THEN
        RAISE WARNING 'Failed to insert into event_log: %', SQLERRM;
    END;

    PERFORM pg_notify('data_changes', payload::text);
    RETURN NULL;
END;
$$ LANGUAGE plpgsql; 