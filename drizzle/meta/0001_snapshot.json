{"id": "362c222d-0878-4f64-9acc-8bb480bd8601", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.mikro_orm_migrations": {"name": "mikro_orm_migrations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "executed_at": {"name": "executed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.addresses": {"name": "addresses", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "entity_id": {"name": "entity_id", "type": "text", "primaryKey": false, "notNull": true}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.asset_enrichments": {"name": "asset_enrichments", "schema": "", "columns": {"serial_number": {"name": "serial_number", "type": "text", "primaryKey": false, "notNull": true}, "product_sku": {"name": "product_sku", "type": "text", "primaryKey": false, "notNull": true}, "country_code": {"name": "country_code", "type": "text", "primaryKey": false, "notNull": false}, "is_enriched": {"name": "is_enriched", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "coverage_status": {"name": "coverage_status", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"asset_enrichments_pkey": {"name": "asset_enrichments_pkey", "columns": ["serial_number", "product_sku"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.access_log": {"name": "access_log", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "type": {"name": "type", "type": "access_log_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "url_path": {"name": "url_path", "type": "text", "primaryKey": false, "notNull": true}, "payload": {"name": "payload", "type": "jsonb", "primaryKey": false, "notNull": false}, "header": {"name": "header", "type": "jsonb", "primaryKey": false, "notNull": true}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.reset_tokens": {"name": "reset_tokens", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.contacts": {"name": "contacts", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "entity_id": {"name": "entity_id", "type": "text", "primaryKey": false, "notNull": true}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "contact_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'inactive'"}}, "indexes": {"idx_contacts_e_id": {"name": "idx_contacts_e_id", "columns": [{"expression": "entity_id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.contracts": {"name": "contracts", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "contract_no": {"name": "contract_no", "type": "text", "primaryKey": false, "notNull": false}, "sar": {"name": "sar", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false}, "group_id": {"name": "group_id", "type": "text", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "text", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "text", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false}, "vendor_id": {"name": "vendor_id", "type": "text", "primaryKey": false, "notNull": false}, "distributor_id": {"name": "distributor_id", "type": "text", "primaryKey": false, "notNull": false}, "reseller_id": {"name": "reseller_id", "type": "text", "primaryKey": false, "notNull": false}, "end_user_id": {"name": "end_user_id", "type": "text", "primaryKey": false, "notNull": false}, "reseller_total_price": {"name": "reseller_total_price", "type": "numeric(20, 4)", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": false}, "ui_status": {"name": "ui_status", "type": "contract_ui_status", "typeSchema": "public", "primaryKey": false, "notNull": false}, "end_customer_total_price": {"name": "end_customer_total_price", "type": "numeric(20, 4)", "primaryKey": false, "notNull": false}}, "indexes": {"contracts_contract_no_index": {"name": "contracts_contract_no_index", "columns": [{"expression": "contract_no", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "contracts_distributor_id_index": {"name": "contracts_distributor_id_index", "columns": [{"expression": "distributor_id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "contracts_end_user_id_index": {"name": "contracts_end_user_id_index", "columns": [{"expression": "end_user_id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "contracts_reseller_id_index": {"name": "contracts_reseller_id_index", "columns": [{"expression": "reseller_id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "contracts_ui_status_index": {"name": "contracts_ui_status_index", "columns": [{"expression": "ui_status", "isExpression": false, "asc": true, "nulls": "last", "opclass": "enum_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "contracts_vendor_id_index": {"name": "contracts_vendor_id_index", "columns": [{"expression": "vendor_id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_contracts_r_id": {"name": "idx_contracts_r_id", "columns": [{"expression": "reseller_id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.contract_items": {"name": "contract_items", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "item_no": {"name": "item_no", "type": "integer", "primaryKey": false, "notNull": false}, "contract_id": {"name": "contract_id", "type": "text", "primaryKey": false, "notNull": false}, "vendor": {"name": "vendor", "type": "text", "primaryKey": false, "notNull": false}, "serial_no": {"name": "serial_no", "type": "text", "primaryKey": false, "notNull": false}, "product_sku": {"name": "product_sku", "type": "text", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": false}, "service_level_sku": {"name": "service_level_sku", "type": "text", "primaryKey": false, "notNull": false}, "service_group_sku": {"name": "service_group_sku", "type": "text", "primaryKey": false, "notNull": false}, "service_group_label": {"name": "service_group_label", "type": "text", "primaryKey": false, "notNull": false}, "reseller_price": {"name": "reseller_price", "type": "numeric(20, 4)", "primaryKey": false, "notNull": false}, "end_customer_price": {"name": "end_customer_price", "type": "numeric(20, 4)", "primaryKey": false, "notNull": false}, "quantity": {"name": "quantity", "type": "numeric(10, 0)", "primaryKey": false, "notNull": false}}, "indexes": {"idx_contract_items_c_id": {"name": "idx_contract_items_c_id", "columns": [{"expression": "contract_id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_contract_items_p_sku": {"name": "idx_contract_items_p_sku", "columns": [{"expression": "product_sku", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_contract_items_s_no": {"name": "idx_contract_items_s_no", "columns": [{"expression": "serial_no", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.entities": {"name": "entities", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "type": {"name": "type", "type": "entity_type", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "owner_id": {"name": "owner_id", "type": "text", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.files": {"name": "files", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.organizations": {"name": "organizations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "organization_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'inactive'"}, "emailable": {"name": "emailable", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"organizations_parent_id_foreign": {"name": "organizations_parent_id_foreign", "tableFrom": "organizations", "tableTo": "organizations", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.organization_entities": {"name": "organization_entities", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "entity_id": {"name": "entity_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"organization_entities_organization_id_foreign": {"name": "organization_entities_organization_id_foreign", "tableFrom": "organization_entities", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "cascade"}, "organization_entities_entity_id_foreign": {"name": "organization_entities_entity_id_foreign", "tableFrom": "organization_entities", "tableTo": "entities", "columnsFrom": ["entity_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"organization_entities_organization_id_unique": {"name": "organization_entities_organization_id_unique", "nullsNotDistinct": false, "columns": ["organization_id"]}, "organization_entities_entity_id_unique": {"name": "organization_entities_entity_id_unique", "nullsNotDistinct": false, "columns": ["entity_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.products": {"name": "products", "schema": "", "columns": {"vendor": {"name": "vendor", "type": "text", "primaryKey": false, "notNull": true}, "sku": {"name": "sku", "type": "text", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": false}, "show_in_dropdown": {"name": "show_in_dropdown", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "weight": {"name": "weight", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"products_pkey": {"name": "products_pkey", "columns": ["vendor", "sku"]}}, "uniqueConstraints": {"products_vendor_sku_unique": {"name": "products_vendor_sku_unique", "nullsNotDistinct": false, "columns": ["vendor", "sku"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.file_quote_request_histories": {"name": "file_quote_request_histories", "schema": "", "columns": {"file_id": {"name": "file_id", "type": "uuid", "primaryKey": false, "notNull": true}, "quote_request_history_id": {"name": "quote_request_history_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"file_quote_request_histories_file_id_foreign": {"name": "file_quote_request_histories_file_id_foreign", "tableFrom": "file_quote_request_histories", "tableTo": "files", "columnsFrom": ["file_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "file_quote_request_histories_quote_request_history_id_foreign": {"name": "file_quote_request_histories_quote_request_history_id_foreign", "tableFrom": "file_quote_request_histories", "tableTo": "quote_request_histories", "columnsFrom": ["quote_request_history_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"file_quote_request_histories_pkey": {"name": "file_quote_request_histories_pkey", "columns": ["file_id", "quote_request_history_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.quotes": {"name": "quotes", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "quote_no": {"name": "quote_no", "type": "text", "primaryKey": false, "notNull": false}, "quote_type": {"name": "quote_type", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false}, "group_id": {"name": "group_id", "type": "text", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "text", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "text", "primaryKey": false, "notNull": false}, "expiry_date": {"name": "expiry_date", "type": "text", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false}, "vendor_id": {"name": "vendor_id", "type": "text", "primaryKey": false, "notNull": false}, "distributor_id": {"name": "distributor_id", "type": "text", "primaryKey": false, "notNull": false}, "reseller_id": {"name": "reseller_id", "type": "text", "primaryKey": false, "notNull": false}, "end_user_id": {"name": "end_user_id", "type": "text", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": false}, "ui_status": {"name": "ui_status", "type": "quote_ui_status", "primaryKey": false, "notNull": false}, "reseller_total_price": {"name": "reseller_total_price", "type": "numeric(20, 4)", "primaryKey": false, "notNull": false}, "distributor_total_price": {"name": "distributor_total_price", "type": "numeric(20, 4)", "primaryKey": false, "notNull": false}, "end_customer_total_price": {"name": "end_customer_total_price", "type": "numeric(20, 4)", "primaryKey": false, "notNull": false}}, "indexes": {"idx_quotes_r_id": {"name": "idx_quotes_r_id", "columns": [{"expression": "reseller_id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "quotes_distributor_id_index": {"name": "quotes_distributor_id_index", "columns": [{"expression": "distributor_id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "quotes_end_user_id_index": {"name": "quotes_end_user_id_index", "columns": [{"expression": "end_user_id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "quotes_vendor_id_index": {"name": "quotes_vendor_id_index", "columns": [{"expression": "vendor_id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.quote_items": {"name": "quote_items", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "item_no": {"name": "item_no", "type": "integer", "primaryKey": false, "notNull": false}, "quote_id": {"name": "quote_id", "type": "text", "primaryKey": false, "notNull": false}, "vendor": {"name": "vendor", "type": "text", "primaryKey": false, "notNull": false}, "serial_no": {"name": "serial_no", "type": "text", "primaryKey": false, "notNull": false}, "product_sku": {"name": "product_sku", "type": "text", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": false}, "source_contract_item_id": {"name": "source_contract_item_id", "type": "text", "primaryKey": false, "notNull": false}, "resulting_contract_item_id": {"name": "resulting_contract_item_id", "type": "text", "primaryKey": false, "notNull": false}, "service_level_sku": {"name": "service_level_sku", "type": "text", "primaryKey": false, "notNull": false}, "service_group_sku": {"name": "service_group_sku", "type": "text", "primaryKey": false, "notNull": false}, "service_group_label": {"name": "service_group_label", "type": "text", "primaryKey": false, "notNull": false}, "reseller_price": {"name": "reseller_price", "type": "numeric(20, 4)", "primaryKey": false, "notNull": false}, "distributor_price": {"name": "distributor_price", "type": "numeric(20, 4)", "primaryKey": false, "notNull": false}, "end_customer_price": {"name": "end_customer_price", "type": "numeric(20, 4)", "primaryKey": false, "notNull": false}, "quantity": {"name": "quantity", "type": "numeric(10, 0)", "primaryKey": false, "notNull": false}}, "indexes": {"idx_quote_items_ecp": {"name": "idx_quote_items_ecp", "columns": [{"expression": "end_customer_price", "isExpression": false, "asc": true, "nulls": "last", "opclass": "numeric_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_quote_items_p_sku": {"name": "idx_quote_items_p_sku", "columns": [{"expression": "product_sku", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_quote_items_q_id": {"name": "idx_quote_items_q_id", "columns": [{"expression": "quote_id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_quote_items_rci": {"name": "idx_quote_items_rci", "columns": [{"expression": "resulting_contract_item_id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_quote_items_sci": {"name": "idx_quote_items_sci", "columns": [{"expression": "source_contract_item_id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_quote_items_sno": {"name": "idx_quote_items_sno", "columns": [{"expression": "serial_no", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_quote_items_v_p_sku": {"name": "idx_quote_items_v_p_sku", "columns": [{"expression": "vendor", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}, {"expression": "product_sku", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_quote_items_v_sl_sku": {"name": "idx_quote_items_v_sl_sku", "columns": [{"expression": "vendor", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}, {"expression": "service_level_sku", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.quote_request_histories": {"name": "quote_request_histories", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "action": {"name": "action", "type": "text", "primaryKey": false, "notNull": true}, "request": {"name": "request", "type": "jsonb", "primaryKey": false, "notNull": true}, "quote_id": {"name": "quote_id", "type": "text", "primaryKey": false, "notNull": true}, "triggered_by": {"name": "triggered_by", "type": "jsonb", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"quote_request_histories_quote_id_foreign": {"name": "quote_request_histories_quote_id_foreign", "tableFrom": "quote_request_histories", "tableTo": "quotes", "columnsFrom": ["quote_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"quote_request_histories_action_check": {"name": "quote_request_histories_action_check", "value": "action = ANY (ARRAY['APPROVAL'::text, 'DECLINE'::text, 'REQUEST_CHANGE'::text, 'REQUEST_QUOTE'::text])"}}, "isRLSEnabled": false}, "public.invitations": {"name": "invitations", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "organization_ids": {"name": "organization_ids", "type": "jsonb", "primaryKey": false, "notNull": true}, "role_id": {"name": "role_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "is_accepted": {"name": "is_accepted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.permissions": {"name": "permissions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "resource_type": {"name": "resource_type", "type": "resource_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "action_type": {"name": "action_type", "type": "action_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.roles": {"name": "roles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "visible": {"name": "visible", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}}, "indexes": {}, "foreignKeys": {"roles_organization_id_foreign": {"name": "roles_organization_id_foreign", "tableFrom": "roles", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.role_permissions": {"name": "role_permissions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "role_id": {"name": "role_id", "type": "uuid", "primaryKey": false, "notNull": true}, "permission_id": {"name": "permission_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"role_permissions_role_id_foreign": {"name": "role_permissions_role_id_foreign", "tableFrom": "role_permissions", "tableTo": "roles", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "cascade"}, "role_permissions_permission_id_foreign": {"name": "role_permissions_permission_id_foreign", "tableFrom": "role_permissions", "tableTo": "permissions", "columnsFrom": ["permission_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "keycloak_id": {"name": "keycloak_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "last_login_at": {"name": "last_login_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "locale": {"name": "locale", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "default": "'en'"}, "timezone": {"name": "timezone", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "default": "'UTC'"}}, "indexes": {"users_keycloak_id_index": {"name": "users_keycloak_id_index", "columns": [{"expression": "keycloak_id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_keycloak_id_unique": {"name": "users_keycloak_id_unique", "nullsNotDistinct": false, "columns": ["keycloak_id"]}, "users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_roles": {"name": "user_roles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role_id": {"name": "role_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"user_roles_user_id_foreign": {"name": "user_roles_user_id_foreign", "tableFrom": "user_roles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "cascade"}, "user_roles_role_id_foreign": {"name": "user_roles_role_id_foreign", "tableFrom": "user_roles", "tableTo": "roles", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.access_log_type": {"name": "access_log_type", "schema": "public", "values": ["lost", "invalid", "success"]}, "public.contact_status": {"name": "contact_status", "schema": "public", "values": ["active", "inactive"]}, "public.contract_ui_status": {"name": "contract_ui_status", "schema": "public", "values": ["Invalid", "Active", "Expiring", "Expired", "Terminated", "Renewed"]}, "public.entity_type": {"name": "entity_type", "schema": "public", "values": ["<PERSON><PERSON><PERSON>", "Distributor", "Reseller", "EndUser"]}, "public.organization_status": {"name": "organization_status", "schema": "public", "values": ["active", "inactive"]}, "public.quote_ui_status": {"name": "quote_ui_status", "schema": "public", "values": ["Invalid", "Open", "Change Requested", "Ordered", "Lost"]}, "public.action_type": {"name": "action_type", "schema": "public", "values": ["create", "read", "update", "approve", "reject", "manage", "export"]}, "public.resource_type": {"name": "resource_type", "schema": "public", "values": ["quote", "quote_item", "contract", "contract_item", "entity", "organization", "user", "role", "contact", "asset", "platform", "user_organization"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {"public.v_assets": {"columns": {"id": {"name": "id", "type": "text", "primaryKey": false, "notNull": false}, "vendor": {"name": "vendor", "type": "text", "primaryKey": false, "notNull": false}, "product_sku": {"name": "product_sku", "type": "text", "primaryKey": false, "notNull": false}, "serial_no": {"name": "serial_no", "type": "text", "primaryKey": false, "notNull": false}, "contract_no": {"name": "contract_no", "type": "text", "primaryKey": false, "notNull": false}, "contract_status": {"name": "contract_status", "type": "text", "primaryKey": false, "notNull": false}, "service_group_sku": {"name": "service_group_sku", "type": "text", "primaryKey": false, "notNull": false}, "service_group_label": {"name": "service_group_label", "type": "text", "primaryKey": false, "notNull": false}, "service_level_sku": {"name": "service_level_sku", "type": "text", "primaryKey": false, "notNull": false}, "service_name": {"name": "service_name", "type": "text", "primaryKey": false, "notNull": false}, "coverage_status": {"name": "coverage_status", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "quote_no": {"name": "quote_no", "type": "text", "primaryKey": false, "notNull": false}, "quote_status": {"name": "quote_status", "type": "text", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "text", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "text", "primaryKey": false, "notNull": false}, "reseller_id": {"name": "reseller_id", "type": "text", "primaryKey": false, "notNull": false}, "end_user_id": {"name": "end_user_id", "type": "text", "primaryKey": false, "notNull": false}, "distributor_id": {"name": "distributor_id", "type": "text", "primaryKey": false, "notNull": false}, "contract_id": {"name": "contract_id", "type": "text", "primaryKey": false, "notNull": false}, "quote_id": {"name": "quote_id", "type": "text", "primaryKey": false, "notNull": false}, "product_name": {"name": "product_name", "type": "text", "primaryKey": false, "notNull": false}, "reseller_price_final": {"name": "reseller_price_final", "type": "numeric", "primaryKey": false, "notNull": false}, "distributor_price_final": {"name": "distributor_price_final", "type": "numeric", "primaryKey": false, "notNull": false}, "end_customer_price_final": {"name": "end_customer_price_final", "type": "numeric", "primaryKey": false, "notNull": false}, "quantity": {"name": "quantity", "type": "numeric", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false}}, "definition": "\n        CREATE OR REPLACE VIEW v_assets AS\n        SELECT\n            v_contract_items.id,\n            v_contract_items.vendor,\n            v_contract_items.product_sku,\n            v_contract_items.serial_no,\n            v_contract_items.contract_no,\n            v_contract_items.contract_status,\n            NULL::text AS quote_no,\n            NULL::text AS quote_status,\n            v_contract_items.start_date,\n            v_contract_items.end_date,\n            v_contract_items.reseller_id,\n            v_contract_items.end_user_id,\n            v_contract_items.distributor_id,\n            v_contract_items.contract_id,\n            NULL::text AS quote_id,\n            v_contract_items.product_name,\n            v_contract_items.reseller_price_final,\n            NULL::numeric AS distributor_price_final,\n            v_contract_items.end_customer_price_final,\n            v_contract_items.quantity,\n            v_contract_items.currency,\n            v_contract_items.coverage_status,\n            v_contract_items.service_group_sku,\n            v_contract_items.service_group_label,\n            v_contract_items.service_level_sku,\n            v_contract_items.service_name\n        FROM\n            v_contract_items\n        UNION\n        SELECT\n            v_quote_items.id,\n            v_quote_items.vendor,\n            v_quote_items.product_sku,\n            v_quote_items.serial_no,\n            NULL::text AS contract_no,\n            NULL::text AS contract_status,\n            v_quote_items.quote_no,\n            v_quote_items.quote_status,\n            v_quote_items.start_date,\n            v_quote_items.end_date,\n            v_quote_items.reseller_id,\n            v_quote_items.end_user_id,\n            v_quote_items.distributor_id,\n            NULL::text AS contract_id,\n            v_quote_items.quote_id,\n            v_quote_items.product_name,\n            v_quote_items.reseller_price_final,\n            v_quote_items.distributor_price_final,\n            v_quote_items.end_customer_price_final,\n            v_quote_items.quantity,\n            v_quote_items.currency,\n            v_quote_items.coverage_status,\n            v_quote_items.service_group_sku,\n            v_quote_items.service_group_label,\n            v_quote_items.service_level_sku,\n            v_quote_items.service_name\n        FROM\n            v_quote_items;\n    ", "name": "v_assets", "schema": "public", "isExisting": false, "materialized": false}, "public.v_documents": {"columns": {"id": {"name": "id", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": false}, "document_no": {"name": "document_no", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false}, "vendor_id": {"name": "vendor_id", "type": "text", "primaryKey": false, "notNull": false}, "distributor_id": {"name": "distributor_id", "type": "text", "primaryKey": false, "notNull": false}, "reseller_id": {"name": "reseller_id", "type": "text", "primaryKey": false, "notNull": false}, "end_user_id": {"name": "end_user_id", "type": "text", "primaryKey": false, "notNull": false}}, "definition": "SELECT contracts.id, 'contract'::text AS type, contracts.contract_no AS document_no, contracts.status, contracts.vendor_id, contracts.distributor_id, contracts.reseller_id, contracts.end_user_id FROM contracts UNION SELECT quotes.id, 'quote'::text AS type, quotes.quote_no AS document_no, quotes.status, quotes.vendor_id, quotes.distributor_id, quotes.reseller_id, quotes.end_user_id FROM quotes", "name": "v_documents", "schema": "public", "isExisting": false, "materialized": false}, "public.v_contract_items": {"columns": {"id": {"name": "id", "type": "text", "primaryKey": false, "notNull": false}, "item_no": {"name": "item_no", "type": "integer", "primaryKey": false, "notNull": false}, "contract_id": {"name": "contract_id", "type": "text", "primaryKey": false, "notNull": false}, "vendor": {"name": "vendor", "type": "text", "primaryKey": false, "notNull": false}, "serial_no": {"name": "serial_no", "type": "text", "primaryKey": false, "notNull": false}, "product_sku": {"name": "product_sku", "type": "text", "primaryKey": false, "notNull": false}, "service_level_sku": {"name": "service_level_sku", "type": "text", "primaryKey": false, "notNull": false}, "service_group_sku": {"name": "service_group_sku", "type": "text", "primaryKey": false, "notNull": false}, "reseller_price": {"name": "reseller_price", "type": "numeric(20, 4)", "primaryKey": false, "notNull": false}, "end_customer_price": {"name": "end_customer_price", "type": "numeric(20, 4)", "primaryKey": false, "notNull": false}, "quantity": {"name": "quantity", "type": "numeric(10, 0)", "primaryKey": false, "notNull": false}, "reseller_price_final": {"name": "reseller_price_final", "type": "numeric", "primaryKey": false, "notNull": false}, "end_customer_price_final": {"name": "end_customer_price_final", "type": "numeric", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": false}, "service_group_label": {"name": "service_group_label", "type": "text", "primaryKey": false, "notNull": false}, "product_name": {"name": "product_name", "type": "text", "primaryKey": false, "notNull": false}, "service_name": {"name": "service_name", "type": "text", "primaryKey": false, "notNull": false}, "contract_no": {"name": "contract_no", "type": "text", "primaryKey": false, "notNull": false}, "contract_status": {"name": "contract_status", "type": "text", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "text", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "text", "primaryKey": false, "notNull": false}, "coverage_status": {"name": "coverage_status", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false}, "distributor_id": {"name": "distributor_id", "type": "text", "primaryKey": false, "notNull": false}, "vendor_id": {"name": "vendor_id", "type": "text", "primaryKey": false, "notNull": false}, "end_user_id": {"name": "end_user_id", "type": "text", "primaryKey": false, "notNull": false}, "reseller_id": {"name": "reseller_id", "type": "text", "primaryKey": false, "notNull": false}}, "definition": "SELECT ci.id, ci.item_no, ci.contract_id, ci.vendor, ci.serial_no, ci.product_sku, ci.service_level_sku, ci.service_group_sku, ci.reseller_price, ci.end_customer_price, ci.quantity, ci.reseller_price * ci.quantity AS reseller_price_final, ci.end_customer_price * ci.quantity AS end_customer_price_final, ci.data, ci.service_group_label, cp.description AS product_name, sp.description AS service_name, c.contract_no, c.status AS contract_status, c.start_date, c.end_date, ae.coverage_status, c.currency, c.distributor_id, c.vendor_id, c.end_user_id, c.reseller_id FROM contract_items ci LEFT JOIN products cp ON cp.sku = ci.product_sku AND cp.vendor = ci.vendor LEFT JOIN products sp ON sp.sku = ci.service_level_sku AND sp.vendor = ci.vendor LEFT JOIN contracts c ON c.id = ci.contract_id LEFT JOIN asset_enrichments ae ON ae.serial_number = ci.serial_no AND ae.product_sku = ci.product_sku", "name": "v_contract_items", "schema": "public", "isExisting": false, "materialized": false}, "public.v_quote_items": {"columns": {"id": {"name": "id", "type": "text", "primaryKey": false, "notNull": false}, "item_no": {"name": "item_no", "type": "integer", "primaryKey": false, "notNull": false}, "quote_id": {"name": "quote_id", "type": "text", "primaryKey": false, "notNull": false}, "vendor": {"name": "vendor", "type": "text", "primaryKey": false, "notNull": false}, "serial_no": {"name": "serial_no", "type": "text", "primaryKey": false, "notNull": false}, "product_sku": {"name": "product_sku", "type": "text", "primaryKey": false, "notNull": false}, "service_level_sku": {"name": "service_level_sku", "type": "text", "primaryKey": false, "notNull": false}, "service_group_sku": {"name": "service_group_sku", "type": "text", "primaryKey": false, "notNull": false}, "reseller_price": {"name": "reseller_price", "type": "numeric(20, 4)", "primaryKey": false, "notNull": false}, "distributor_price": {"name": "distributor_price", "type": "numeric(20, 4)", "primaryKey": false, "notNull": false}, "end_customer_price": {"name": "end_customer_price", "type": "numeric(20, 4)", "primaryKey": false, "notNull": false}, "quantity": {"name": "quantity", "type": "numeric(10, 0)", "primaryKey": false, "notNull": false}, "reseller_price_final": {"name": "reseller_price_final", "type": "numeric", "primaryKey": false, "notNull": false}, "distributor_price_final": {"name": "distributor_price_final", "type": "numeric", "primaryKey": false, "notNull": false}, "end_customer_price_final": {"name": "end_customer_price_final", "type": "numeric", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": false}, "service_group_label": {"name": "service_group_label", "type": "text", "primaryKey": false, "notNull": false}, "product_name": {"name": "product_name", "type": "text", "primaryKey": false, "notNull": false}, "service_name": {"name": "service_name", "type": "text", "primaryKey": false, "notNull": false}, "quote_no": {"name": "quote_no", "type": "text", "primaryKey": false, "notNull": false}, "quote_status": {"name": "quote_status", "type": "text", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "text", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "text", "primaryKey": false, "notNull": false}, "coverage_status": {"name": "coverage_status", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false}, "distributor_id": {"name": "distributor_id", "type": "text", "primaryKey": false, "notNull": false}, "vendor_id": {"name": "vendor_id", "type": "text", "primaryKey": false, "notNull": false}, "end_user_id": {"name": "end_user_id", "type": "text", "primaryKey": false, "notNull": false}, "reseller_id": {"name": "reseller_id", "type": "text", "primaryKey": false, "notNull": false}}, "definition": "SELECT qi.id, qi.item_no, qi.quote_id, qi.vendor, qi.serial_no, qi.product_sku, qi.service_level_sku, qi.service_group_sku, qi.reseller_price, qi.distributor_price, qi.end_customer_price, qi.quantity, qi.reseller_price * qi.quantity AS reseller_price_final, qi.distributor_price * qi.quantity AS distributor_price_final, qi.end_customer_price * qi.quantity AS end_customer_price_final, qi.data, qi.service_group_label, cp.description AS product_name, sp.description AS service_name, q.quote_no, q.status AS quote_status, q.start_date, q.end_date, ae.coverage_status, q.currency, q.distributor_id, q.vendor_id, q.end_user_id, q.reseller_id FROM quote_items qi LEFT JOIN products cp ON cp.sku = qi.product_sku AND cp.vendor = qi.vendor LEFT JOIN products sp ON sp.sku = qi.service_level_sku AND sp.vendor = qi.vendor LEFT JOIN quotes q ON q.id = qi.quote_id LEFT JOIN asset_enrichments ae ON ae.serial_number = qi.serial_no AND ae.product_sku = qi.product_sku", "name": "v_quote_items", "schema": "public", "isExisting": false, "materialized": false}}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}