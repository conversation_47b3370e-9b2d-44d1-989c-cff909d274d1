CREATE TYPE "public"."event_log_action" AS ENUM('CREATED', 'UPDATED', 'DELETED');
CREATE TYPE "public"."event_log_status" AS ENUM('pending', 'processing', 'completed', 'failed');
CREATE TABLE "event_log" (
                             "id" bigserial PRIMARY KEY NOT NULL,
                             "entity" text NOT NULL,
                             "entity_id" text NOT NULL,
                             "action" "event_log_action" NOT NULL,
                             "before_data" jsonb,
                             "after_data" jsonb,
                             "organization_id" uuid,
                             "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                             "processed_at" timestamp with time zone,
                             "status" "event_log_status" DEFAULT 'pending' NOT NULL,
                             "retry_count" integer DEFAULT 0 NOT NULL,
                             "error_message" text
);
CREATE INDEX "event_log_entity_idx" ON "event_log" ("entity");
CREATE INDEX "event_log_entity_id_idx" ON "event_log" ("entity_id");
CREATE INDEX "event_log_status_idx" ON "event_log" ("status");
CREATE INDEX "event_log_created_at_idx" ON "event_log" ("created_at");
CREATE INDEX "event_log_organization_id_idx" ON "event_log" ("organization_id");

CREATE OR REPLACE FUNCTION notify_data_changes()
RETURNS TRIGGER AS $$
DECLARE
payload JSON;
    entity_name TEXT;
    org_id UUID;
    action_type event_log_action;
    before_data JSONB;
    after_data JSONB;
    entity_id_val TEXT;
BEGIN
    entity_name := INITCAP(TRIM(TRAILING 's' FROM TG_TABLE_NAME));


    IF (TG_OP = 'INSERT' OR TG_OP = 'UPDATE') THEN

BEGIN
            org_id := (NEW.organization_id)::UUID;
EXCEPTION WHEN OTHERS THEN
            org_id := NULL;
END;
    ELSIF (TG_OP = 'DELETE') THEN

BEGIN
            org_id := (OLD.organization_id)::UUID;
EXCEPTION WHEN OTHERS THEN
            org_id := NULL;
END;
END IF;

    IF (TG_OP = 'INSERT') THEN
        action_type := 'CREATED';
        entity_id_val := NEW.id;
        before_data := NULL;
        after_data := row_to_json(NEW)::JSONB;

        payload := json_build_object(
            'action', 'CREATED',
            'entityId', NEW.id,
            'entity', entity_name,
            'before', NULL,
            'after', row_to_json(NEW)
        );
    ELSIF (TG_OP = 'UPDATE') THEN
        action_type := 'UPDATED';
        entity_id_val := NEW.id;
        before_data := row_to_json(OLD)::JSONB;
        after_data := row_to_json(NEW)::JSONB;

        payload := json_build_object(
            'action', 'UPDATED',
            'entityId', NEW.id,
            'entity', entity_name,
            'before', row_to_json(OLD),
            'after', row_to_json(NEW)
        );
    ELSIF (TG_OP = 'DELETE') THEN
        action_type := 'DELETED';
        entity_id_val := OLD.id;
        before_data := row_to_json(OLD)::JSONB;
        after_data := NULL;

        payload := json_build_object(
            'action', 'DELETED',
            'entityId', OLD.id,
            'entity', entity_name,
            'before', row_to_json(OLD),
            'after', NULL
        );
END IF;

BEGIN
INSERT INTO event_log (
    entity,
    entity_id,
    action,
    before_data,
    after_data,
    organization_id,
    created_at,
    status
) VALUES (
             entity_name,
             entity_id_val,
             action_type,
             before_data,
             after_data,
             org_id,
             CURRENT_TIMESTAMP,
             'pending'
         );
EXCEPTION WHEN OTHERS THEN
        RAISE WARNING 'Failed to insert into event_log: %', SQLERRM;
END;

    PERFORM pg_notify('data_changes', payload::text);
RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS quotes_data_change_trigger ON quotes;
CREATE TRIGGER quotes_data_change_trigger
    AFTER INSERT OR UPDATE OR DELETE ON quotes
    FOR EACH ROW
    EXECUTE FUNCTION notify_data_changes();

DROP TRIGGER IF EXISTS organizations_data_change_trigger ON organizations;
CREATE TRIGGER organizations_data_change_trigger
    AFTER INSERT OR UPDATE OR DELETE ON organizations
    FOR EACH ROW
    EXECUTE FUNCTION notify_data_changes();
