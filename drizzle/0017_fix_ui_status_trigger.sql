-- Custom SQL migration file, put your code below! --
CREATE OR REPLACE FUNCTION update_contracts_ui_status()
    RETURNS TRIGGER AS
$$
BEGIN
    IF LOWER(NEW.status) IN ('not validated', 'archived') THEN
        NEW.ui_status := 'Invalid';
    ELSIF LOWER(NEW.status) = 'active' THEN
        NEW.ui_status := 'Active';
    ELSIF LOWER(NEW.status) = 'expired' THEN
        NEW.ui_status := 'Expired';
    ELSIF LOWER(NEW.status) = 'terminated' THEN
        NEW.ui_status := 'Terminated';
    ELSIF LOWER(NEW.status) = 'renewed' THEN
        NEW.ui_status := 'Renewed';
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS contracts_status_update_trigger ON contracts;
CREATE TRIGGER contracts_status_update_trigger
    BEFORE INSERT OR UPDATE OF status
    ON contracts
    FOR EACH ROW
EXECUTE FUNCTION update_contracts_ui_status();

-----------------------------

CREATE OR REPLACE FUNCTION update_quotes_ui_status()
    RETURNS TRIGGER AS
$$
BEGIN
    IF LOWER(NEW.status) IN ('in preparation', 'created', 'preparation', 'quote ready', 'cancelled', 'archived',
                             'renewed', 'expired') THEN
        NEW.ui_status := 'Invalid';

    ELSIF LOWER(NEW.status) IN ('customer contact', 'expires in 90 days', 'expires in 60 days', 'expires in 30 days',
                                'active') THEN
        NEW.ui_status := 'Open';

    ELSIF LOWER(NEW.status) IN ('ready to order', 'order confirmed', 'invoiced', 'customer order') THEN
        NEW.ui_status := 'Ordered';

    ELSIF LOWER(NEW.status) = 'lost' THEN
        NEW.ui_status := 'Lost';
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS quotes_status_update_trigger ON quotes;
CREATE TRIGGER quotes_status_update_trigger
    BEFORE INSERT OR UPDATE OF status
    ON quotes
    FOR EACH ROW
EXECUTE FUNCTION update_quotes_ui_status();

-----------------------------

UPDATE contracts
SET ui_status = CASE
                    WHEN LOWER(status) IN ('not validated', 'archived') THEN 'Invalid'

                    WHEN LOWER(status) = 'active' THEN 'Active'

                    WHEN LOWER(status) = 'expired' THEN 'Expired'

                    WHEN LOWER(status) = 'terminated' THEN 'Terminated'

                    WHEN LOWER(status) = 'renewed' THEN 'Renewed'

                    ELSE ui_status
    END
WHERE ui_status IS NULL;

-----------------------------

UPDATE quotes
SET ui_status = CASE
                    WHEN LOWER(status) IN
                         ('in preparation', 'created', 'preparation', 'quote ready', 'cancelled', 'archived', 'renewed',
                          'expired') THEN 'Invalid'

                    WHEN LOWER(status) IN
                         ('customer contact', 'expires in 90 days', 'expires in 60 days', 'expires in 30 days',
                          'active') THEN 'Open'

                    WHEN LOWER(status) IN ('ready to order', 'order confirmed', 'invoiced', 'customer order')
                        THEN 'Ordered'

                    WHEN LOWER(status) = 'lost' THEN 'Lost'

                    ELSE ui_status
    END
WHERE ui_status IS NULL;
