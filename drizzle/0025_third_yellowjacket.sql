CREATE TYPE "public"."data_source" AS ENUM('iAsset', 'AssetHub', 'Dummy');--> statement-breakpoint

ALTER TABLE "addresses" ADD COLUMN "source" "data_source" DEFAULT 'iAsset' NOT NULL;--> statement-breakpoint
ALTER TABLE "contacts" ADD COLUMN "source" "data_source" DEFAULT 'iAsset' NOT NULL;--> statement-breakpoint
ALTER TABLE "contracts" ADD COLUMN "source" "data_source" DEFAULT 'iAsset' NOT NULL;--> statement-breakpoint
ALTER TABLE "contract_items" ADD COLUMN "source" "data_source" DEFAULT 'iAsset' NOT NULL;--> statement-breakpoint
ALTER TABLE "entities" ADD COLUMN "source" "data_source" DEFAULT 'iAsset' NOT NULL;--> statement-breakpoint
ALTER TABLE "products" ADD COLUMN "source" "data_source" DEFAULT 'iAsset' NOT NULL;--> statement-breakpoint
ALTER TABLE "quotes" ADD COLUMN "source" "data_source" DEFAULT 'iAsset' NOT NULL;--> statement-breakpoint
ALTER TABLE "quote_items" ADD COLUMN "source" "data_source" DEFAULT 'iAsset' NOT NULL;--> statement-breakpoint

UPDATE "addresses" SET source = 'Dummy' WHERE (data -> '__Dummy')::boolean = TRUE;--> statement-breakpoint
UPDATE "contacts" SET source = 'Dummy' WHERE (data -> '__Dummy')::boolean = TRUE;--> statement-breakpoint
UPDATE "contracts" SET source = 'Dummy' WHERE (data -> '__Dummy')::boolean = TRUE;--> statement-breakpoint
UPDATE "contract_items" SET source = 'Dummy' WHERE (data -> '__Dummy')::boolean = TRUE;--> statement-breakpoint
UPDATE "entities" SET source = 'Dummy' WHERE (data -> '__Dummy')::boolean = TRUE;--> statement-breakpoint
UPDATE "products" SET source = 'Dummy' WHERE (data -> '__Dummy')::boolean = TRUE;--> statement-breakpoint
UPDATE "quotes" SET source = 'Dummy' WHERE (data -> '__Dummy')::boolean = TRUE;--> statement-breakpoint
UPDATE "quote_items" SET source = 'Dummy' WHERE (data -> '__Dummy')::boolean = TRUE;
