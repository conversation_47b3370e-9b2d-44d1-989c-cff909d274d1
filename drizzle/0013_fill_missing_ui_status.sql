-- Custom SQL migration file, put your code below! --
UPDATE quotes
SET ui_status = CASE
                    WHEN status IN
                         ('In Preparation', 'Created', 'Preparation', 'Quote Ready', 'Cancelled', 'Archived', 'Renewed',
                          'Expired') THEN 'Invalid'

                    WHEN status IN
                         ('Customer Contact', 'Expires in 90 days', 'Expires in 60 days', 'Expires in 30 days',
                          'Active') THEN 'Open'

                    WHEN status IN ('Ready to Order', 'Order Confirmed', 'Invoiced', 'Customer Order') THEN 'Ordered'

                    WHEN status = 'Lost' THEN 'Lost'

                    ELSE ui_status
    END
WHERE ui_status IS NULL;

UPDATE contracts
SET ui_status = CASE
                    WHEN status IN ('Not Validated', 'Archived') THEN 'Invalid'

                    WHEN status = 'Active' THEN 'Active'

                    WHEN status = 'Expired' THEN 'Expired'

                    WHEN status = 'Terminated' THEN 'Terminated'

                    WHEN status = 'Renewed' THEN 'Renewed'

                    ELSE ui_status
    END
WHERE ui_status IS NULL;
