CREATE EXTENSION IF NOT EXISTS pg_trgm;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_entities_name' AND tablename='entities') THEN
        CREATE INDEX "idx_entities_name" ON "entities" USING gin ("name" gin_trgm_ops);
    END IF;
END $$;--> statement-breakpoint

DROP INDEX IF EXISTS "idx_vito_entities_name";--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_quotes_quote_no' AND tablename='quotes') THEN
        CREATE INDEX "idx_quotes_quote_no" ON "quotes" USING btree ("quote_no");
    END IF;
END $$;--> statement-breakpoint

DROP INDEX IF EXISTS "idx_vito_quotes_quote_no";--> statement-breakpoint

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_addresses_entity_id' AND tablename='addresses') THEN
        CREATE INDEX "idx_addresses_entity_id" ON "addresses" USING btree ("entity_id");
    END IF;
END $$;--> statement-breakpoint

DROP INDEX IF EXISTS "idx_vito_addresses_entity_id";--> statement-breakpoint
