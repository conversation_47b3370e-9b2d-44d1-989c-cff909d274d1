-- Replace the content of 0019_regular_namorita.sql with:
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'import_status') THEN
        CREATE TYPE "public"."import_status" AS ENUM('parsed', 'validated', 'imported');
    END IF;
END
$$;--> statement-breakpoint

CREATE TABLE IF NOT EXISTS "imports" (
	"id" uuid PRIMARY KEY NOT NULL,
	"data" jsonb,
	"file_url" text,
	"created_by_user_id" uuid,
	"import_status" "import_status" NOT NULL,
	"success" boolean NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'import_created_by_user_foreign') THEN
        ALTER TABLE "imports" ADD CONSTRAINT "import_created_by_user_foreign" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE cascade;
    END IF;
END
$$;