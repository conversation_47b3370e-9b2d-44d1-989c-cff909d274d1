import { IMockedService } from '../interfaces/mocked-service.interface';
import { faker } from '@faker-js/faker';
import * as Brevo from '@getbrevo/brevo';

const createMockedContact = () => ({
    email: faker.internet.email(),
    id: faker.string.alphanumeric(5),
    emailBlacklisted: faker.datatype.boolean(),
    smsBlacklisted: faker.datatype.boolean(),
    createdAt: faker.date.past(),
    modifiedAt: faker.date.past(),
    listIds: [faker.string.alphanumeric(5)],
    attributes: {},
});

export class BrevoServiceMock implements IMockedService {
    send = jest.fn();
    getContact = jest.fn().mockResolvedValue(createMockedContact());
    createContact = jest.fn().mockResolvedValue({
        id: faker.string.alphanumeric(5),
    });
    updateContact = jest.fn().mockResolvedValue(createMockedContact());

    public setSendResult(result: any) {
        this.send.mockResolvedValue(result);
    }

    public setGetContactResult(result: Brevo.GetExtendedContactDetails | null) {
        this.getContact.mockResolvedValue(result);
    }

    public setCreateContactResult(result: Brevo.CreateUpdateContactModel | null) {
        this.createContact.mockResolvedValue(result);
    }

    public setUpdateContactResult(result: Brevo.GetExtendedContactDetails | null) {
        this.updateContact.mockResolvedValue(result);
    }

    reset() {
        this.send = jest.fn();
        this.getContact.mockResolvedValue(createMockedContact());
        this.createContact.mockResolvedValue({
            id: faker.string.alphanumeric(5),
        });
        this.updateContact.mockResolvedValue(createMockedContact());
    }
}
