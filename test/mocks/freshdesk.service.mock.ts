import { IMockedService } from '../interfaces/mocked-service.interface';

export const DEFAULT_MOCK_COMPANY_ID = 111;
export const DEFAULT_MOCK_CONTACT_ID = 222;
export const DEFAULT_MOCK_TICKET_ID = 333;
export const DEFAULT_MOCK_AGENT_ID = 'agent-999';
export const DEFAULT_ENVIRONMENT = 'Test';

export class FreshdeskServiceMock implements IMockedService {
    createCompany = jest.fn().mockResolvedValue(DEFAULT_MOCK_COMPANY_ID);
    createContact = jest.fn().mockResolvedValue(DEFAULT_MOCK_CONTACT_ID);
    createTicket = jest.fn().mockResolvedValue(DEFAULT_MOCK_TICKET_ID);
    findAgentIdByEmail = jest.fn().mockResolvedValue(DEFAULT_MOCK_AGENT_ID);
    getCapitalizedEnvironment = jest.fn().mockReturnValue(DEFAULT_ENVIRONMENT);

    public setCreateCompanyResult(result: number) {
        this.createCompany.mockResolvedValue(result);
    }

    public setCreateContactResult(result: number) {
        this.createContact.mockResolvedValue(result);
    }

    public setCreateTicketResult(result: number) {
        this.createTicket.mockResolvedValue(result);
    }

    public setFindAgentIdByEmailResult(result: string | null) {
        this.findAgentIdByEmail.mockResolvedValue(result);
    }

    public setGetCapitalizedEnvironmentResult(result: string) {
        this.getCapitalizedEnvironment.mockReturnValue(result);
    }

    public reset(): void {
        this.createCompany.mockReturnValue(DEFAULT_MOCK_COMPANY_ID);
        this.createContact.mockResolvedValue(DEFAULT_MOCK_COMPANY_ID);
        this.createTicket.mockResolvedValue(DEFAULT_MOCK_TICKET_ID);
        this.findAgentIdByEmail.mockReturnValue(DEFAULT_MOCK_AGENT_ID);
        this.getCapitalizedEnvironment.mockReturnValue(DEFAULT_ENVIRONMENT);
    }
}
