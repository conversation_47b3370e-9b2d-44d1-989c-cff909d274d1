import { CanActivate, ExecutionContext } from '@nestjs/common';

const DEFAULT_MOCK_AUTH_TOKEN = 'mock-token';

export const createMockAuthenticatedGuard = (canActivateReturn: boolean, context?: any): CanActivate => {
    return {
        canActivate: (executionContext: ExecutionContext) => {
            const request = executionContext.switchToHttp().getRequest();
            if (context) {
                request.context = context;
                request.user = context.user;
                request.access_token = DEFAULT_MOCK_AUTH_TOKEN;
            }
            return canActivateReturn;
        },
    };
};
