import { IAssetFile } from '../../src/domain/iasset/iasset-file';
import { IMockedService } from '../interfaces/mocked-service.interface';

export const DEFAULT_MOCK_QUOTE_OWNER_ENTITY_ID = 'DEFAULT_ENTITY';
export const DEFAULT_MOCK_CONTRACT_OWNER_ENTITY_ID = 'DEFAULT_CONTRACT_ENTITY';
export const DEFAULT_MOCK_QUOTE_FILE_NAME = 'default-quote.pdf';
export const DEFAULT_MOCK_CONTRACT_FILE_NAME = 'default-contract.pdf';
export const DEFAULT_MOCK_QUOTE_ID = 1;
export const DEFAULT_MOCK_CONTRACT_ID = 2;

export class IAssetClientMock implements IMockedService {
    findQuoteIdByNumber = jest.fn().mockResolvedValue(DEFAULT_MOCK_QUOTE_ID);
    getQuoteOwnerEntityByQuoteId = jest.fn().mockResolvedValue(DEFAULT_MOCK_QUOTE_OWNER_ENTITY_ID);
    findContractIdByNumber = jest.fn().mockResolvedValue(DEFAULT_MOCK_CONTRACT_ID);
    getContractOwnerEntityByContractId = jest.fn().mockResolvedValue(DEFAULT_MOCK_CONTRACT_OWNER_ENTITY_ID);
    downloadQuotePdf = jest.fn().mockResolvedValue(new IAssetFile(DEFAULT_MOCK_QUOTE_FILE_NAME, {} as any));
    downloadContractPdf = jest.fn().mockResolvedValue(new IAssetFile(DEFAULT_MOCK_CONTRACT_FILE_NAME, {} as any));

    setFindQuoteIdByNumberResult(result: number) {
        this.findQuoteIdByNumber.mockResolvedValue(result);
    }

    setGetQuoteOwnerEntityByQuoteIdResult(result: string) {
        this.getQuoteOwnerEntityByQuoteId.mockResolvedValue(result);
    }

    setFindContractIdByNumberResult(result: number) {
        this.findContractIdByNumber.mockResolvedValue(result);
    }

    setGetContractOwnerEntityByContractIdResult(result: string) {
        this.getContractOwnerEntityByContractId.mockResolvedValue(result);
    }

    setDownloadQuotePdfResult(file: IAssetFile) {
        this.downloadQuotePdf.mockResolvedValue(file);
    }

    setDownloadContractPdfResult(file: IAssetFile) {
        this.downloadContractPdf.mockResolvedValue(file);
    }

    reset() {
        this.findQuoteIdByNumber.mockResolvedValue(DEFAULT_MOCK_QUOTE_ID);
        this.getQuoteOwnerEntityByQuoteId.mockResolvedValue(DEFAULT_MOCK_QUOTE_OWNER_ENTITY_ID);
        this.findContractIdByNumber.mockResolvedValue(DEFAULT_MOCK_CONTRACT_ID);
        this.getContractOwnerEntityByContractId.mockResolvedValue(DEFAULT_MOCK_CONTRACT_OWNER_ENTITY_ID);
        this.downloadQuotePdf.mockResolvedValue(new IAssetFile(DEFAULT_MOCK_QUOTE_FILE_NAME, {} as any));
        this.downloadContractPdf.mockResolvedValue(new IAssetFile(DEFAULT_MOCK_CONTRACT_FILE_NAME, {} as any));
    }
}
