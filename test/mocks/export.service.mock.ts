import { IMockedService } from '../interfaces/mocked-service.interface';
import { faker } from '@faker-js/faker';

export class ExportServiceMock implements IMockedService {
    exportData = jest.fn().mockResolvedValue({ data: {}, filename: `${faker.string.alpha(10)}.csv` });

    setExportDataResult(result: { data: Buffer; filename: string }) {
        this.exportData.mockResolvedValue(result);
    }

    reset() {
        this.setExportDataResult({ data: {} as Buffer, filename: `${faker.string.alpha(10)}.csv` });
    }
}
