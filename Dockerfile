# ---- Builder Stage ----
FROM node:22.4.1 as builder

RUN apt-get update && apt-get install -y jq && rm -rf /var/lib/apt/lists/*

WORKDIR /app

COPY package*.json ./
RUN npm ci

COPY . .
RUN chmod +x ./docker/init.sh && chmod +x ./docker/keycloak/init.sh

RUN npm run build

FROM node:22.4.1 as development

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY --from=builder /app/node_modules ./node_modules

CMD ["npm", "run", "start:dev"]

# ---- Production Stage ----
FROM node:22.4.1 as production

WORKDIR /app

COPY --from=builder /app/patches ./patches
COPY package*.json ./
# Install patch-package as a separate step before running npm ci --omit=dev
RUN npm install --no-save patch-package
RUN npm ci --omit=dev --ignore-scripts
# Run the postinstall script manually after dependencies are installed
RUN npx patch-package

COPY --from=builder /app/docker/init.sh ./docker/init.sh
COPY --from=builder /app/docker/keycloak ./docker/keycloak
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/tsconfig.json ./tsconfig.json
COPY --from=builder /app/templates ./templates
COPY --from=builder /app/i18n ./i18n
COPY --from=builder /app/drizzle/*.sql ./drizzle/
COPY --from=builder /app/drizzle/meta ./drizzle/meta

# Create a simplified entrypoint script
RUN echo '#!/bin/sh\n\necho "Running migrations..."\nnpm run drizzle:migrate:prod || echo "Migrations failed but continuing..."\n\necho "Starting application..."\nexec npm run start:prod' > /app/entrypoint.sh \
    && chmod +x /app/entrypoint.sh

CMD ["/bin/sh", "/app/entrypoint.sh"]
