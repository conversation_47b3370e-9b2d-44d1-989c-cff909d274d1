import { Utils } from '@mikro-orm/core';
import { defineConfig } from '@mikro-orm/postgresql';
import { PostgreSqlDriver } from '@mikro-orm/postgresql';
import { SeedManager } from '@mikro-orm/seeder';
import { Logger } from '@nestjs/common';
const logger = new Logger('MikroORM');

export default defineConfig({
    driver: PostgreSqlDriver,
    driverOptions: {
        connection: {
            max: 20,
            connectionTimeoutMillis: 2000,
        },
    },
    pool: {
        min: 4,
        max: 20,
    },
    host: process.env.DATABASE_HOST,
    port: parseInt(process.env.DATABASE_PORT || '5432', 10),

    dbName: process.env.DATABASE_NAME,
    user: process.env.DATABASE_USER,
    password: process.env.DATABASE_PASSWORD,
    extensions: [SeedManager],
    entities: ['./dist/**/*.entity.js', './dist/domain/**/*.entity.js'],
    entitiesTs: ['./src/**/*.entity.ts', './src/domain/**/*.entity.ts'],
    debug: process.env.NODE_ENV === 'development',
    logger: logger.log.bind(logger),
    allowGlobalContext: true,
});
