import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { Cache } from 'cache-manager';
import { and, count, eq, or, sql } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

import * as schema from '../../drizzle/schema';
import { ActionType, ResourceType } from '../domain/users/entities';
import { PermissionDrizzleRepository } from '../domain/users/repositories/permission.drizzle.repository';
import { DRIZZLE_PROVIDER } from '../drizzle/drizzle.provider';

@Injectable()
export class PermissionsService {
    private readonly logger = new Logger(PermissionsService.name);

    constructor(
        @Inject(DRIZZLE_PROVIDER)
        private readonly db: NodePgDatabase<typeof schema>,
        private readonly permissionRepository: PermissionDrizzleRepository,
        @Inject(CACHE_MANAGER) private cacheManager: Cache,
    ) {}

    async createPermission(
        resourceType: ResourceType,
        actionType: ActionType,
        description?: string,
    ): Promise<typeof schema.permissions.$inferSelect> {
        const name = `${resourceType}:${actionType}`;

        const [permission] = await this.db
            .insert(schema.permissions)
            .values({
                id: sql`gen_random_uuid()`,
                name,
                resourceType,
                actionType,
                description: description || `Permission to ${actionType} ${resourceType}`,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                disabled: false,
            })
            .returning();

        return permission;
    }

    async assignPermissionToRole(
        roleId: string,
        permissionId: string,
    ): Promise<typeof schema.rolePermissions.$inferSelect> {
        // Check if role and permission exist
        const role = await this.db.query.roles.findFirst({
            where: eq(schema.roles.id, roleId),
        });

        const permission = await this.db.query.permissions.findFirst({
            where: eq(schema.permissions.id, permissionId),
        });

        if (!role || !permission) {
            throw new NotFoundException('Role or Permission not found');
        }

        const [rolePermission] = await this.db
            .insert(schema.rolePermissions)
            .values({
                id: sql`gen_random_uuid()`,
                roleId,
                permissionId,
                createdAt: new Date().toISOString(),
            })
            .returning();

        return rolePermission;
    }

    async checkPermission(
        keycloakId: string,
        resourceType: ResourceType,
        actionType: ActionType,
        organizationId: string,
    ): Promise<boolean> {
        // Create a cache key based on method parameters
        const cacheKey = `permission:${keycloakId}:${resourceType}:${actionType}:${organizationId || 'global'}`;
        const cachedResult = await this.cacheManager.get<boolean>(cacheKey);

        this.logger.log(`Cached result: ${cachedResult}`);

        if (cachedResult) {
            return cachedResult;
        }

        const query = this.db
            .select({ count: count() })
            .from(schema.permissions)
            .innerJoin(schema.rolePermissions, eq(schema.rolePermissions.permissionId, schema.permissions.id))
            .innerJoin(schema.roles, eq(schema.roles.id, schema.rolePermissions.roleId))
            .innerJoin(schema.userRoles, eq(schema.userRoles.roleId, schema.roles.id))
            .innerJoin(schema.users, eq(schema.users.id, schema.userRoles.userId));

        const conditions = [
            eq(schema.users.keycloakId, keycloakId),
            eq(schema.permissions.disabled, false),
            eq(schema.roles.organizationId, organizationId),
            or(
                and(eq(schema.permissions.resourceType, resourceType), eq(schema.permissions.actionType, actionType)),
                and(
                    eq(schema.permissions.resourceType, resourceType),
                    eq(schema.permissions.actionType, ActionType.MANAGE),
                ),
                and(
                    eq(schema.permissions.resourceType, ResourceType.PLATFORM),
                    eq(schema.permissions.actionType, ActionType.MANAGE),
                ),
            ),
        ];

        const result = await query.where(and(...conditions));

        const hasPermission = result[0].count > 0;

        await this.cacheManager.set(cacheKey, hasPermission);

        return hasPermission;
    }

    async getPermissionsByRole(roleId: string): Promise<(typeof schema.permissions.$inferSelect)[]> {
        const rolePermissions = await this.db
            .select()
            .from(schema.permissions)
            .innerJoin(schema.rolePermissions, eq(schema.rolePermissions.permissionId, schema.permissions.id))
            .where(eq(schema.rolePermissions.roleId, roleId));

        if (!rolePermissions.length) {
            throw new NotFoundException('Role not found or has no permissions');
        }

        return rolePermissions.map((rp) => rp.permissions);
    }

    async getUserPermissionsInOrganization(
        userId: string,
        organizationId: string,
    ): Promise<(typeof schema.permissions.$inferSelect)[]> {
        const permissions = await this.db
            .select()
            .from(schema.permissions)
            .innerJoin(schema.rolePermissions, eq(schema.rolePermissions.permissionId, schema.permissions.id))
            .innerJoin(schema.roles, eq(schema.roles.id, schema.rolePermissions.roleId))
            .innerJoin(schema.userRoles, eq(schema.userRoles.roleId, schema.roles.id))
            .where(
                and(
                    eq(schema.userRoles.userId, userId),
                    eq(schema.roles.organizationId, organizationId),
                    eq(schema.permissions.disabled, false),
                ),
            );

        return permissions.map((p) => p.permissions);
    }

    async getUserPermissionsInOrganizationByKeycloakId(
        userId: string,
        organizationId: string,
    ): Promise<(typeof schema.permissions.$inferSelect)[]> {
        const permissions = await this.db
            .select()
            .from(schema.permissions)
            .innerJoin(schema.rolePermissions, eq(schema.rolePermissions.permissionId, schema.permissions.id))
            .innerJoin(schema.roles, eq(schema.roles.id, schema.rolePermissions.roleId))
            .innerJoin(schema.userRoles, eq(schema.userRoles.roleId, schema.roles.id))
            .innerJoin(schema.users, eq(schema.users.id, schema.userRoles.userId))
            .where(
                and(
                    eq(schema.users.keycloakId, userId),
                    eq(schema.roles.organizationId, organizationId),
                    eq(schema.permissions.disabled, false),
                ),
            );

        return permissions.map((p) => p.permissions);
    }
}
