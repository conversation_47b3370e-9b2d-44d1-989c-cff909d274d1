import { CanActivate, ExecutionContext, Injectable, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

import { RequestInterface } from '../domain/common/interfaces/request.interface';
import { UserService } from './../domain/users/services/users.service';
import { DISABLE_PERMISSIONS_GUARD_KEY, RESOURCE_ACTION_KEY } from './permissions.decorator';
import { PermissionsService } from './permissions.service';
import { ResourceActionRequirement } from './permissions.types';

@Injectable()
export class PermissionsGuard implements CanActivate {
    private readonly logger = new Logger(PermissionsGuard.name);

    constructor(
        private reflector: Reflector,
        private permissionService: PermissionsService,
        private userService: UserService,
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const requirements = this.reflector.get<ResourceActionRequirement[]>(RESOURCE_ACTION_KEY, context.getHandler());
        const disablePermissionsGuard = this.reflector.get<boolean>(
            DISABLE_PERMISSIONS_GUARD_KEY,
            context.getHandler(),
        );

        const request = context.switchToHttp().getRequest<RequestInterface>();
        const correlationId = request.headers['x-correlation-id'] as string;
        const method = request.method;
        const url = request.url;

        this.logger.debug('Permission check started', {
            correlationId,
            method,
            url,
            requirementsCount: requirements?.length || 0,
            isGuardDisabled: disablePermissionsGuard,
        });

        if (!requirements?.length || disablePermissionsGuard) {
            this.logger.debug('Permission check bypassed', {
                correlationId,
                reason: !requirements?.length ? 'no requirements' : 'guard disabled',
            });
            return true;
        }

        //todo we should use user id instead of keycloak id
        const userId = request.user?.sub;
        const organizationId = request.context?.organizationId;

        if (!userId || !organizationId) {
            this.logger.error('Permission check failed: missing required context', {
                correlationId,
                hasUserId: !!userId,
                hasOrganizationId: !!organizationId,
                method,
                url,
            });
            return false;
        }

        this.logger.debug('Starting permission validation', {
            correlationId,
            userId,
            organizationId,
            requirements: requirements.map((r) => `${r.action}:${r.resource}`),
        });

        for (const requirement of requirements) {
            const hasPermission = await this.permissionService.checkPermission(
                userId,
                requirement.resource,
                requirement.action,
                organizationId,
            );

            if (hasPermission) {
                this.logger.log('Permission granted', {
                    correlationId,
                    userId,
                    organizationId,
                    resource: requirement.resource,
                    action: requirement.action,
                });
                return true;
            }

            this.logger.warn('Permission denied', {
                correlationId,
                userId,
                organizationId,
                resource: requirement.resource,
                action: requirement.action,
                method,
                url,
            });
        }

        this.logger.error('All permission checks failed', {
            correlationId,
            userId,
            organizationId,
            checkedRequirements: requirements.map((r) => `${r.action}:${r.resource}`),
            method,
            url,
        });

        return false;
    }
}
