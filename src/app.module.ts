import { join } from 'node:path';

import { MikroOrmModule } from '@mikro-orm/nestjs';
import { CacheModule } from '@nestjs/cache-manager';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_GUARD } from '@nestjs/core';
import { JwtModule } from '@nestjs/jwt';
import { ScheduleModule } from '@nestjs/schedule';
import { SentryModule } from '@sentry/nestjs/setup';
import { HeaderResolver, I18nModule } from 'nestjs-i18n';
import { LoggerModule } from 'nestjs-pino';
import * as process from 'process';

import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { AuthenticatedGuard } from './auth/authenticated.guard';
import { loggerConfig } from './config/logger.config';
import config from './configs/mikro-orm.config';
import { AssetsModule } from './domain/assets';
import { CommonModule } from './domain/common/common.module';
import { ContractsModule } from './domain/contracts/contracts.module';
import { EntitiesModule } from './domain/entities/entities.module';
import { FeatureFlagsModule } from './domain/feature-flags/feature-flags.module';
import { FeatureFlagGuard } from './domain/feature-flags/guards/feature-flag.guard';
import { FilesModule } from './domain/files';
import { IAssetModule } from './domain/iasset/iasset.module';
import { MetricsModule } from './domain/metrics/metrics.module';
import { OrganizationsModule } from './domain/organizations/organizations.module';
import { ProductsModule } from './domain/products/products.module';
import { QuotesModule } from './domain/quotes/quotes.module';
import { ReportingModule } from './domain/reporting';
import { SearchModule } from './domain/search/search.module';
import { UsersModule } from './domain/users/users.module';
import { DrizzleModule } from './drizzle/drizzle.module';
import { PermissionsGuard } from './rbac/permissions.guard';
import { RbacModule } from './rbac/rbac.module';
import { SchemaModule } from './schema/schema.module';
import { WebhooksModule } from './webhooks/webhooks.module';

@Module({
    imports: [
        SentryModule.forRoot(),
        LoggerModule.forRoot(loggerConfig),
        CacheModule.register({
            isGlobal: true,
            ttl: 5 * 60 * 1000, // 5 minutes
        }),
        ConfigModule.forRoot({ isGlobal: true }),
        ScheduleModule.forRoot(),
        MikroOrmModule.forRoot(config),
        JwtModule.register({ secret: process.env.JWT_SECRET || 'secret', signOptions: { expiresIn: '1h' } }),
        MetricsModule,
        CommonModule,
        ContractsModule,
        EntitiesModule,
        IAssetModule,
        OrganizationsModule,
        ProductsModule,
        QuotesModule,
        SearchModule,
        UsersModule,
        AuthModule.register(),
        RbacModule,
        SchemaModule,
        ReportingModule,
        AssetsModule,
        FilesModule,
        DrizzleModule,
        FeatureFlagsModule,
        WebhooksModule,
        I18nModule.forRootAsync({
            useFactory: () => ({
                fallbackLanguage: 'de',
                loaderOptions: {
                    path: join(process.cwd(), 'i18n'),
                },
                typesOutputPath: join(process.cwd(), 'i18n/generated/i18n.generated.ts'),
            }),
            resolvers: [new HeaderResolver(['x-lang'])],
        }),
    ],
    controllers: [AppController],
    providers: [
        AppService,
        {
            provide: APP_GUARD,
            useClass: AuthenticatedGuard,
        },
        {
            provide: APP_GUARD,
            useClass: PermissionsGuard,
        },
        {
            provide: APP_GUARD,
            useClass: FeatureFlagGuard,
        },
    ],
})
export class AppModule {}
