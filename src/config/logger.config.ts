import { randomUUID } from 'crypto';
import { Params } from 'nestjs-pino';

const getProductionTransport = () => {
    if (process.env.LOKI_URL && (process.env.NODE_ENV === 'production' || process.env.NODE_ENV === 'staging')) {
        return {
            target: 'pino-loki',
            options: {
                batching: true,
                interval: 5,
                host: process.env.LOKI_URL,
                basicAuth: process.env.LOKI_BASIC_AUTH
                    ? {
                          username: process.env.LOKI_USERNAME,
                          password: process.env.LOKI_PASSWORD,
                      }
                    : undefined,
                labels: {
                    job: process.env.SERVICE_NAME || 'nestjs-app',
                    environment: process.env.NODE_ENV,
                    service: process.env.SERVICE_NAME || 'nestjs-app',
                    version: process.env.SERVICE_VERSION || '1.0.0',
                    instance: process.env.RAILWAY_SERVICE_ID || process.env.HOSTNAME || 'unknown',
                },
                replaceTimestamp: false,
                convertArrays: true,
            },
        };
    }
    // Default to stdout for containerized environments
    return undefined;
};

export const loggerConfig: Params = {
    pinoHttp: {
        genReqId: (req) => req.headers['x-correlation-id'] || randomUUID(),
        level: process.env.LOG_LEVEL || (process.env.NODE_ENV === 'production' ? 'info' : 'debug'),
        transport:
            process.env.NODE_ENV === 'development'
                ? {
                      target: 'pino-pretty',
                      options: {
                          colorize: true,
                          levelFirst: true,
                          translateTime: 'yyyy-mm-dd HH:MM:ss.l',
                      },
                  }
                : getProductionTransport(),
        serializers: {
            req: (req) => {
                try {
                    const serialized: any = {
                        id: req.id,
                        method: req.method,
                        url: req.url,
                        query: req.query,
                        params: req.params,
                        headers: {
                            'user-agent': req.headers['user-agent'],
                            'x-correlation-id': req.headers['x-correlation-id'],
                            'x-organization-id': req.headers['x-organization-id'],
                            'content-type': req.headers['content-type'],
                            'content-length': req.headers['content-length'],
                        },
                        remoteAddress: req.remoteAddress,
                        remotePort: req.remotePort,
                    };

                    // Debug: Log context state (temporary for troubleshooting)
                    serialized.debug = {
                        hasContext: !!req.context,
                        hasContextUser: !!req.context?.user,
                        hasReqUser: !!req.user,
                        contextKeys: req.context ? Object.keys(req.context) : [],
                        userKeys: req.user ? Object.keys(req.user) : [],
                    };

                    if (req.context?.user) {
                        serialized.user = {
                            id: req.context.user.id,
                            keycloakId: req.context.user.keycloakId,
                            email: req.context.user.email,
                            locale: req.context.user.locale,
                            timezone: req.context.user.timezone,
                            isActive: req.context.user.isActive,
                            status: req.context.user.status,
                        };
                    } else if (req.user) {
                        serialized.user = {
                            keycloakId: req.user.sub,
                            email: req.user.email,
                            roles: req.user.realm_access?.roles,
                        };
                    }

                    if (req.context?.organizationId) {
                        serialized.organization = {
                            id: req.context.organizationId,
                        };
                    }

                    if (req.context?.permissions) {
                        serialized.permissions = req.context.permissions.map((p) => ({
                            resource: p.resourceType,
                            action: p.actionType,
                            name: p.name,
                        }));
                    }

                    if (req.context?.hasAdvancedPermissions) {
                        serialized.advancedPermissions = {
                            enabled: req.context.hasAdvancedPermissions,
                            resellerIds: req.context.resellerIds,
                            endUserIds: req.context.endUserIds,
                        };
                    }

                    if (process.env.NODE_ENV !== 'production' && req.body) {
                        const contentType = req.headers['content-type'] || '';
                        const bodySize = JSON.stringify(req.body).length;

                        if (
                            bodySize < 1000 &&
                            (contentType.includes('application/json') ||
                                contentType.includes('application/x-www-form-urlencoded'))
                        ) {
                            serialized.body = req.body;
                        }
                    }

                    return serialized;
                } catch (error) {
                    return {
                        id: req.id,
                        method: req.method,
                        url: req.url,
                        serializationError: 'Failed to serialize request',
                    };
                }
            },
            res: (res) => {
                try {
                    return {
                        statusCode: res.statusCode,
                        headers: typeof res.getHeaders === 'function' ? res.getHeaders() : {},
                    };
                } catch (error) {
                    return {
                        statusCode: res.statusCode || 'unknown',
                        serializationError: 'Failed to serialize response',
                    };
                }
            },
            err: (err) => {
                try {
                    const serialized: any = {
                        type: err.constructor.name,
                        message: err.message,
                        stack: err.stack,
                        code: err.code,
                        statusCode: err.statusCode,
                        ...(err.cause && { cause: err.cause }),
                    };

                    if (err.name === 'ValidationError' && err.details) {
                        serialized.validationDetails = err.details;
                    }

                    if (err.name === 'QueryFailedError' && err.query) {
                        serialized.query = err.query;
                        serialized.parameters = err.parameters;
                    }

                    return serialized;
                } catch (serializationError) {
                    return {
                        type: 'SerializationError',
                        message: 'Failed to serialize error',
                        originalMessage: err?.message || 'Unknown error',
                    };
                }
            },
        },
        autoLogging: {
            ignore: (req) => req.url === '/metrics' || req.url === '/health',
        },
        customLogLevel: (req, res, err) => {
            if (res.statusCode >= 400 && res.statusCode < 500) {
                return 'warn';
            } else if (res.statusCode >= 500 || err) {
                return 'error';
            }
            return 'info';
        },
        customSuccessMessage: (req, res, responseTime) => {
            return `${req.method} ${req.url} ${res.statusCode} - ${responseTime}ms`;
        },
        customErrorMessage: (error, res) => {
            return `Request failed with ${res.statusCode}: ${(error as any)?.message || 'Unknown error'}`;
        },
        customAttributeKeys: {
            req: 'request',
            res: 'response',
            err: 'error',
            responseTime: 'duration',
        },
        messageKey: 'message',
        timestamp: () => `,"timestamp":"${new Date().toISOString()}"`,
        ...(process.env.NODE_ENV === 'production' && {
            redact: {
                paths: [
                    'req.headers.authorization',
                    'req.headers.cookie',
                    'req.body.password',
                    'req.body.token',
                    'req.body.secret',
                    'req.query.token',
                    'req.query.password',
                    'req.params.token',
                ],
                censor: '[REDACTED]',
            },
        }),
        base: {
            service: process.env.SERVICE_NAME || 'assethub-backend',
            environment: process.env.NODE_ENV || 'development',
            version: process.env.SERVICE_VERSION || '1.0.0',
            instance: process.env.RAILWAY_SERVICE_ID || process.env.HOSTNAME || 'unknown',
        },
    },
    ...(process.env.NODE_ENV === 'production' && {
        timestamp: () => `,"time":${Date.now()}`,
    }),
};
