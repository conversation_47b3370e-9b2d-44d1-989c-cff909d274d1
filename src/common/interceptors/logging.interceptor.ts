import { Call<PERSON><PERSON><PERSON>, ExecutionContext, Injectable, Logger, NestInterceptor } from '@nestjs/common';
import { randomUUID } from 'crypto';
import { Request, Response } from 'express';
import { Observable } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
    private readonly logger = new Logger(LoggingInterceptor.name);

    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        const ctx = context.switchToHttp();
        const request = ctx.getRequest<Request>();
        const response = ctx.getResponse<Response>();

        const correlationId = (request.headers['x-correlation-id'] as string) || randomUUID();
        request.headers['x-correlation-id'] = correlationId;

        response.setHeader('x-correlation-id', correlationId);

        const startTime = Date.now();
        const { method, url, ip, headers } = request;
        const userAgent = headers['user-agent'] || '';

        this.logger.log({
            message: 'Incoming request',
            correlationId,
            method,
            url,
            ip,
            userAgent,
            timestamp: new Date().toISOString(),
        });

        return next.handle().pipe(
            tap((data) => {
                const duration = Date.now() - startTime;
                const { statusCode } = response;

                this.logger.log({
                    message: 'Request completed',
                    correlationId,
                    method,
                    url,
                    statusCode,
                    duration,
                    timestamp: new Date().toISOString(),
                });
            }),
            catchError((error) => {
                const duration = Date.now() - startTime;
                const statusCode = error.status || 500;

                this.logger.error({
                    message: 'Request failed',
                    correlationId,
                    method,
                    url,
                    statusCode,
                    duration,
                    error: {
                        message: error.message,
                        stack: error.stack,
                    },
                    timestamp: new Date().toISOString(),
                });

                throw error;
            }),
        );
    }
}
