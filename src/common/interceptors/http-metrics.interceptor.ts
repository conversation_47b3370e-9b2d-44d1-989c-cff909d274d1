import { Call<PERSON><PERSON><PERSON>, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { InjectMetric } from '@willsoto/nestjs-prometheus';
import { Request, Response } from 'express';
import { Counter, Histogram } from 'prom-client';
import { Observable } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';

@Injectable()
export class HttpMetricsInterceptor implements NestInterceptor {
    private readonly serviceName: string;
    private readonly environment: string;

    constructor(
        @InjectMetric('http_requests_total')
        private readonly httpRequestsTotal: Counter<string>,
        @InjectMetric('http_request_duration_seconds')
        private readonly httpRequestDuration: Histogram<string>,
        @InjectMetric('http_request_errors_total')
        private readonly httpRequestErrors: Counter<string>,
    ) {
        this.serviceName = process.env.SERVICE_NAME || 'nestjs-app';
        this.environment = process.env.NODE_ENV || 'development';
    }

    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        const ctx = context.switchToHttp();
        const request = ctx.getRequest<Request>();
        const response = ctx.getResponse<Response>();

        const startTime = Date.now();
        const { method, route } = request;
        const path = route?.path || request.url;

        return next.handle().pipe(
            tap(() => {
                const duration = (Date.now() - startTime) / 1000;
                const { statusCode } = response;
                const statusClass = this.getStatusClass(statusCode);

                this.httpRequestsTotal.inc({
                    method,
                    path,
                    status_code: statusCode.toString(),
                    status_class: statusClass,
                });

                this.httpRequestDuration.observe(
                    {
                        method,
                        path,
                        status_code: statusCode.toString(),
                        status_class: statusClass,
                    },
                    duration,
                );
            }),
            catchError((error) => {
                const duration = (Date.now() - startTime) / 1000;
                const statusCode = error.status || 500;
                const statusClass = this.getStatusClass(statusCode);

                this.httpRequestsTotal.inc({
                    method,
                    path,
                    status_code: statusCode.toString(),
                    status_class: statusClass,
                });

                this.httpRequestDuration.observe(
                    {
                        method,
                        path,
                        status_code: statusCode.toString(),
                        status_class: statusClass,
                    },
                    duration,
                );

                this.httpRequestErrors.inc({
                    method,
                    path,
                    status_code: statusCode.toString(),
                    error_type: error.name || 'UnknownError',
                });

                throw error;
            }),
        );
    }

    private getStatusClass(statusCode: number): string {
        if (statusCode >= 100 && statusCode < 200) return '1xx';
        if (statusCode >= 200 && statusCode < 300) return '2xx';
        if (statusCode >= 300 && statusCode < 400) return '3xx';
        if (statusCode >= 400 && statusCode < 500) return '4xx';
        if (statusCode >= 500) return '5xx';
        return 'unknown';
    }
}
