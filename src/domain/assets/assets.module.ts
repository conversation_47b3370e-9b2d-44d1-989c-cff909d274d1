import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { CommonModule } from '../common/common.module';
import { ContractsModule } from '../contracts/contracts.module';
import { EntitiesModule } from '../entities/entities.module';
import { QuotesModule } from '../quotes';
import { AssetsController } from './controllers';
import { AssetEnrichmentRepository, AssetViewDrizzleRepository } from './repositories';
import { AssetEnrichmentService, AssetsService } from './services';

@Module({
    imports: [ConfigModule, QuotesModule, ContractsModule, CommonModule, forwardRef(() => EntitiesModule)],
    controllers: [AssetsController],
    providers: [AssetEnrichmentService, AssetsService, AssetEnrichmentRepository, AssetViewDrizzleRepository],
    exports: [AssetEnrichmentService, AssetsService],
})
export class AssetsModule {}
