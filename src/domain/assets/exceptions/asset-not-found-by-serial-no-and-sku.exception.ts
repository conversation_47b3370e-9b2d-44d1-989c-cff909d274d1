import { NotFoundException } from '@nestjs/common';
import { I18nContext } from 'nestjs-i18n';

import { I18nPath } from '../../../../i18n/generated/i18n.generated';

export class AssetNotFoundBySerialNoAndSkuException extends NotFoundException {
    constructor(serialNo: string, sku: string) {
        super(
            I18nContext.current().t<I18nPath>('errors.ASSETS.ASSET_NOT_FOUND_BY_SERIAL_NO_AND_SKU', {
                args: {
                    sku,
                    serialNo,
                },
            }),
        );
    }
}
