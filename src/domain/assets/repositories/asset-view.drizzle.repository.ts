import { Inject, Injectable } from '@nestjs/common';
import { and, desc, eq, inArray } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

import * as schema from '../../../../drizzle/schema';
import { DRIZZLE_PROVIDER } from '../../../drizzle/drizzle.provider';
import { BaseDrizzleRepository } from '../../common/repositories/base.drizzle.repository';
import { AssetType } from '../../common/types';

@Injectable()
export class AssetViewDrizzleRepository extends BaseDrizzleRepository<any> {
    constructor(
        @Inject(DRIZZLE_PROVIDER)
        db: NodePgDatabase<typeof schema>,
    ) {
        super(db, schema.vAssets);
    }

    async checkAssetBySerialNumberAndProductSku(
        resellerIds: string[],
        endUserIds: string[] | null,
        serialNumber: string,
        productSku: string,
    ): Promise<boolean> {
        const query = this.db
            .select({ id: schema.vAssets.id })
            .from(schema.vAssets)
            .where(
                and(
                    ...[
                        inArray(schema.vAssets.resellerId, resellerIds),
                        ...(endUserIds !== null ? [inArray(schema.vAssets.endUserId, endUserIds)] : []),
                        eq(schema.vAssets.serialNo, serialNumber),
                        eq(schema.vAssets.productSku, productSku),
                    ],
                ),
            )
            .orderBy(desc(schema.vAssets.startDate))
            .limit(1);

        const result = await query;
        return result.length > 0;
    }

    async findAssetBySerialNumber(resellerIds: string[], endUserIds: string[] | null, serialNumber: string) {
        const query = this.db
            .select()
            .from(schema.vAssets)
            .where(
                and(
                    ...[
                        inArray(schema.vAssets.resellerId, resellerIds),
                        ...(endUserIds !== null ? [inArray(schema.vAssets.endUserId, endUserIds)] : []),
                        eq(schema.vAssets.serialNo, serialNumber),
                    ],
                ),
            )
            .orderBy(desc(schema.vAssets.startDate))
            .limit(1);

        const result = await query;
        if (!result.length) {
            return null;
        }

        return this.mapToEntity(result[0]);
    }

    async findAssetById(resellerIds: string[], endUserIds: string[] | null, id: string) {
        const query = this.db
            .select()
            .from(schema.vAssets)
            .where(
                and(
                    ...[
                        inArray(schema.vAssets.resellerId, resellerIds),
                        ...(endUserIds !== null ? [inArray(schema.vAssets.endUserId, endUserIds)] : []),
                        eq(schema.vAssets.id, id),
                    ],
                ),
            )
            .orderBy(desc(schema.vAssets.startDate))
            .limit(1);

        const result = await query;
        if (!result.length) {
            return null;
        }

        return this.mapToEntity(result[0]);
    }

    protected mapToEntity(record: any): AssetType {
        return {
            ...record,
            service_name: record.serviceName,
            service_group_label: record.serviceGroupLabel,
            product_sku: record.productSku,
            end_user_id: record.endUserId ?? null,
            start_date: record.startDate ? new Date(record.startDate) : null,
            end_date: record.endDate ? new Date(record.endDate) : null,
            name: record.name ?? record.productName,
            quantity: record.quantity ? Number(record.quantity) : null,
            items_count: record.items_count ? Number(record.items_count) : null,
            reseller_price_final_sum: record.reseller_price_final_sum ? Number(record.reseller_price_final_sum) : null,
            end_customer_price_final_sum: record.end_customer_price_final_sum
                ? Number(record.end_customer_price_final_sum)
                : null,
            vendor_id: record.vendorId ?? null,
        } as AssetType;
    }
}
