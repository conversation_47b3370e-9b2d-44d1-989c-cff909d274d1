import { Inject, Injectable } from '@nestjs/common';
import { and, eq, or } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

import * as schema from '../../../../drizzle/schema';
import { DRIZZLE_PROVIDER } from '../../../drizzle/drizzle.provider';
import { BaseDrizzleRepository } from '../../common/repositories/base.drizzle.repository';

// Define the type for the data field
interface AssetEnrichmentData {
    coverageStatus?: string;
    coverageEntries?: {
        type?: string;
        status?: string;
        description?: string;
        serviceLevels?: {
            sku?: string;
            description?: string;
        }[];
        coverageEndDate?: string;
        coverageStartDate?: string;
    }[];
    technicalStatus?: string;
    coverageStatusUpdatedAt?: string;
    errorMessage?: string;
}

interface AssetEnrichmentEntity {
    id: string;
    serialNumber: string;
    productSku: string;
    countryCode?: string;
    isEnriched: boolean;
    updatedAt: string;
    data: AssetEnrichmentData;
    coverageStatus?: string;
}

@Injectable()
export class AssetEnrichmentRepository extends BaseDrizzleRepository<AssetEnrichmentEntity> {
    constructor(
        @Inject(DRIZZLE_PROVIDER)
        db: NodePgDatabase<typeof schema>,
    ) {
        super(db, schema.assetEnrichments);
    }

    async findAssetEnrichmentBySerialNumber(serialNumber: string, productSku: string | null = null) {
        return this.db
            .select()
            .from(schema.assetEnrichments)
            .where(
                and(
                    ...[
                        eq(schema.assetEnrichments.serialNumber, serialNumber),
                        ...(productSku ? [eq(schema.assetEnrichments.productSku, productSku)] : []),
                    ],
                ),
            );
    }

    async getLatestCoverageDate(serialNumber: string, productSku: string): Promise<string | null> {
        const items = await this.db
            .select({ data: schema.assetEnrichments.data })
            .from(schema.assetEnrichments)
            .where(
                and(
                    eq(schema.assetEnrichments.serialNumber, serialNumber),
                    eq(schema.assetEnrichments.productSku, productSku),
                ),
            )
            .limit(1);

        const item = items[0];
        if (!item) return null;

        const data = item.data as AssetEnrichmentData;
        if (!data?.coverageEntries || data.coverageEntries.length === 0) {
            return null;
        }

        let latestDate: Date | null = null;
        let latestDateString: string | null = null;

        for (const entry of data.coverageEntries) {
            if (!entry.coverageEndDate) continue;

            try {
                const currentDate = new Date(entry.coverageEndDate);
                if (!latestDate || currentDate > latestDate) {
                    latestDate = currentDate;
                    latestDateString = entry.coverageEndDate;
                }
            } catch {
                this.logger.error(
                    `Invalid date format in coverage entry for ${serialNumber}/${productSku}: ${entry.coverageEndDate}`,
                );
            }
        }

        return latestDateString;
    }

    async findLatestCoverageDates(
        identifiers: { serial_number: string; product_sku: string }[],
    ): Promise<Map<string, string | null>> {
        const resultsMap = new Map<string, string | null>();

        if (!identifiers || identifiers.length === 0) {
            return resultsMap;
        }

        // Build the WHERE conditions for the query
        const whereConditions = identifiers.map((id) =>
            and(
                eq(schema.assetEnrichments.serialNumber, id.serial_number),
                eq(schema.assetEnrichments.productSku, id.product_sku),
            ),
        );

        try {
            // Execute the query using Drizzle
            const enrichments = await this.db
                .select({
                    serial_number: schema.assetEnrichments.serialNumber,
                    data: schema.assetEnrichments.data,
                })
                .from(schema.assetEnrichments)
                .where(whereConditions.length > 1 ? or(...whereConditions) : whereConditions[0]);

            for (const row of enrichments) {
                const serialNumber = row.serial_number;
                const data = row.data as AssetEnrichmentData;
                let latestDate: Date | null = null;
                let latestDateString: string | null = null;

                if (data?.coverageEntries && Array.isArray(data.coverageEntries)) {
                    for (const entry of data.coverageEntries) {
                        if (!entry.coverageEndDate) continue;
                        try {
                            const currentDate = new Date(entry.coverageEndDate);
                            if (!latestDate || currentDate > latestDate) {
                                latestDate = currentDate;
                                latestDateString = entry.coverageEndDate;
                            }
                        } catch {
                            this.logger.error(
                                `Invalid date format in coverage entry for ${serialNumber}: ${entry.coverageEndDate}`,
                            );
                        }
                    }
                }

                const existingLatest = resultsMap.get(serialNumber);
                if (
                    !existingLatest ||
                    (latestDateString && (!existingLatest || new Date(latestDateString) > new Date(existingLatest)))
                ) {
                    resultsMap.set(serialNumber, latestDateString);
                }
            }
        } catch (error) {
            this.logger.error({ err: error }, 'Error fetching bulk coverage dates');
        }

        return resultsMap;
    }
}
