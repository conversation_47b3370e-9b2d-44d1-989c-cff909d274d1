import { Injectable, Logger } from '@nestjs/common';

import { Context } from '../../common/interfaces';
import { AssetEnrichmentRepository } from '../repositories/asset-enrichment.repository';

@Injectable()
export class AssetEnrichmentService {
    private readonly logger = new Logger(AssetEnrichmentService.name);

    constructor(private readonly assetEnrichmentRepository: AssetEnrichmentRepository) {}

    async getAssetEnrichment(serialNumber: string, productSku: string | null = null) {
        return this.assetEnrichmentRepository.findAssetEnrichmentBySerialNumber(serialNumber, productSku);
    }
}
