import { Inject, Injectable, Logger } from '@nestjs/common';

import { Context } from '../../common/interfaces';
import { EntitiesService } from '../../entities/entities.service';
import { AssetNotFoundBySerialNoException } from '../exceptions/asset-not-found-by-serial-no.exception';
import { AssetNotFoundBySerialNoAndSkuException } from '../exceptions/asset-not-found-by-serial-no-and-sku.exception';
import { AssetViewDrizzleRepository } from '../repositories';
import { AssetEnrichmentService } from './asset-enrichment.service';

@Injectable()
export class AssetsService {
    private readonly logger = new Logger(AssetsService.name);

    constructor(
        private readonly assetEnrichmentService: AssetEnrichmentService,
        @Inject(AssetViewDrizzleRepository)
        private readonly assetViewDrizzleRepository: AssetViewDrizzleRepository,
        private readonly entitiesService: EntitiesService,
    ) {}

    async getAsset(context: Context, serialNo: string) {
        const [byId, bySerialNumber] = await Promise.all([
            this.assetViewDrizzleRepository.findAssetById(context.resellerIds, context.endUserIds, serialNo),
            this.assetViewDrizzleRepository.findAssetBySerialNumber(context.resellerIds, context.endUserIds, serialNo),
        ]);

        const asset = bySerialNumber ?? byId;

        if (!asset) {
            throw new AssetNotFoundBySerialNoException(serialNo);
        }

        const [assetEnrichment, vendor, endUser] = await Promise.all([
            this.assetEnrichmentService.getAssetEnrichment(serialNo),
            this.entitiesService.getEntityById(asset.vendor_id, true),
            this.entitiesService.getEntityById(asset.end_user_id, true),
        ]);

        const data = {
            name: asset.name,
            productSku: asset.product_sku,
            serviceGroupLabel: asset.service_group_label,
            coverageStatus: assetEnrichment?.[0]?.coverageStatus ?? null,
            serialNo: bySerialNumber ? serialNo : null,
            startDate: asset.start_date,
            endDate: asset.end_date,
            vendor,
            endUser,
            _links: {
                contracts: {
                    uri: `/assets/${asset.serial_number}/contracts`,
                    name: 'Contracts',
                },
                quotes: {
                    uri: `/assets/${asset.serial_number}/quotes`,
                    name: 'Quotes',
                },
            },
        };

        return data;
    }

    async getAssetWarranty(context: Context, serialNumber: string, productSku: string) {
        const isAssetExist = await this.assetViewDrizzleRepository.checkAssetBySerialNumberAndProductSku(
            context.resellerIds,
            context.endUserIds,
            serialNumber,
            productSku,
        );

        if (!isAssetExist) {
            throw new AssetNotFoundBySerialNoAndSkuException(serialNumber, productSku);
        }

        return await this.assetEnrichmentService.getAssetEnrichment(serialNumber, productSku);
    }
}
