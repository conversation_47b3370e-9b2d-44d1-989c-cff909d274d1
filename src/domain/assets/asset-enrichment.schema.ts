import { sql } from 'drizzle-orm';
import { boolean, jsonb, pgTable, primaryKey, text, timestamp, varchar } from 'drizzle-orm/pg-core';

export const assetEnrichments = pgTable(
    'asset_enrichments',
    {
        serialNumber: text('serial_number').notNull(),
        productSku: text('product_sku').notNull(),
        countryCode: text('country_code'),
        isEnriched: boolean('is_enriched').default(false).notNull(),

        createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
        updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }).defaultNow().notNull(),

        data: jsonb().default({}).notNull(),
        coverageStatus: varchar('coverage_status', { length: 255 }),
    },
    (table) => [primaryKey({ columns: [table.serialNumber, table.productSku], name: 'asset_enrichments_pkey' })],
);
