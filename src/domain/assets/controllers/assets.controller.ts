import { <PERSON>, <PERSON>, Logger, <PERSON>m, Query, Res } from '@nestjs/common';
import { ApiBadRequestResponse, ApiNotFoundResponse, ApiOkResponse } from '@nestjs/swagger';
import { Response } from 'express';

import { RequestContext } from '../../../auth/context.decorator';
import { RequireResourceAction } from '../../../rbac/permissions.decorator';
import { BaseController } from '../../common/controllers/base.controller';
import { Context } from '../../common/interfaces';
import { ErrorModel } from '../../common/models/error.model';
import { Pagination } from '../../common/pagination/pagination.decorator';
import { PaginationParamsModel } from '../../common/pagination/pagination.model';
import { ContractsService } from '../../contracts/contracts.service';
import { ContractFilterModel } from '../../contracts/models/contract-filter.model';
import { ContractListModel } from '../../contracts/models/contract-list.model';
import { QuotesService } from '../../quotes';
import { QuoteFilterModel, QuoteListModel } from '../../quotes/models';
import { ActionType, ResourceType } from '../../users/entities';
import { AssetsService } from '../services';

@Controller('assets')
export class AssetsController extends BaseController {
    private readonly logger = new Logger(AssetsController.name);

    constructor(
        private readonly assetsService: AssetsService,
        private readonly contractsService: ContractsService,
        private readonly quotesService: QuotesService,
    ) {
        super();
    }

    @RequireResourceAction({ resource: ResourceType.ASSET, action: ActionType.READ })
    @Get('/:serialNo')
    @ApiOkResponse({
        description: 'Asset details',
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    @ApiNotFoundResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async getAssetDetails(
        @Res() res: Response,
        @RequestContext() context: Context,
        @Param() { serialNo }: { serialNo: string },
    ) {
        const data = await this.assetsService.getAsset(context, serialNo);

        return this.sendOk(res, data);
    }

    @RequireResourceAction({ resource: ResourceType.ASSET, action: ActionType.READ })
    @Get('warranty/:serialNumber/:productSku')
    @ApiOkResponse({
        description: 'Asset Warranty details',
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    @ApiNotFoundResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async getAssetWarrantyDetailsBySerialNumberAndProductSku(
        @Res() res: Response,
        @RequestContext() context: Context,
        @Param() { serialNumber, productSku }: { serialNumber: string; productSku: string },
    ) {
        const data = await this.assetsService.getAssetWarranty(context, serialNumber, productSku);

        return this.sendOk(res, data);
    }

    @RequireResourceAction(
        { resource: ResourceType.ASSET, action: ActionType.READ },
        { resource: ResourceType.CONTRACT, action: ActionType.READ },
    )
    @Get('/:serialNo/contracts')
    @ApiOkResponse({
        description: 'Get asset contracts',
        type: ContractListModel,
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    @ApiNotFoundResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async getAssetContracts(
        @Res() res: Response,
        @RequestContext() context: Context,
        @Param() { serialNo }: { serialNo: string },
        @Pagination() paginationParams: PaginationParamsModel,
        @Query() query: ContractFilterModel,
    ) {
        const model = await this.contractsService.getContractsByResellerIdAndSerialNumber(
            context.resellerIds,
            context.endUserIds,
            serialNo,
            paginationParams,
            query,
        );
        return this.sendOk(res, model.getData());
    }

    @RequireResourceAction(
        { resource: ResourceType.ASSET, action: ActionType.READ },
        { resource: ResourceType.QUOTE, action: ActionType.READ },
    )
    @Get('/:serialNo/quotes')
    @ApiOkResponse({
        description: 'Get asset quotes',
        type: QuoteListModel,
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    @ApiNotFoundResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async getAssetQuotes(
        @Res() res: Response,
        @RequestContext() context: Context,
        @Param() { serialNo }: { serialNo: string },
        @Pagination() paginationParams: PaginationParamsModel,
        @Query() query: QuoteFilterModel,
    ) {
        const model = await this.quotesService.getQuotesByResellerAndSerialNumber(
            context.resellerIds,
            context.endUserIds,
            serialNo,
            paginationParams,
            query,
        );
        return this.sendOk(res, model.getData());
    }
}
