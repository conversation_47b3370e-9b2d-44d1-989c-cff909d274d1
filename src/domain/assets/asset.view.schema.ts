import { sql } from 'drizzle-orm';
import { numeric, pgView, text, varchar } from 'drizzle-orm/pg-core';

export const vAssets = pgView('v_assets', {
    id: text(),
    vendorId: text('vendor_id'),
    productSku: text('product_sku'),
    serialNo: text('serial_no'),
    contractNo: text('contract_no'),
    contractStatus: text('contract_status'),
    serviceGroupSku: text('service_group_sku'),
    serviceGroupLabel: text('service_group_label'),
    serviceLevelSku: text('service_level_sku'),
    serviceName: text('service_name'),
    coverageStatus: varchar('coverage_status', { length: 255 }),
    quoteNo: text('quote_no'),
    quoteStatus: text('quote_status'),
    startDate: text('start_date'),
    endDate: text('end_date'),
    resellerId: text('reseller_id'),
    endUserId: text('end_user_id'),
    distributorId: text('distributor_id'),
    contractId: text('contract_id'),
    quoteId: text('quote_id'),
    productName: text('product_name'),
    resellerPriceFinal: numeric('reseller_price_final'),
    distributorPriceFinal: numeric('distributor_price_final'),
    endCustomerPriceFinal: numeric('end_customer_price_final'),
    quantity: numeric('quantity'),
    currency: text('currency'),
    supportLifeEndDate: text('support_life_end_date'),
}).as(
    sql`
        CREATE OR REPLACE VIEW v_assets AS
        SELECT
            v_contract_items.id,
            v_contract_items.vendor_id,
            v_contract_items.product_sku,
            v_contract_items.serial_no,
            v_contract_items.contract_no,
            v_contract_items.contract_status,
            NULL::text AS quote_no,
            NULL::text AS quote_status,
            v_contract_items.start_date,
            v_contract_items.end_date,
            v_contract_items.reseller_id,
            v_contract_items.end_user_id,
            v_contract_items.distributor_id,
            v_contract_items.contract_id,
            NULL::text AS quote_id,
            v_contract_items.product_name,
            v_contract_items.reseller_price_final,
            NULL::numeric AS distributor_price_final,
            v_contract_items.end_customer_price_final,
            v_contract_items.quantity,
            v_contract_items.currency,
            v_contract_items.coverage_status,
            v_contract_items.service_group_sku,
            v_contract_items.service_group_label,
            v_contract_items.service_level_sku,
            v_contract_items.service_name,
            v_contract_items.support_life_end_date
        FROM
            v_contract_items
        UNION
        SELECT
            v_quote_items.id,
            v_quote_items.vendor_id,
            v_quote_items.product_sku,
            v_quote_items.serial_no,
            NULL::text AS contract_no,
            NULL::text AS contract_status,
            v_quote_items.quote_no,
            v_quote_items.quote_status,
            v_quote_items.start_date,
            v_quote_items.end_date,
            v_quote_items.reseller_id,
            v_quote_items.end_user_id,
            v_quote_items.distributor_id,
            NULL::text AS contract_id,
            v_quote_items.quote_id,
            v_quote_items.product_name,
            v_quote_items.reseller_price_final,
            v_quote_items.distributor_price_final,
            v_quote_items.end_customer_price_final,
            v_quote_items.quantity,
            v_quote_items.currency,
            v_quote_items.coverage_status,
            v_quote_items.service_group_sku,
            v_quote_items.service_group_label,
            v_quote_items.service_level_sku,
            v_quote_items.service_name,
            v_quote_items.support_life_end_date
        FROM
            v_quote_items;
    `,
);
