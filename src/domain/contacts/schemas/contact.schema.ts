import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { ContactStatusEnum } from '../enums';

export const ContactSchema = extendApi(
    z.object({
        id: z.string(),
        entityId: z.string(),
        status: extendApi(z.nativeEnum(ContactStatusEnum).nullable().optional(), {
            description: 'Contact status',
        }),
        contactId: z.union([z.number(), z.string()]).optional().nullable(),
        contactReference: z.string().optional().nullable(),
        surname: z.string().optional().nullable(),
        givenName: z.string().optional().nullable(),
        emailAddress: z.string().optional().nullable(),
        businessPhone: z.string().optional().nullable(),
        active: z.boolean().optional(),
        type: z.string().optional(),
    }),
    {
        title: 'Contact',
        description: 'Contact model',
    },
);
