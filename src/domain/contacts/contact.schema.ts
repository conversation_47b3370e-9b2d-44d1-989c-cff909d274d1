import { index, jsonb, pgEnum, pgTable, text } from 'drizzle-orm/pg-core';

import { DataSourceEnum } from '../contracts/enums/data-source.enum';

export const contactStatus = pgEnum('contact_status', ['active', 'inactive']);

const dataSource = pgEnum('data_source', DataSourceEnum);

export const contacts = pgTable(
    'contacts',
    {
        id: text().primaryKey().notNull(),
        entityId: text('entity_id').notNull(),
        data: jsonb(),
        status: contactStatus().default('inactive').notNull(),

        source: dataSource('source').default(DataSourceEnum.iAsset).notNull(),
    },
    (table) => [index('idx_contacts_e_id').using('btree', table.entityId.asc().nullsLast().op('text_ops'))],
);
