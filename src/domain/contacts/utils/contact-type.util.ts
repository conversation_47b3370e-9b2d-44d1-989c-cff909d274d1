import { Quote } from '../../quotes';
import { QuoteContactTypeEnum } from '../enums/quote-contact-type.enum';
import { Contact } from '../repositories/contact.drizzle.repository';

export function getContactTypeByQuote(quote: Quote, contact: Contact): QuoteContactTypeEnum | null {
    const contactTypeMap: Record<string, QuoteContactTypeEnum> = {
        [String(quote.data.RenewalContactId)]: QuoteContactTypeEnum.DISTRIBUTOR_CONTACT,
        [String(quote.data.CustomerContactId)]: QuoteContactTypeEnum.RESELLER_CONTACT,
        [String(quote.data.EndUserContactId)]: QuoteContactTypeEnum.END_USER_CONTACT,
    };

    return contactTypeMap[contact.id] ?? null;
}
