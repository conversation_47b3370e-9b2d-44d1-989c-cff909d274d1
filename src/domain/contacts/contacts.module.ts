import { forwardRef, Module } from '@nestjs/common';

import { EntitiesModule } from '../entities/entities.module';
import { ContactListPresenter } from './presenters';
import { ContactDrizzleRepository } from './repositories/contact.drizzle.repository';

@Module({
    imports: [forwardRef(() => EntitiesModule)],
    providers: [ContactListPresenter, ContactDrizzleRepository],
    exports: [ContactListPresenter, ContactDrizzleRepository],
})
export class ContactsModule {}
