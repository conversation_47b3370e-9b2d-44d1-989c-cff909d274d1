import { Inject, Injectable } from '@nestjs/common';
import { eq, inArray } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

import * as schema from '../../../../drizzle/schema';
import { DRIZZLE_PROVIDER } from '../../../drizzle/drizzle.provider';
import { BaseDrizzleRepository } from '../../common/repositories/base.drizzle.repository';
import { ContactStatusEnum } from '../enums';

// Define a type for Contact based on the Drizzle schema
export type Contact = {
    id: string;
    entityId: string;
    data?: {
        ContactId?: number;
        EntityId?: number;
        ContactReference?: string;
        Surname?: string;
        GivenName?: string;
        EmailAddress?: string;
        BusinessPhone?: string;
        Active?: boolean;
        FirstName?: string;
        LastName?: string;
    } | null;
    status: ContactStatusEnum;
};

@Injectable()
export class ContactDrizzleRepository extends BaseDrizzleRepository<Contact> {
    constructor(
        @Inject(DRIZZLE_PROVIDER)
        db: NodePgDatabase<typeof schema>,
    ) {
        super(db, schema.contacts);
    }

    async findContactByIds(contactIds: string[] | number[]): Promise<Contact[]> {
        const ids = contactIds.map((id: string | number) => String(id));

        if (!ids?.length) {
            return [];
        }

        const results = await this.db.select().from(schema.contacts).where(inArray(schema.contacts.id, ids));

        return results.map((record) => {
            return {
                id: record.id,
                entityId: record.entityId,
                data: record.data,
                status: record.status === 'active' ? ContactStatusEnum.ACTIVE : ContactStatusEnum.INACTIVE,
            };
        });
    }

    async findContactById(contactId: string): Promise<Contact | null> {
        const results = await this.db.select().from(schema.contacts).where(eq(schema.contacts.id, contactId)).limit(1);

        if (results.length === 0) {
            return null;
        }

        const record = results[0];
        return {
            id: record.id,
            entityId: record.entityId,
            data: record.data,
            status: record.status === 'active' ? ContactStatusEnum.ACTIVE : ContactStatusEnum.INACTIVE,
        };
    }
}
