import { Entity, Enum, Index, ManyToOne, PrimaryKey, Property } from '@mikro-orm/core';

import { EntityEntity } from '../../entities/entities/entity.entity';
import { ContactStatusEnum } from '../enums';

@Entity({
    tableName: 'contacts',
})
@Index({ name: 'idx_contacts_e_id ', properties: ['entityId'] })
export class Contact {
    @PrimaryKey({ type: 'text' })
    id!: string;

    @Property({ type: 'text' })
    entityId!: string;

    @Enum({ items: () => ContactStatusEnum, nativeEnumName: 'contact_status', default: ContactStatusEnum.INACTIVE })
    status: ContactStatusEnum;

    @Property({ type: 'object', nullable: true })
    data?: {
        ContactId?: number;
        EntityId?: number;
        ContactReference?: string;
        Surname?: string;
        GivenName?: string;
        EmailAddress?: string;
        BusinessPhone?: string;
        Active?: boolean;
        FirstName?: string;
        LastName?: string;
    };

    @ManyToOne(() => EntityEntity, { nullable: true, persist: false })
    entity?: EntityEntity;
}
