import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { FeatureFlagsController } from './controllers/feature-flags.controller';
import { FeatureFlagGuard } from './guards/feature-flag.guard';
import { FeatureFlagRepository } from './repositories/feature-flag.repository';
import { FeatureFlagService } from './services/feature-flag.service';

@Module({
    imports: [ConfigModule],
    providers: [FeatureFlagRepository, FeatureFlagService, FeatureFlagGuard],
    exports: [FeatureFlagRepository, FeatureFlagService, FeatureFlagGuard],
    controllers: [FeatureFlagsController],
})
export class FeatureFlagsModule {}
