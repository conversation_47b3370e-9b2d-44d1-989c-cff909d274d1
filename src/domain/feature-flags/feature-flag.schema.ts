import { boolean, pgEnum, pgTable, primaryKey } from 'drizzle-orm/pg-core';

import { FeatureNameEnum } from './enums/feature-name.enum';

export const featureFlagName = pgEnum('feature_flag_name', FeatureNameEnum);

export const featureFlag = pgTable(
    'feature_flags',
    {
        name: featureFlagName(),
        enabled: boolean().default(true),
    },
    (table) => [primaryKey({ columns: [table.name], name: 'feature_flags_primary_key' })],
);
