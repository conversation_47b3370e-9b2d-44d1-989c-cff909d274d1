import { Inject, Injectable } from '@nestjs/common';
import { inArray, InferSelectModel } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

import * as schema from '../../../../drizzle/schema';
import { DRIZZLE_PROVIDER } from '../../../drizzle/drizzle.provider';
import { BaseDrizzleRepository } from '../../common/repositories/base.drizzle.repository';
import { FeatureNameEnum } from '../enums/feature-name.enum';

export type FeatureFlagDrizzle = Partial<InferSelectModel<typeof schema.featureFlag>>;

@Injectable()
export class FeatureFlagRepository extends BaseDrizzleRepository<any> {
    private readonly getFeatureFlagsPrepared;

    constructor(@Inject(DRIZZLE_PROVIDER) db: NodePgDatabase<typeof schema>) {
        super(db, schema.featureFlag);
        this.getFeatureFlagsPrepared = this.db
            .select({
                name: schema.featureFlag.name,
                enabled: schema.featureFlag.enabled,
            })
            .from(schema.featureFlag)
            .prepare('feature_flags_prepared');
    }

    async getFeatureFlags(): Promise<FeatureFlagDrizzle[]> {
        return await this.getFeatureFlagsPrepared.execute();
    }

    async getFeaturesByNames(names: FeatureNameEnum[]): Promise<FeatureFlagDrizzle[]> {
        const query = this.db
            .select({
                name: schema.featureFlag.name,
                enabled: schema.featureFlag.enabled,
            })
            .from(schema.featureFlag)
            .where(inArray(schema.featureFlag.name, names));

        return await query;
    }
}
