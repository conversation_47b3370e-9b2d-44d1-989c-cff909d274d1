import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

import { FEATURE_FLAGS_KEY } from '../decorators/feature-flag.decorator';
import { FeatureNameEnum } from '../enums/feature-name.enum';
import { FeatureIsDisabledException } from '../exceptions/feature-is-disabled.exception';
import { FeatureFlagService } from '../services/feature-flag.service';

@Injectable()
export class FeatureFlagGuard implements CanActivate {
    constructor(
        private readonly featureFlagService: FeatureFlagService,
        private readonly reflector: Reflector,
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const requiredFeatureFlags = this.reflector.get<FeatureNameEnum[]>(FEATURE_FLAGS_KEY, context.getHandler());

        if (!requiredFeatureFlags?.length) {
            return true;
        }

        const allFeaturesAreEnabled = await this.featureFlagService.isFeaturesEnabled(requiredFeatureFlags);

        if (!allFeaturesAreEnabled) {
            throw new FeatureIsDisabledException();
        }

        return true;
    }
}
