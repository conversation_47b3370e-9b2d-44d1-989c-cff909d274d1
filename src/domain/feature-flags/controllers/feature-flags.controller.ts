import { Controller, Get } from '@nestjs/common';
import { ApiOkResponse } from '@nestjs/swagger';

import { DisableAuthenticatedGuard } from '../../../auth/authenticated.guard';
import { DisablePermissionsGuard } from '../../../rbac/permissions.decorator';
import { BaseController } from '../../common/controllers/base.controller';
import { FeatureFlagDto } from '../dtos/feature-flag.dto';
import { FeatureFlagService } from '../services/feature-flag.service';

@Controller('feature-flags')
export class FeatureFlagsController extends BaseController {
    constructor(private readonly featureFlagService: FeatureFlagService) {
        super();
    }

    @Get('')
    @ApiOkResponse({
        description: 'Feature flag list',
        type: FeatureFlagDto,
        isArray: true,
    })
    @DisableAuthenticatedGuard()
    @DisablePermissionsGuard()
    async getFeatureFlags(): Promise<FeatureFlagDto[]> {
        return await this.featureFlagService.getFeatureFlags();
    }
}
