import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { FeatureNameEnum } from '../enums/feature-name.enum';

export const FeatureFlagSchema = extendApi(
    z.object({
        name: z.nativeEnum(FeatureNameEnum),
        enabled: z.boolean(),
    }),
);

export class FeatureFlagDto extends createZodDto(FeatureFlagSchema) {}
