import { Injectable } from '@nestjs/common';

import { FeatureFlagDto } from '../dtos/feature-flag.dto';
import { FeatureNameEnum } from '../enums/feature-name.enum';
import { FeatureFlagDrizzle, FeatureFlagRepository } from '../repositories/feature-flag.repository';

@Injectable()
export class FeatureFlagService {
    constructor(private readonly repository: FeatureFlagRepository) {}

    async getFeatureFlags(): Promise<FeatureFlagDto[]> {
        const result = await this.repository.getFeatureFlags();

        return result.map((feature) => this.entityToDto(feature));
    }

    async isFeaturesEnabled(names: FeatureNameEnum[]): Promise<boolean> {
        const features = await this.repository.getFeaturesByNames(names);

        const featuresMap = new Map(features.map((feature) => [feature.name, feature.enabled]));

        for (const featureName of names) {
            if (featuresMap.has(featureName) && !featuresMap.get(featureName)) {
                return false;
            }
        }

        return true;
    }

    private entityToDto(record: FeatureFlagDrizzle): FeatureFlagDto {
        return {
            enabled: record.enabled,
            name: record.name,
        };
    }
}
