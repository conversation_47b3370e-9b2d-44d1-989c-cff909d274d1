import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

export const CreateFileSchema = extendApi(
    z.object({
        file: extendApi(
            z
                .custom<Express.Multer.File>(
                    (file) => {
                        // Check if file exists and is an object
                        if (!file || typeof file !== 'object') return false;
                    },
                    {
                        path: ['attachPo'],
                        message: 'Invalid file type',
                    },
                )
                .describe('File'),
            {
                type: 'string',
                format: 'file',
            },
        ),
        type: z.enum(['quote-approve', 'quote-request', 'assets-import']),
    }),
    {
        title: 'CreateFile',
        description: 'Create File model',
    },
);

export class CreateFileDto extends createZodDto(CreateFileSchema) {}
