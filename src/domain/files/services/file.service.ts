import { InjectRepository } from '@mikro-orm/nestjs';
import { Inject, Injectable, Logger } from '@nestjs/common';

import { Context } from '../../common/interfaces';
import { ObjectStorageService } from '../../common/services';
import { CreateFileDto } from '../dtos';
import { File } from '../entities';
import { FileRepository } from '../repositories';

export const ceilTwoDecimals = (num) => {
    const result = Math.ceil(num * 100) / 100;
    return result.toFixed(2);
};

@Injectable()
export class FileService {
    private readonly logger = new Logger(FileService.name);

    constructor(
        @InjectRepository(File)
        private readonly fileRepository: FileRepository,
        @Inject(ObjectStorageService)
        private objectStorageService: ObjectStorageService,
    ) {}

    async createFile(context: Context, { file, type }: CreateFileDto) {
        this.logger.log('File upload started', {
            userId: context.user?.id,
            organizationId: context.organizationId,
            fileName: file.originalname,
            fileSize: file.size,
            mimeType: file.mimetype,
            bucketType: type,
        });

        if (!this.objectStorageService) {
            this.logger.error('File upload failed: Object storage service not configured', {
                userId: context.user?.id,
                organizationId: context.organizationId,
                fileName: file.originalname,
                bucketType: type,
            });
            return null;
        }

        try {
            const { id, url } = await this.objectStorageService.store(context, {
                file,
                bucket: type,
            });

            this.logger.debug('File stored in object storage', {
                userId: context.user?.id,
                organizationId: context.organizationId,
                fileName: file.originalname,
                fileId: id,
                fileUrl: url,
                bucketType: type,
            });

            const entry = await this.fileRepository.createFile({
                id,
                url,
            });

            this.logger.log('File upload completed successfully', {
                userId: context.user?.id,
                organizationId: context.organizationId,
                fileName: file.originalname,
                fileId: id,
                fileSize: file.size,
                mimeType: file.mimetype,
                bucketType: type,
                dbRecordId: entry.id,
            });

            return entry;
        } catch (error) {
            this.logger.error('File upload failed', {
                userId: context.user?.id,
                organizationId: context.organizationId,
                fileName: file.originalname,
                fileSize: file.size,
                mimeType: file.mimetype,
                bucketType: type,
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined,
            });
            throw error;
        }
    }
}
