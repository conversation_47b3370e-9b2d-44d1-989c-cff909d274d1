import { extendApi } from '@anatine/zod-openapi';

import { ContractSchema } from './contract.schema';

export const ContractListItemSchema = extendApi(
    ContractSchema.pick({
        id: true,
        contractNumber: true,
        sar: true,
        status: true,
        uiStatus: true,
        groupId: true,
        startDate: true,
        endDate: true,
        currency: true,
        resellerTotalPrice: true,
        endCustomerTotalPrice: true,
        endUser: true,
        vendor: true,
    }).setKey(
        '_links',
        ContractSchema.shape._links.pick({
            self: true,
            endUser: true,
        }),
    ),
    {
        title: 'ContractListItem',
        description: 'Contract list item model',
    },
);
