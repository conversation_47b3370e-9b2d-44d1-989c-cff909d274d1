import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { BaseFilterSchema } from '../../common/pagination/pagination.schema';

const CONTRACT_ITEM_SORTABLE_FIELDS = [
    'id',
    'serialNo',
    'productName',
    'productSku',
    'serviceLevelSku',
    'serviceGroupSku',
    'resellerPrice',
    // 'distributorPrice', // Hidden because of AH-101
    'endCustomerPrice',
    'createdAt',
    'updatedAt',
    'startDate',
    'endDate',
    'supportLifeEndDate',
];

export const ContractItemFilterSchema = extendApi(
    BaseFilterSchema.extend({
        search: z.string().optional().describe('Search query to filter by'),
        hasEndCustomerPrice: z.coerce.boolean().optional().describe('Filter by presence of end customer price'),
        hasEndOfServiceLifeDate: z.preprocess((value) => {
            if (value === 'true') return true;
            if (value === 'false') return false;
        }, z.boolean().optional().describe('Filter by presence of end of service life')),
        sortBy: extendApi(
            z
                .union([
                    z.literal('id'),
                    z.literal('serialNo'),
                    z.literal('productName'),
                    z.literal('productSku'),
                    z.literal('serviceLevelSku'),
                    z.literal('serviceGroupSku'),
                    z.literal('resellerPrice'),
                    // z.literal('distributorPrice'), // Hidden because of AH-101
                    z.literal('endCustomerPrice'),
                    z.literal('createdAt'),
                    z.literal('updatedAt'),
                    z.literal('name'),
                    z.literal('serialNumber'),
                    z.literal('quantity'),
                    z.literal('endCustomerPriceFinalSum'),
                    z.literal('resellerPriceFinalSum'),
                    z.literal('startDate'),
                    z.literal('endDate'),
                    z.literal('supportLifeEndDate'),
                ])
                .optional(),
            {
                description: 'Field to sort by',
                type: 'string',
                enum: CONTRACT_ITEM_SORTABLE_FIELDS,
                default: 'serialNo',
            },
        ),
    }),
    {
        title: 'ContractItemFilter',
        description: 'Filter parameters for contract items',
    },
);
