import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { BaseFilterSchema } from '../../common/pagination/pagination.schema';
import { ContractStatusEnum } from '../enums/contract-status.enum';

const contractStatusValues = Object.values(ContractStatusEnum);

export const CONTRACT_SORTABLE_FIELDS = [
    'id',
    'contractNo',
    'groupId',
    'sar',
    'status',
    'uiStatus',
    'startDate',
    'endDate',
    'createdAt',
    'updatedAt',
    'resellerTotalPrice',
    'endUser',
    'endUserName',
    'contractNumber',
    'vendor',
];

export const ContractFilterSchema = extendApi(
    BaseFilterSchema.extend({
        search: z.string().optional().describe('Search query to filter by'),

        // Support both string and array formats for status
        status: extendApi(
            z
                .union([z.array(z.nativeEnum(ContractStatusEnum)), z.nativeEnum(ContractStatusEnum)])
                .optional()
                .transform((val) => {
                    // Normalize to simple string when there's a single value
                    if (Array.isArray(val) && val.length === 1) {
                        return val[0];
                    }
                    // Keep as is for arrays with multiple values or single string values
                    return val;
                }),
            {
                description: 'CONTRACTS.STATUS',
                oneOf: [
                    { type: 'string', enum: contractStatusValues },
                    { type: 'array', items: { type: 'string', enum: contractStatusValues } },
                ],
            },
        ),

        endDateAfter: extendApi(z.string().optional().describe('End Date After'), {
            format: 'date',
        }),
        endDateBefore: extendApi(z.string().optional().describe('End Date Before'), {
            format: 'date',
        }),
        vendorNames: extendApi(z.array(z.string()).nullable().optional(), {
            description: 'List of vendors contract related to',
        }),
        sortBy: extendApi(
            z
                .union([
                    z.literal('id'),
                    z.literal('contractNo'),
                    z.literal('groupId'),
                    z.literal('sar'),
                    z.literal('status'),
                    z.literal('uiStatus'),
                    z.literal('startDate'),
                    z.literal('endDate'),
                    z.literal('createdAt'),
                    z.literal('updatedAt'),
                    z.literal('resellerTotalPrice'),
                    z.literal('endUser'),
                    z.literal('endUserName'),
                    z.literal('contractNumber'),
                    z.literal('vendor'),
                    z.literal('vendorName'),
                ])
                .optional()
                .transform((val) => {
                    if (val === 'contractNumber') return 'contractNo';
                    return val;
                }),
            {
                description: 'Field to sort by',
                type: 'string',
                enum: CONTRACT_SORTABLE_FIELDS,
                default: 'contractNo',
            },
        ),
    }),
    {
        title: 'ContractFilter',
        description: 'Contract filter parameters',
    },
);
