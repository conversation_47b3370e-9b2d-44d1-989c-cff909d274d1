import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { CurrencyEnum } from '../../common/enums';
import { ContractItemSchema } from './contract-item.schema';

export const ContractItemApiSchema = extendApi(
    ContractItemSchema.pick({
        id: true,
        itemNo: true,
        contractId: true,
        vendorId: true,
        serviceLevelSku: true,
        serviceName: true,
        serviceGroupSku: true,
        serviceGroupLabel: true,
        productSku: true,
        resellerPrice: true,
        endCustomerPrice: true,
        _links: true,
        _actions: true,
    })
        .setKey(
            'name',
            extendApi(z.string().nullable().optional(), {
                description: 'Product Name',
            }),
        )
        .setKey('serialNumber', ContractItemSchema.shape.serialNo)
        .setKey(
            'coverageStatus',
            extendApi(z.string().nullable().optional(), {
                description: 'Coverage Status',
            }),
        )
        .setKey(
            'currency',
            extendApi(z.nativeEnum(CurrencyEnum).nullable().optional(), {
                description: 'Currency code',
            }),
        )
        .setKey(
            'itemsCount',
            extendApi(z.number().nullable().optional(), {
                description: 'Items Count',
            }),
        )
        .setKey('startDate', z.string().datetime({ offset: true }).optional().nullable().describe('Start Date'))
        .setKey('endDate', z.string().datetime({ offset: true }).optional().nullable().describe('End Date'))
        .setKey(
            'supportLifeEndDate',
            z.string().datetime({ offset: true }).optional().nullable().describe('End OfService Life'),
        ),
    {
        title: 'ContractItem',
        description: 'Contract item model',
    },
);
