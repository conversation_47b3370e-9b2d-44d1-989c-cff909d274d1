import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { RelatedActionSchema } from '../../common/schemas/related-action.schema';
import { RelatedCompanyLinkSchema } from '../../common/schemas/related-company-link.schema';
import { RelatedItemLinkSchema } from '../../common/schemas/related-item-link.schema';

export const ContractItemSchema = extendApi(
    z.object({
        id: extendApi(z.string(), {
            description: 'Contract item ID',
        }),

        itemNo: z.union([z.string(), z.number()]).nullable().optional().describe('Item No'),

        contractId: extendApi(z.string().nullable().optional(), {
            description: 'Contract ID',
        }),

        vendorId: extendApi(z.string().nullable().optional(), {
            description: 'Vendor',
        }),

        serialNo: extendApi(z.string().nullable().optional(), {
            description: 'Serial Number',
        }),

        productSku: extendApi(z.string().nullable().optional(), {
            description: 'Product SKU',
        }),

        serviceLevelSku: extendApi(z.string().nullable().optional(), {
            description: 'Service Level SKU',
        }),

        serviceName: extendApi(z.string().nullable().optional(), {
            description: 'Service Name',
        }),

        serviceGroupSku: extendApi(z.string().nullable().optional(), {
            description: 'Service Group Sku',
        }),

        quantity: extendApi(z.number().default(1), {
            description: 'Quantity',
        }),

        serviceGroupLabel: extendApi(z.string().nullable().optional(), {
            description: 'Service Group Label',
        }),

        resellerPrice: z.number().nullable().optional().describe('Reseller Price (per unit)'),
        endCustomerPrice: z.number().nullable().optional().describe('End Customer Price (per unit)'),

        // *************************************************************************************************************
        // ** Links section
        // *************************************************************************************************************

        _links: extendApi(
            z.object({
                self: extendApi(RelatedItemLinkSchema.omit({ count: true }), { description: 'Self' }),

                vendor: extendApi(RelatedCompanyLinkSchema, {
                    description: 'Vendor',
                }),
            }),
            {
                description: 'Links',
            },
        ),

        // *************************************************************************************************************
        // ** Actions section
        // *************************************************************************************************************
        _actions: extendApi(
            z.object({
                edit: extendApi(RelatedActionSchema, {
                    description: 'Update',
                }),
            }),
            {
                description: 'Actions',
            },
        ),
    }),
    {
        title: 'Contract item',
        description: 'Contract item model',
    },
);
