import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { CurrencyEnum } from '../../common/enums/currency.enum';
import { RelatedCompanyLinkSchema } from '../../common/schemas';
import { RelatedItemLinkSchema } from '../../common/schemas/related-item-link.schema';
import { ContractUiStatusEnum } from '../enums';

export const ContractSchema = extendApi(
    z.object({
        id: z.string().describe('Contract ID'),

        contractNumber: z.string().nullable().optional().describe('Contract Number'),

        sar: z.string().nullable().optional().describe('SAR Number'),

        status: z.string().nullable().optional().describe('Contract IAsset Status'),

        uiStatus: z.nativeEnum(ContractUiStatusEnum).nullable().optional().describe('Contract status'),

        groupId: z.string().nullable().optional().describe('Group ID'),

        startDate: z.string().datetime({ offset: true }).nullable().optional().describe('Start Date'),

        endDate: z.string().datetime({ offset: true }).nullable().optional().describe('End Date'),

        currency: z.nativeEnum(CurrencyEnum).nullable().optional().describe('Currency code'),

        resellerTotalPrice: z.number().nullable().optional().describe('Reseller Total Price'),

        endCustomerTotalPrice: z.number().nullable().optional().describe('End Customer Total Price'),

        vendorId: z.string().nullable().optional().describe('Vendor ID'),

        distributorId: z.string().nullable().optional().describe('Distributor ID'),

        resellerId: z.string().nullable().optional().describe('Reseller ID'),

        endUserId: z.string().nullable().optional().describe('End User ID'),

        endUser: z
            .object({
                id: z.string().optional().nullable(),
                name: z.string().optional().nullable(),
            })
            .nullable()
            .optional()
            .describe('End User'),

        distributor: z
            .object({
                id: z.string().optional().nullable(),
                name: z.string().optional().nullable(),
            })
            .nullable()
            .optional()
            .describe('Distributor'),

        vendor: z
            .object({
                id: z.string().optional().nullable(),
                name: z.string().optional().nullable(),
            })
            .nullable()
            .optional()
            .describe('Vendor'),

        // *************************************************************************************************************
        // ** Links section
        // *************************************************************************************************************

        _links: extendApi(
            z.object({
                self: extendApi(RelatedItemLinkSchema.omit({ count: true }), { description: 'Self' }),

                assets: extendApi(RelatedItemLinkSchema, {
                    description: 'Assets',
                }),
                quotes: extendApi(RelatedItemLinkSchema, {
                    description: 'Contracts',
                }),
                contacts: extendApi(RelatedItemLinkSchema, {
                    description: 'Contacts',
                }),
                // notes: extendApi(RelatedItemLinkSchema, {
                //     description: 'Notes',
                // }),
                pdf: extendApi(RelatedItemLinkSchema, {
                    description: 'PDF',
                }),

                endUser: extendApi(RelatedCompanyLinkSchema, {
                    description: 'End User',
                }).optional(),

                parties: extendApi(RelatedItemLinkSchema, {
                    description: 'Parties',
                }).optional(),
            }),
            {
                description: 'Links',
            },
        ),
    }),
    {
        title: 'Contract',
        description: 'Contract model',
    },
);
