import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { createPaginatedResponseSchema } from '../../common/pagination/pagination.schema';
import { ContractListItemSchema } from './contract-list-item.schema';

// This model is not used in code because it's not possible to populate it with data
export const ContractListItemsSchema = extendApi(z.array(ContractListItemSchema), {
    title: 'ContractList',
    description: 'Contract list model',
});

export const ContractListSchema = createPaginatedResponseSchema(ContractListItemSchema);
