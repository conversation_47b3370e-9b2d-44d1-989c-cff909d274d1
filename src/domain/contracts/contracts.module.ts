import { MikroOrmModule } from '@mikro-orm/nestjs';
import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { RbacModule } from '../../rbac/rbac.module';
import { AssetEnrichment } from '../assets/entities/asset-enrichment.entity';
import { CommonModule } from '../common/common.module';
import { ContactsModule } from '../contacts/contacts.module';
import { EntitiesModule } from '../entities/entities.module';
import { FeatureFlagsModule } from '../feature-flags';
import { FilesModule } from '../files';
import { IAssetModule } from '../iasset/iasset.module';
import { ImportModule } from '../import/import.module';
import { OrganizationsModule } from '../organizations/organizations.module';
import { ProductsModule } from '../products/products.module';
import { QuotesModule } from '../quotes/quotes.module';
import { ContractImportService } from './contract-import.service';
import { ContractItemsService } from './contract-items.service';
import { ContractsController } from './contracts.controller';
import { ContractsService } from './contracts.service';
import { EmailService } from './email.service';
import { Contract } from './entities/contract.entity';
import { ContractItem } from './entities/contract-item.entity';
import { HandleContractJobsJob } from './jobs';
import { ContractStatusUpdatedListener } from './listeners';
import { ContractPresenter } from './presenters/contract.presenter';
import { ContractItemPresenter } from './presenters/contract-item.presenter';
import { ContractDrizzleRepository } from './repositories/contract.drizzle.repository';
import { ContractItemDrizzleRepository } from './repositories/contract-item.drizzle.repository';
import { ContractItemViewDrizzleRepository } from './repositories/contract-item-view.drizzle.repository';
import { ContractJobDrizzleRepository } from './repositories/contract-job.drizzle.repository';

@Module({
    imports: [
        ConfigModule,
        MikroOrmModule.forFeature([Contract, ContractItem, AssetEnrichment]),
        forwardRef(() => RbacModule),
        forwardRef(() => QuotesModule),
        forwardRef(() => IAssetModule),
        forwardRef(() => ContactsModule),
        forwardRef(() => CommonModule),
        forwardRef(() => EntitiesModule),
        forwardRef(() => OrganizationsModule),
        forwardRef(() => FilesModule),
        forwardRef(() => ImportModule),
        forwardRef(() => ProductsModule),
        forwardRef(() => FeatureFlagsModule),
    ],
    controllers: [ContractsController],
    providers: [
        ContractsService,
        ContractItemsService,
        ContractPresenter,
        ContractItemPresenter,
        ContractDrizzleRepository,
        ContractItemDrizzleRepository,
        ContractItemViewDrizzleRepository,
        ContractJobDrizzleRepository,
        EmailService,
        ContractStatusUpdatedListener,
        ContractImportService,
        HandleContractJobsJob,
    ],
    exports: [
        ContractsService,
        ContractItemsService,
        ContractPresenter,
        ContractItemPresenter,
        ContractDrizzleRepository,
        ContractItemDrizzleRepository,
        ContractItemViewDrizzleRepository,
        MikroOrmModule.forFeature([Contract, ContractItem]),
        ContractImportService,
    ],
})
export class ContractsModule {}
