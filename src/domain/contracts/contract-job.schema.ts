import { index, jsonb, pgTable, text, timestamp } from 'drizzle-orm/pg-core';

export type ContractJobDataField = {
    contracts: any[];
};

export const contractJobs = pgTable(
    'contract_jobs',
    {
        id: text().primaryKey().notNull(),
        groupId: text('group_id'),
        status: text('status'),
        expiresAt: timestamp('expires_at', { withTimezone: true, mode: 'string' }),
        data: jsonb().$type<ContractJobDataField>(),
        createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
        updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
    },
    (table) => [
        index('idx_contract_jobs_g_id').using('btree', table.groupId.asc().nullsLast().op('text_ops')),
        index('idx_contract_jobs_s_t').using('btree', table.status.asc().nullsLast().op('text_ops')),
        index('idx_contract_jobs_e_a').using('btree', table.expiresAt.asc().nullsLast().op('timestamp_ops')),
    ],
);
