import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';

import { EventNameEnum } from '../../common/enums/event-names.enum';
import { GenericDbEvent } from '../../common/interfaces/generic-db-event.interface';
import { FeatureFlagService, FeatureNameEnum } from '../../feature-flags';
import { ContractsService } from '../contracts.service';
import { ContractStatusEnum } from '../enums';
import { ContractJobDrizzleRepository } from '../repositories';

@Injectable()
export class ContractStatusUpdatedListener {
    private readonly logger = new Logger(ContractStatusUpdatedListener.name);

    constructor(
        private readonly contractService: ContractsService,
        private readonly contractJobRepository: ContractJobDrizzleRepository,
        private readonly featureFlagService: FeatureFlagService,
    ) {}

    @OnEvent(EventNameEnum.DB_WATCHER_CONTRACT_UPDATED)
    async handleContractUpdated(event: GenericDbEvent) {
        const isContractJobsEnabled = await this.featureFlagService.isFeaturesEnabled([FeatureNameEnum.CONTRACT_JOBS]);
        if (!isContractJobsEnabled) {
            this.logger.debug(`Contract jobs feature flag is not enabled, skipping`);
            return;
        }

        await this.handleStatusUpdated(event);
    }

    @OnEvent(EventNameEnum.DB_WATCHER_CONTRACT_CREATED)
    async handleContractCreated(event: GenericDbEvent) {
        const isContractJobsEnabled = await this.featureFlagService.isFeaturesEnabled([FeatureNameEnum.CONTRACT_JOBS]);
        if (!isContractJobsEnabled) {
            this.logger.debug(`Contract jobs feature flag is not enabled, skipping`);
            return;
        }

        await this.handleStatusUpdated(event);
    }

    private async handleStatusUpdated(event: GenericDbEvent) {
        this.logger.debug(`Received event:`, JSON.stringify(event, null, 2));

        const oldStatus = event.changes.changed_fields?.status?.old;
        const newStatus = event.changes.changed_fields?.status?.new;

        const isActiveStatus = oldStatus !== newStatus && newStatus === ContractStatusEnum.ACTIVE;
        if (!isActiveStatus) {
            this.logger.debug(`Contract status is not active, skipping`);
            return;
        }

        this.logger.debug(`Contract status is active, processing`);

        const contract = await this.contractService.getContractById(event.entityId);
        if (!contract) {
            this.logger.error(`Contract not found for ID: ${event.entityId}`);
            return;
        }

        const existingJob = await this.contractJobRepository.findJobByGroupId(contract.groupId);
        if (existingJob) {
            this.logger.debug(`Contract job already exists, updating: ${existingJob.id}`);
            await this.contractJobRepository.updateContractJob({
                id: existingJob.id,
                contract,
            });
            return;
        }

        this.logger.debug(`Contract job does not exist, creating`);
        const job = await this.contractJobRepository.createContractJob({
            groupId: contract.groupId,
            contract,
        });
        this.logger.debug(`Contract job created: ${job.id}`);
    }
}
