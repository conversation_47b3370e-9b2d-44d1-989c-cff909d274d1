import { InjectRepository } from '@mikro-orm/nestjs';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { User } from '@sentry/nestjs';
import { and, eq, inArray } from 'drizzle-orm';
import { alias, PgSelect, SelectedFields } from 'drizzle-orm/pg-core';
import { I18nContext } from 'nestjs-i18n';

import * as schema from '../../../drizzle/schema';
import { Context } from '../common/interfaces';
import { PaginationParamsModel } from '../common/pagination/pagination.model';
import { FreshdeskService, TemplateService, TicketPriority, TicketStatus, TicketType } from '../common/services';
import { IContactToModelInput } from '../contacts/interfaces/contact-to-model-input.interface';
import { ContactListModel } from '../contacts/models';
import { ContactListPresenter } from '../contacts/presenters';
import { ContactDrizzleRepository } from '../contacts/repositories/contact.drizzle.repository';
import { EntityTypeEnum } from '../entities/enums/entity-type.enum';
import { EntityDrizzleRepository } from '../entities/repositories/entity.drizzle.repository';
import { IAssetService } from '../iasset/iasset.service';
import { IAssetFile } from '../iasset/iasset-file';
import { OrganizationsService } from '../organizations';
import { ContractListSelect } from './consts/contract-list.select';
import { ContractItemsService } from './contract-items.service';
import { ContractCreateDto } from './dtos/contract-create.dto';
import { ContractGlobalSearchFilterDto } from './dtos/contract-global-search-filter.dto';
import { EmailService } from './email.service';
import { Contract } from './entities/contract.entity';
import { ContractNotFoundException } from './exceptions/contract-not-found.exception';
import { ContractStreamDownloadFailedException } from './exceptions/contract-stream-download-failed.exception';
import { ContractsGroupMismatchException } from './exceptions/contracts-group-mismatch.exception';
import { ContractModel } from './models/contract.model';
import { ContractFilterModel } from './models/contract-filter.model';
import { ContractListModel } from './models/contract-list.model';
import { ContractRequestQuoteModel } from './models/contract-request-quote.model';
import { ContractPresenter } from './presenters/contract.presenter';
import { ContractDrizzleRepository } from './repositories/contract.drizzle.repository';
import { ContractRepository } from './repositories/contract.repository';

@Injectable()
export class ContractsService {
    private readonly logger = new Logger(ContractsService.name);
    constructor(
        private readonly contractPresenter: ContractPresenter,
        private readonly iAssetService: IAssetService,
        private readonly contactListPresenter: ContactListPresenter,
        private readonly freshdeskService: FreshdeskService,
        private readonly templateService: TemplateService,
        private readonly emailService: EmailService,
        private readonly organizationService: OrganizationsService,
        private readonly contractItemsService: ContractItemsService,

        @InjectRepository(Contract)
        private readonly contractRepository: ContractRepository,

        @Inject(ContactDrizzleRepository)
        private readonly contactDrizzleRepository: ContactDrizzleRepository,

        @Inject(ContractDrizzleRepository)
        private readonly contractDrizzleRepository: ContractDrizzleRepository,

        @Inject(EntityDrizzleRepository)
        private readonly entityDrizzleRepository: EntityDrizzleRepository,
    ) {}

    async checkIfContractExistsByContractId(
        resellerIds: string[],
        endUserIds: string[] | null,
        contractId: string,
    ): Promise<void> {
        const res = await this.contractDrizzleRepository.checkIfContractExistsByContractId(
            resellerIds,
            endUserIds,
            contractId,
        );

        if (!res) {
            throw new ContractNotFoundException();
        }
    }

    async getContractByResellerIdAndContractId(
        resellerIds: string[],
        endUserIds: string[] | null,
        contractId: string,
        fields?: SelectedFields,
        queryModifier?: (query: PgSelect) => void,
    ): Promise<Contract> {
        const entity = await this.contractDrizzleRepository.findContractByResellerIdAndId(
            resellerIds,
            endUserIds,
            contractId,
            fields,
            queryModifier,
        );
        if (!entity) {
            throw new ContractNotFoundException();
        }

        return entity;
    }

    async getContractById(contractId: string): Promise<Contract> {
        const entity = await this.contractRepository.findContractById(contractId);
        if (!entity) {
            throw new ContractNotFoundException();
        }

        return entity;
    }

    async getContractModelById(
        resellerIds: string[],
        endUserIds: string[] | null,
        contractId: string,
    ): Promise<ContractModel> {
        const entity = await this.getContractByResellerIdAndContractId(
            resellerIds,
            endUserIds,
            contractId,
            {
                id: schema.contracts.id,
                contractNo: schema.contracts.contractNo,
                sar: schema.contracts.sar,
                status: schema.contracts.status,
                uiStatus: schema.contracts.uiStatus,
                groupId: schema.contracts.groupId,
                startDate: schema.contracts.startDate,
                endDate: schema.contracts.endDate,
                currency: schema.contracts.currency,
                endUserId: schema.contracts.endUserId,
                resellerId: schema.contracts.resellerId,
                distributorId: schema.contracts.distributorId,
                vendorId: schema.contracts.vendorId,
                endCustomerTotalPrice: schema.contracts.endCustomerTotalPrice,
                resellerTotalPrice: schema.contracts.resellerTotalPrice,
                endUser: {
                    id: schema.entities.id,
                    name: schema.entities.name,
                },
            },
            (query) => {
                query.leftJoin(schema.entities, eq(schema.entities.id, schema.contracts.endUserId));
            },
        );

        return this.contractPresenter.toFullModel(entity);
    }

    async globalSearch(resellerIds: string[], endUserIds: string[] | null, searchPhrase?: string) {
        return this.contractDrizzleRepository.globalSearch(resellerIds, endUserIds, searchPhrase);
    }

    async getContractsByResellerIds(
        resellerIds: string[],
        endUserIds: string[] | null,
        paginationParams: PaginationParamsModel,
        filter?: ContractFilterModel | ContractGlobalSearchFilterDto,
    ): Promise<ContractListModel> {
        const vendorSchema = alias(schema.entities, 'vendor');
        const entities = await this.contractDrizzleRepository.findContracts(
            resellerIds,
            endUserIds,
            paginationParams,
            filter,
            {
                ...ContractListSelect,
                vendorName: vendorSchema.name,
                vendor: {
                    id: vendorSchema.id,
                    name: vendorSchema.name,
                },
            },
            (query: PgSelect) => {
                query
                    .leftJoin(schema.entities, eq(schema.contracts.endUserId, schema.entities.id))
                    .innerJoin(
                        vendorSchema,
                        and(
                            eq(vendorSchema.id, schema.contracts.vendorId),
                            filter?.vendorNames?.length ? inArray(vendorSchema.name, filter.vendorNames) : undefined,
                        ),
                    );
            },
        );

        if (!entities) {
            return new ContractListModel();
        }

        const items = entities.data.map((entity) => this.contractPresenter.toListItemModel(entity));
        const model = new ContractListModel();
        model.setData(items, entities.meta);

        return model;
    }

    async getContractsByResellerAndEndUserId(
        resellerIds: string[],
        endUserIds: string[] | null,
        endUserId: string,
        paginationParams: PaginationParamsModel,
        filter?: ContractFilterModel,
    ): Promise<ContractListModel> {
        const vendorSchema = alias(schema.entities, 'vendor');

        const entities = await this.contractDrizzleRepository.findContractsByResellerAndEndUserId(
            resellerIds,
            endUserIds,
            endUserId,
            paginationParams,
            filter,
            {
                ...ContractListSelect,
                vendor: {
                    id: vendorSchema.id,
                    name: vendorSchema.name,
                },
            },
            (query: PgSelect) => {
                query
                    .leftJoin(schema.entities, eq(schema.contracts.endUserId, schema.entities.id))
                    .innerJoin(
                        vendorSchema,
                        and(
                            eq(vendorSchema.id, schema.contracts.vendorId),
                            filter?.vendorNames?.length ? inArray(vendorSchema.name, filter.vendorNames) : undefined,
                        ),
                    );
            },
        );

        if (!entities) {
            return new ContractListModel();
        }

        const items = entities.data.map((entity) => this.contractPresenter.toListItemModel(entity));
        const model = new ContractListModel();
        model.setData(items, entities.meta);

        return model;
    }

    async getContractsByQuoteId(
        resellerIds: string[],
        endUserIds: string[] | null,
        quoteId: string,
        paginationParams: PaginationParamsModel,
        filter?: ContractFilterModel,
    ): Promise<ContractListModel> {
        const entities = await this.contractDrizzleRepository.findContractsByQuote(
            resellerIds,
            endUserIds,
            quoteId,
            paginationParams,
            filter,
        );

        if (!entities) {
            return new ContractListModel();
        }

        const items = entities.data.map((entity) => this.contractPresenter.toListItemModel(entity));
        const model = new ContractListModel();
        model.setData(items, entities.meta);

        return model;
    }

    async getContractsByResellerIdAndSerialNumber(
        resellerIds: string[],
        endUserIds: string[] | null,
        serialNo: string,
        paginationParams: PaginationParamsModel,
        filter: ContractFilterModel,
    ) {
        const entities = await this.contractDrizzleRepository.ContractsByResellerIdAndSerialNumber(
            resellerIds,
            endUserIds,
            serialNo,
            paginationParams,
            filter,
        );

        if (!entities) {
            return new ContractListModel();
        }

        const items = entities.data.map((entity) => this.contractPresenter.toListItemModel(entity));
        const model = new ContractListModel();
        model.setData(items, entities.meta);

        return model;
    }

    async getContractsIncludingSerialNumber(context: Context, { serialNo }: { serialNo: string }) {
        return this.contractRepository.findAll({
            where: {
                contractItems: {
                    serialNo,
                },
            },
            orderBy: {
                endDate: 'desc',
            },
            populate: ['endUser', 'vendor'],
        });
    }

    async getContactsByContractId(contractId: string): Promise<ContactListModel> {
        const contract = await this.contractRepository.findContractById(contractId);

        if (!contract) {
            throw new ContractNotFoundException();
        }

        const contractContacts = contract?.data?.ContractContacts || [];
        const ids = Array.from(new Set([...(contract?.data?.ContractContacts || [])?.map((el) => el.ContactId)]));

        const contacts = await this.contactDrizzleRepository.findContactByIds(ids?.filter((id) => !!id));
        const input: IContactToModelInput[] = [];

        for (const contractContact of contractContacts) {
            const contactEntity = contacts.find((contact) => contact.id === String(contractContact.ContactId));

            if (!contactEntity) {
                continue;
            }

            input.push({
                ...contactEntity,
                type: contractContact.ContactTypeName,
            });
        }

        return this.contactListPresenter.toModel(input);
    }

    async getContractPdf(resellerIds: string[], endUserIds: string[] | null, contractId: string): Promise<IAssetFile> {
        const contractNo = await this.contractDrizzleRepository.findContractNumberByContractId(
            resellerIds,
            endUserIds,
            contractId,
        );
        if (!contractNo) {
            throw new ContractNotFoundException();
        }
        const iAssetFile = await this.iAssetService.getDownloadContractPdfFromIAsset(contractNo);
        if (!iAssetFile.getStream()) {
            throw new ContractStreamDownloadFailedException();
        }

        return iAssetFile;
    }

    async getMultipleContractsPdf(
        context: Context,
        contractNumbers: string[],
        contractPrintName?: string,
    ): Promise<IAssetFile> {
        const contracts = await this.contractDrizzleRepository.findContractsByContractNumbers(
            context.resellerIds,
            context.endUserIds,
            contractNumbers,
        );

        if (contracts.length === 0) {
            throw new ContractNotFoundException();
        }

        if (contracts.length !== contractNumbers.length) {
            throw new ContractNotFoundException();
        }

        const firstContractGroupId = contracts[0].groupId;
        if (firstContractGroupId) {
            for (const contract of contracts) {
                if (contract.groupId !== firstContractGroupId) {
                    throw new ContractsGroupMismatchException();
                }
            }
        }

        const languageCode = I18nContext.current().lang || context.user?.locale || undefined;

        const iAssetFile = await this.iAssetService.getDownloadMultipleContractsPdfFromIAsset(
            contractNumbers,
            contractPrintName,
            languageCode,
        );

        if (!iAssetFile.getStream()) {
            throw new ContractStreamDownloadFailedException();
        }

        return iAssetFile;
    }

    async requestQuote(context: Context, data: ContractRequestQuoteModel): Promise<void> {
        const { user: actor, entityId } = context;
        const entity = await this.entityDrizzleRepository.findById(entityId);
        const entityName = await this.entityDrizzleRepository.findEntityNameById(entityId);
        const customerId = data.customerId ?? null;
        const customerName = await this.entityDrizzleRepository.findEntityNameById(customerId);
        const organization = await this.organizationService.getOrganizationByEntityId(entityId);

        await this.createQuoteRequestFreshdeskTicket(
            actor,
            entityName,
            customerName,
            data.groupId,
            data.contractNo,
            data.requestForContractGroupId,
            data.generalRequest ?? null,
            data.files ?? [],
            entity.data?.AccountManagerId,
        );

        await this.emailService.sendRequestQuoteConfirmationEmail({
            firstName: actor.firstName,
            lastName: actor.lastName,
            email: actor.email,
            ccEmails: [],
            locale: actor.locale,
            customerName,
            assets: [],
            contractNo: data.contractNo,
            groupId: data?.requestForContractGroupId ? data.groupId : null,
            message: data?.generalRequest ?? null,
            organizationId: organization.id,
        });
    }

    async sendContractUpdatedNotifications(context: Context, { contracts }: { contracts: Contract[] }) {
        const contract = contracts[0];
        const organization = await this.organizationService.getOrganizationByEntityId(contract.resellerId);
        if (!organization) {
            this.logger.warn(
                `Organization not found for resellerId: ${contract.resellerId} for contract ${contract.id} while sending contract status updated email`,
            );
            return;
        }
        const customer = await this.entityDrizzleRepository.findById(contract.endUserId);
        if (!customer) {
            this.logger.warn(
                `Customer not found for endUserId: ${contract.endUserId} for contract ${contract.id} while sending contract status updated email`,
            );
            return;
        }

        const uniqueContacts = new Map<string, { ContactId?: string; EntityId?: number; EmailAddress?: string }>();
        for (const contract of contracts) {
            const contractContacts = contract.data?.ContractContacts || [];
            for (const contact of contractContacts) {
                if (contact.EmailAddress && !uniqueContacts.has(contact.ContactId)) {
                    uniqueContacts.set(contact.ContactId, contact);
                }
            }
        }
        if (uniqueContacts.size === 0) {
            this.logger.warn(
                `No contacts found for resellerId: ${contract.resellerId} for contract ${contract.id} while sending contract status updated email`,
            );
            return;
        }

        const entityIds = Array.from(uniqueContacts.values()).map((contact) => `${contact.EntityId}`);
        const entities = await this.entityDrizzleRepository.findByIds(entityIds);

        const validEmails: string[] = [];
        const entityMap = new Map(entities.map((entity) => [entity?.id, entity]));

        for (const contact of uniqueContacts.values()) {
            const entity = entityMap.get(`${contact.EntityId}`);
            if (entity?.type === EntityTypeEnum.Reseller) {
                validEmails.push(contact.EmailAddress);
            }
        }

        if (validEmails.length === 0) {
            return;
        }

        await this.emailService.sendContractUpdatedEmail({
            emails: validEmails,
            groupId: contract.groupId,
            contractNumbers: contracts.map((contract) => contract.contractNo),
            organizationId: organization.id,
            locale: organization.locale,
            customerName: customer.name,
        });
    }

    private async createQuoteRequestFreshdeskTicket(
        user: User,
        entityName: string,
        customerName: string,
        groupId: string | null,
        contractNo: string,
        requestForContractGroupId: string | null,
        message: string | null,
        files: any[],
        accountManagerId: string | null,
    ) {
        const resellerName = entityName;

        const companyId = await this.freshdeskService.createCompany({
            name: resellerName,
        });
        const contactId = await this.freshdeskService.createContact({
            name: `${user.firstName} ${user.lastName}`,
            email: user.email,
            companyId,
        });

        if (files) {
            for (const file of files) {
                file.objectType = 'ExpressMulterFile';
            }
        }

        const ticketId = await this.freshdeskService.createTicket({
            contactId,
            subject: `Quote request`,
            ticketType: TicketType.NEW_BUSINESS,
            ticketStatus: TicketStatus.OPEN,
            ticketPriority: TicketPriority.LOW,
            description: await this.templateService.render('freshdesk/contract-request-quote', {
                message: message,
                endCustomer: customerName,
                groupId,
                contractNo,
                requestForContractGroupId,
            }),
            attachments: files || [],
            tags: [this.freshdeskService.getCapitalizedEnvironment()],
            customerName: customerName,
            responderId: (await this.findAgentIdByAccountManagerId(accountManagerId)) ?? '',
        });

        return ticketId;
    }

    public async createContract(contractData: ContractCreateDto): Promise<ContractModel> {
        const createdContractId = await this.contractDrizzleRepository.createContract(contractData);
        const contract = await this.contractDrizzleRepository.findContractById(
            [contractData.resellerId],
            [contractData.endUserId],
            createdContractId,
        );
        return this.contractPresenter.toFullModel(contract);
    }

    private async findAgentIdByAccountManagerId(accountManagerId: string) {
        if (!accountManagerId) {
            return null;
        }

        const contact = await this.contactDrizzleRepository.findContactById(accountManagerId);
        if (!contact) {
            return null;
        }
        if (!contact.data?.EmailAddress) {
            return null;
        }
        const email = contact.data.EmailAddress;
        const agentId = await this.freshdeskService.findAgentIdByEmail(email);
        if (!agentId) {
            return null;
        }
        return agentId;
    }
}
