import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { I18nContext } from 'nestjs-i18n';

import { I18nPath } from '../../../i18n/generated/i18n.generated';
import { Context } from '../common/interfaces';
import { FileService } from '../files';
import { ImportStatusEnum } from '../import/enums/import-status.enum';
import { ImportRepository } from '../import/repositories/import.repository';
import { ProductDrizzleRepository } from '../products/repositories/product.drizzle.repository';
import { ContractImportCreateDto, ContractImportDto, ImportedContractItemDto } from './dtos/contract-import.dto';

@Injectable()
export class ContractImportService {
    constructor(
        private readonly fileService: FileService,
        private readonly importRepository: ImportRepository,
        private readonly productRepository: ProductDrizzleRepository,
    ) {}

    async createImport(files: Express.Multer.File[] = [], input: ContractImportCreateDto, context: Context) {
        const userId = context?.user?.id;

        const savedFiles = files.length
            ? await Promise.all(
                  files.map((file) =>
                      this.fileService.createFile(context, {
                          file,
                          type: 'assets-import',
                      }),
                  ),
              )
            : [];

        const validationResult = await this.validateImport(input);

        return await this.importRepository.createOne({
            status: ImportStatusEnum.VALIDATED,
            data: {
                assets: input.assets,
            },
            fileUrls: savedFiles.map((file) => file.url),
            createdByUserId: userId,
            success: validationResult === null,
        });
    }

    async validateImport(data: ContractImportDto) {
        const assets = data?.assets || [];
        const productSkuSet: Set<string> = new Set<string>();
        const serviceGroupSkuSet: Set<string> = new Set<string>();

        for (let i = 0; i < assets.length; i++) {
            const asset = assets[i];
            if (asset.productSku) {
                productSkuSet.add(asset.productSku);
            }

            if (asset.serviceGroupSku) {
                serviceGroupSkuSet.add(asset.serviceGroupSku);
            }
        }

        const productSkus = Array.from(productSkuSet);
        const serviceGroupSkus = Array.from(serviceGroupSkuSet);
        if (![...productSkus, ...serviceGroupSkus].length) {
            return;
        }

        const products = await this.productRepository.findBySkus([...productSkus, ...serviceGroupSkus]);
        const skusSet = new Set(products.map((product) => product.sku));

        const assetValidationErrors = [];
        for (let i = 0; i < assets.length; i++) {
            const asset = assets[i];
            const validationResult = this.validateAsset(skusSet, asset, i);
            if (validationResult) {
                assetValidationErrors.push(validationResult);
            }
        }

        if (assetValidationErrors.length) {
            throw new BadRequestException({
                statusCode: HttpStatus.BAD_REQUEST,
                message: I18nContext.current().t<I18nPath>('errors.COMMON.VALIDATION_FAILED'),
                errors: assetValidationErrors,
            });
        }
    }

    private validateAsset(skusSet: Set<string>, asset: ImportedContractItemDto, index: number) {
        const errors = [];
        const productSkuErrors = [];
        const serviceGroupSkuErrors = [];

        if (!asset.productSku) {
            productSkuErrors.push(I18nContext.current().t<I18nPath>('errors.ASSETS.PRODUCT_SKU_IS_EMPTY'));
        }

        if (asset.productSku && !skusSet.has(asset.productSku)) {
            productSkuErrors.push(I18nContext.current().t<I18nPath>('errors.ASSETS.PRODUCT_SKU_DOES_NOT_EXIST'));
        }

        if (!asset.serviceGroupSku) {
            serviceGroupSkuErrors.push(I18nContext.current().t<I18nPath>('errors.ASSETS.SERVICE_GROUP_SKU_IS_EMPTY'));
        }

        if (asset.serviceGroupSku && !skusSet.has(asset.serviceGroupSku)) {
            serviceGroupSkuErrors.push(
                I18nContext.current().t<I18nPath>('errors.ASSETS.SERVICE_GROUP_SKU_DOES_NOT_EXIST'),
            );
        }

        if (serviceGroupSkuErrors.length) {
            errors.push({ serviceGroupSku: serviceGroupSkuErrors });
        }

        if (productSkuErrors.length) {
            errors.push({ productSku: productSkuErrors });
        }

        if (!errors.length) {
            return null;
        }

        return {
            index,
            errors,
        };
    }
}
