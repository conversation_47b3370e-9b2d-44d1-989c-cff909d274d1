import { sql } from 'drizzle-orm';
import { integer, jsonb, numeric, pgView, text, varchar } from 'drizzle-orm/pg-core';

export const vContractItems = pgView('v_contract_items', {
    id: text(),
    itemNo: integer('item_no'),
    contractId: text('contract_id'),
    serialNo: text('serial_no'),
    productSku: text('product_sku'),
    serviceLevelSku: text('service_level_sku'),
    serviceGroupSku: text('service_group_sku'),
    resellerPrice: numeric('reseller_price', { precision: 20, scale: 4 }),
    endCustomerPrice: numeric('end_customer_price', { precision: 20, scale: 4 }),
    quantity: numeric({ precision: 10, scale: 0 }),
    resellerPriceFinal: numeric('reseller_price_final'),
    endCustomerPriceFinal: numeric('end_customer_price_final'),
    data: jsonb(),
    serviceGroupLabel: text('service_group_label'),
    productName: text('product_name'),
    serviceName: text('service_name'),
    contractNo: text('contract_no'),
    contractStatus: text('contract_status'),
    startDate: text('start_date'),
    endDate: text('end_date'),
    coverageStatus: varchar('coverage_status', { length: 255 }),
    currency: text(),
    distributorId: text('distributor_id'),
    vendorId: text('vendor_id'),
    endUserId: text('end_user_id'),
    resellerId: text('reseller_id'),
    supportLifeEndDate: text('support_life_end_date'),
}).as(
    sql`SELECT ci.id, ci.item_no, ci.contract_id, ci.vendor_id, ci.serial_no, ci.product_sku, ci.service_level_sku, ci.service_group_sku, ci.reseller_price, ci.end_customer_price, ci.quantity, ci.reseller_price AS reseller_price_final, ci.end_customer_price AS end_customer_price_final, ci.data, ci.service_group_label, cp.description AS product_name, sp.description AS service_name, c.contract_no, c.status AS contract_status, ae.coverage_status, c.currency, c.distributor_id, c.end_user_id, c.reseller_id, (ci.data ->> 'SupportLifeEndDate') AS support_life_end_date, (ci.data ->> 'ServiceStartDate') AS start_date, (ci.data ->> 'ServiceEndDate') AS end_date FROM contract_items ci LEFT JOIN products cp ON cp.sku = ci.product_sku AND cp.vendor_id = ci.vendor_id LEFT JOIN products sp ON sp.sku = ci.service_level_sku AND sp.vendor_id = ci.vendor_id LEFT JOIN contracts c ON c.id = ci.contract_id LEFT JOIN asset_enrichments ae ON ae.serial_number = ci.serial_no AND ae.product_sku = ci.product_sku`,
);
