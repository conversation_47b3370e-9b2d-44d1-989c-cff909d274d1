import { Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { EmailTemplateEnum } from '../common/enums';
import { EmailService as CommonEmailService } from '../common/services/email';
import { OrganizationsService } from '../organizations';

interface BaseEmailParams {
    organizationId: string;
    locale: string;
}

interface SendRequestQuoteConfirmationEmailParams extends BaseEmailParams {
    email: string;
    ccEmails: string[];
    firstName: string;
    lastName: string;
    customerName: string;
    groupId?: string;
    contractNo?: string;
    assets: {
        serial: string;
        productSku: string;
        serviceGroup: string;
        endDate: string;
    }[];
    message?: string;
}

interface SendContractUpdatedEmailParams extends BaseEmailParams {
    emails: string[];
    customerName: string;
    groupId: string;
    contractNumbers: string[];
}

@Injectable()
export class EmailService {
    private readonly logger = new Logger('QuoteEmailService');

    constructor(
        private configService: ConfigService,
        @Inject(CommonEmailService)
        private emailService: CommonEmailService,
        private organizationService: OrganizationsService,
    ) {}

    public async sendRequestQuoteConfirmationEmail({
        email,
        ccEmails,
        customerName,
        groupId,
        contractNo,
        assets,
        firstName,
        lastName,
        message,
        organizationId,
        locale,
    }: SendRequestQuoteConfirmationEmailParams) {
        try {
            this.logger.log(`Sending request quote confirmation email to ${email}`);

            const organization = await this.organizationService.getOrganizationById(organizationId);
            if (!organization) {
                this.logger.log('No organization found');
                return;
            }

            if (!organization.emailable) {
                this.logger.log('Organization is not emailable');
                return;
            }

            await this.emailService.send({
                toEmails: [email],
                ccEmails,
                locale,
                templateName: EmailTemplateEnum.QUOTE_REQUEST_CONFIRMATION,
                tags: ['Quote'],
                params: {
                    customerName,
                    groupId,
                    message,
                    assets,
                    contact: {
                        firstName,
                        lastName,
                    },
                    document: {
                        contractNo,
                    },
                },
            });
        } catch (error) {
            this.logger.error({ err: error, email }, `Error sending new quote available email to ${email}`);
        }
    }

    public async sendContractUpdatedEmail({
        emails,
        customerName,
        groupId,
        contractNumbers,
        organizationId,
        locale,
    }: SendContractUpdatedEmailParams) {
        try {
            this.logger.log(`Sending contract status updated email to ${emails.join(', ')}`);

            const organization = await this.organizationService.getOrganizationById(organizationId);
            if (!organization) {
                this.logger.log('No organization found');
                return;
            }

            if (!organization.emailable) {
                this.logger.log('Organization is not emailable');
                return;
            }

            await this.emailService.send({
                toEmails: emails,
                locale,
                templateName: EmailTemplateEnum.CONTRACT_STATUS_UPDATED,
                tags: ['Quote'],
                params: {
                    customerName,
                    groupId,
                    contractNumbers,
                },
            });
        } catch (error) {
            this.logger.error(`Error sending contract status updated email to ${emails.join(', ')}`, error);
        }
    }
}
