import { index, integer, jsonb, numeric, pgEnum, pgTable, text, timestamp } from 'drizzle-orm/pg-core';

import { DataSourceEnum } from './enums/data-source.enum';

const dataSource = pgEnum('data_source', DataSourceEnum);

export type ContractItemDataField = {
    [x: string]: any;

    ContractItemId?: number | string;
    ContractItemNo?: number;
    ServiceProductID?: number; // Service Level Product ID - Service Level Description can be extracted from there
    ServiceProductSku?: string; // Service Level SKU
    ServiceLevel?: string; // Service Group Description
    Quantity?: number; // Units quantity
    SupplierId?: number | string; // Vendor ID
    SupplierContractNo?: string;
    CoveredProductId?: number | string; // Product ID
    CoveredProductSku?: string; // Product SKU

    CustomerInvoiceAmount?: number | null; // [DEPRECATED] Reseller Price (already multiplied by quantity) - USE TesediResellerPrice INSTEAD
    CustomerRetailPrice?: number | null; // [DEPRECATED] End Customer Price (already multiplied by quantity) - USE TesediEndCustomerPrice INSTEAD
    TesediResellerPrice?: number;
    TesediEndCustomerPrice?: number;
    TesediResellerPricePerUnit?: string;
    TesediEndCustomerPricePerUnit?: string;

    EndUserSiteId?: number | string;
    EndUserEntityId?: number | string;
    EndUserContactId?: number | string;
    RenewalContractItemId?: number | string;
    AssetId?: string | null;
    SAR?: string;
    SAID?: string;
    DocId?: string;
    DocType?: string;
    EquipNo?: string;
    SvcLvlSku?: string; // Service Group SKU
    SvcLvlDesc?: string;
    MonthlyListPrice?: string;
    MonthlyDisc?: string;
    MonthlyNetPrice?: string;
    MonthlyListPriceEC?: string;
    EnvID?: string;
    SalesOrg?: string;
    PSPId?: string;
    PSPName?: string;
    GroupID?: string;
    MyDisc?: string;
    SndDisc?: string;
    PpDisc?: string;
    HwContact?: string;
    SwContact?: string;
    SysContact?: string;
    ProductLine?: string;
    ProductType?: string;
    SupportLifeEndDate?: string;
};

export const contractItems = pgTable(
    'contract_items',
    {
        id: text().primaryKey().notNull(),
        itemNo: integer('item_no'),
        contractId: text('contract_id'),
        vendorId: text('vendor_id'),
        serialNo: text('serial_no'),
        productSku: text('product_sku'),
        data: jsonb().$type<ContractItemDataField>(),
        serviceLevelSku: text('service_level_sku'),
        serviceGroupSku: text('service_group_sku'),
        serviceGroupLabel: text('service_group_label'),
        resellerPrice: numeric('reseller_price', { precision: 20, scale: 4 }),
        endCustomerPrice: numeric('end_customer_price', { precision: 20, scale: 4 }),
        quantity: numeric({ precision: 10, scale: 0 }),

        createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
        updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }).defaultNow().notNull(),

        source: dataSource('source').default(DataSourceEnum.iAsset).notNull(),
    },
    (table) => [
        index('idx_contract_items_c_id').using('btree', table.contractId.asc().nullsLast().op('text_ops')),
        index('idx_contract_items_p_sku').using('btree', table.productSku.asc().nullsLast().op('text_ops')),
        index('idx_contract_items_s_no').using('btree', table.serialNo.asc().nullsLast().op('text_ops')),
    ],
);
