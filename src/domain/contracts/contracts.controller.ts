import {
    BadRequestException,
    Body,
    Controller,
    Get,
    Logger,
    Param,
    Post,
    Query,
    Res,
    UploadedFiles,
    UseInterceptors,
} from '@nestjs/common';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { ApiBadRequestResponse, ApiNotFoundResponse, ApiOkResponse, ApiOperation } from '@nestjs/swagger';
import { Response } from 'express';

import { RequestContext } from '../../auth/context.decorator';
import { RequireResourceAction } from '../../rbac/permissions.decorator';
import { BaseController } from '../common/controllers/base.controller';
import { Context } from '../common/interfaces';
import { AssetListModel, ErrorModel, ExportFormatModel } from '../common/models';
import { Pagination } from '../common/pagination/pagination.decorator';
import { PaginationParamsModel } from '../common/pagination/pagination.model';
import { ExportFormat } from '../common/services';
import { ContactListModel } from '../contacts/models';
import { EntitiesService } from '../entities/entities.service';
import { EntityListModel } from '../entities/models/entity-list.model';
import { QuotesService } from '../quotes';
import { QuoteFilterModel, QuoteListModel } from '../quotes/models';
import { ActionType, ResourceType } from '../users/entities';
import { ContractImportService } from './contract-import.service';
import { ContractItemsService } from './contract-items.service';
import { ContractsService } from './contracts.service';
import { ContractBatchDownloadDto } from './dtos/contract-batch-download.dto';
import { ContractCreateDto } from './dtos/contract-create.dto';
import { ContractImportCreateDto, ContractImportDto } from './dtos/contract-import.dto';
import { ContractItemsCreateDto } from './dtos/contract-items-create.dto';
import { ContractModel } from './models/contract.model';
import { ContractFilterModel } from './models/contract-filter.model';
import { ContractIdParamModel } from './models/contract-id-param.model';
import { ContractItemFilterModel } from './models/contract-item-filter.model';
import { ContractListModel } from './models/contract-list.model';
import { ContractRequestQuoteFilesModel, ContractRequestQuoteModel } from './models/contract-request-quote.model';

@Controller('contracts')
export class ContractsController extends BaseController {
    private readonly logger = new Logger(ContractsController.name);

    constructor(
        private readonly contractsService: ContractsService,
        private readonly contractItemsService: ContractItemsService,
        private readonly quotesService: QuotesService,
        private readonly entitiesService: EntitiesService,
        private readonly contractImportService: ContractImportService,
    ) {
        super();
    }

    @RequireResourceAction({ resource: ResourceType.CONTRACT, action: ActionType.READ })
    @Get('/')
    @ApiOkResponse({
        description: 'Contracts list',
        type: ContractListModel,
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    @ApiNotFoundResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async getContracts(
        @Pagination() paginationParams: PaginationParamsModel,
        @Res() res: Response,
        @Query() query: ContractFilterModel,
        @RequestContext() context: Context,
    ) {
        const model = await this.contractsService.getContractsByResellerIds(
            context.resellerIds,
            context.endUserIds,
            paginationParams,
            query,
        );
        return this.sendOk(res, model.getData());
    }

    @RequireResourceAction({ resource: ResourceType.CONTRACT, action: ActionType.READ })
    @Get('batch-download-pdf')
    @ApiOperation({ summary: 'Download multiple contracts as a single PDF' })
    @ApiOkResponse({
        description: 'Combined PDF file',
        content: {
            'application/pdf': {
                schema: {
                    type: 'string',
                    format: 'binary',
                },
            },
        },
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async batchDownloadContractsPdf(
        @Res() res: Response,
        @Query() query: ContractBatchDownloadDto,
        @RequestContext() context: Context,
    ) {
        const iAssetFile = await this.contractsService.getMultipleContractsPdf(
            context,
            query.contractNumbers,
            query.contractPrintName,
        );
        const stream = iAssetFile.getStream();
        return this.sendStream(res, stream, 'application/pdf', iAssetFile.getName());
    }

    @RequireResourceAction({ resource: ResourceType.CONTRACT, action: ActionType.READ })
    @Get(':contractId')
    @ApiOkResponse({
        description: 'The contract object',
        type: ContractModel,
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async getContractById(
        @Res() res: Response,
        @Param() params: ContractIdParamModel,
        @RequestContext() context: Context,
    ) {
        const model = await this.contractsService.getContractModelById(
            context.resellerIds,
            context.endUserIds,
            params.contractId,
        );
        return this.sendOk(res, model.getData());
    }

    @RequireResourceAction({ resource: ResourceType.CONTRACT, action: ActionType.READ })
    @Get(':contractId/pdf')
    @ApiOkResponse({
        description: 'PDF file',
        content: {
            'application/pdf': {
                schema: {
                    type: 'string',
                    format: 'binary',
                },
            },
        },
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async getContractPdf(
        @Res() res: Response,
        @Param() params: ContractIdParamModel,
        @RequestContext() context: Context,
    ) {
        const iAssetFile = await this.contractsService.getContractPdf(
            context.resellerIds,
            context.endUserIds,
            params.contractId,
        );
        const stream = iAssetFile.getStream();
        return this.sendStream(res, stream, 'application/pdf', iAssetFile.getName());
    }

    @RequireResourceAction({ resource: ResourceType.CONTRACT_ITEM, action: ActionType.READ })
    @Get(':contractId/assets')
    @ApiOperation({
        summary: 'Get assets by contract ID',
    })
    @ApiOkResponse({
        description: 'Success',
        type: AssetListModel,
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async getAssetsByContractId(
        @Res() res: Response,
        @Param() params: ContractIdParamModel,
        @Pagination() paginationParams: PaginationParamsModel,
        @Query() filter: ContractItemFilterModel,
        @RequestContext() context: Context,
    ) {
        const model = await this.contractItemsService.getAssetsByContractId(
            context.resellerIds,
            context.endUserIds,
            params.contractId,
            paginationParams,
            filter,
        );
        return this.sendOkWithPagination(res, model.getData(), model.getMeta(), Boolean(paginationParams?.limit));
    }

    @RequireResourceAction({ resource: ResourceType.CONTRACT, action: ActionType.READ })
    @Get(':contractId/assets/export')
    @ApiOperation({ summary: 'Export assets by contract ID as CSV or XLSX' })
    @ApiOkResponse({
        description: 'Assets exported successfully',
        content: {
            'text/csv': {
                schema: {
                    type: 'string',
                    format: 'binary',
                },
            },
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': {
                schema: {
                    type: 'string',
                    format: 'binary',
                },
            },
        },
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async exportAssetsByContractId(
        @Res() res: Response,
        @Param() params: ContractIdParamModel,
        @Query() filter: ContractItemFilterModel,
        @Query() exportFormat: ExportFormatModel,
        @RequestContext() context: Context,
    ) {
        const format = exportFormat.format as ExportFormat;
        const result = await this.contractItemsService.exportAssetsByContractId(
            context.resellerIds,
            context.endUserIds,
            params.contractId,
            format,
            filter,
        );
        const contentType =
            format === ExportFormat.CSV
                ? 'text/csv'
                : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';

        return this.sendFile(res, result.data, result.filename, contentType);
    }

    @RequireResourceAction({ resource: ResourceType.CONTACT, action: ActionType.READ })
    @Get(':contractId/contacts')
    @ApiOkResponse({
        description: 'The contacts list',
        type: ContactListModel,
    })
    async getContactsByContractId(@Res() res: Response, @Param() params: ContractIdParamModel) {
        const model = await this.contractsService.getContactsByContractId(params.contractId);
        return this.sendOk(res, model.getData());
    }

    @RequireResourceAction(
        { resource: ResourceType.QUOTE, action: ActionType.READ },
        { resource: ResourceType.CONTRACT, action: ActionType.READ },
    )
    @Get(':contractId/quotes')
    @ApiOkResponse({
        description: 'Quotes list',
        type: QuoteListModel,
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async getQuotes(
        @Pagination() paginationParams: PaginationParamsModel,
        @Res() res: Response,
        @Param() params: ContractIdParamModel,
        @Query() query: QuoteFilterModel,
        @RequestContext() context: Context,
    ) {
        await this.contractsService.checkIfContractExistsByContractId(
            context.resellerIds,
            context.endUserIds,
            params.contractId,
        );

        const model = await this.quotesService.getQuotesByContract(
            context.resellerIds,
            context.endUserIds,
            params.contractId,
            paginationParams,
            query,
        );
        return this.sendOk(res, model.getData());
    }

    @RequireResourceAction({ resource: ResourceType.ENTITY, action: ActionType.READ })
    @Get(':contractId/entities')
    @ApiOkResponse({
        description: 'Entity list',
        type: EntityListModel,
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async getEntities(@Res() res: Response, @Param() params: ContractIdParamModel, @RequestContext() context: Context) {
        const model = await this.entitiesService.findByContractId(
            context.resellerIds,
            context.endUserIds,
            params.contractId,
        );

        return this.sendOkWithPagination(res, model.getData().data, model.getData().meta, true);
    }

    @RequireResourceAction(
        { resource: ResourceType.QUOTE, action: ActionType.CREATE },
        { resource: ResourceType.CONTRACT, action: ActionType.READ },
    )
    @Post('/request-quote')
    @ApiOkResponse()
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    @UseInterceptors(FileFieldsInterceptor([{ name: 'files', maxCount: 10 }]))
    async requestQuote(
        @RequestContext() context: Context,
        @Res() res: Response,
        @Body() body: ContractRequestQuoteModel,
        @UploadedFiles() files: ContractRequestQuoteFilesModel,
    ) {
        const correlationId = res.req.headers['x-correlation-id'] as string;
        const filesCount = files?.files?.length || 0;

        this.logger.log('Quote request started', {
            correlationId,
            userId: context.user.id,
            organizationId: context.organizationId,
            contractNo: body.contractNo,
            filesCount,
        });

        try {
            body.files = files?.files;

            await this.contractsService.requestQuote(context, body);

            this.logger.log('Quote request completed successfully', {
                correlationId,
                userId: context.user.id,
                organizationId: context.organizationId,
                contractNo: body.contractNo,
                filesCount,
            });

            return this.sendOk(res, null);
        } catch (error) {
            this.logger.error('Quote request failed', {
                correlationId,
                userId: context.user.id,
                organizationId: context.organizationId,
                contractNo: body.contractNo,
                filesCount,
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined,
            });
            throw error;
        }
    }

    @Post('import')
    @UseInterceptors(FileFieldsInterceptor([{ name: 'files' }]))
    async createImport(
        @Res() res: Response,
        @UploadedFiles() files: ContractRequestQuoteFilesModel,
        @Body() body: ContractImportCreateDto,
        @RequestContext() context: Context,
    ) {
        const result = await this.contractImportService.createImport(files.files, body, context);

        return this.sendOk(res, result);
    }

    @Post('import/validate')
    async validateImport(@Body() body: ContractImportDto, @Res() res: Response) {
        await this.contractImportService.validateImport(body);

        return this.sendOk(res, { success: true });
    }

    @RequireResourceAction(
        { resource: ResourceType.CONTRACT, action: ActionType.CREATE },
        { resource: ResourceType.CONTRACT_ITEM, action: ActionType.CREATE },
    )
    @Post()
    @ApiOkResponse({
        description: 'The contract object',
        type: ContractModel,
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async createContract(@Res() res: Response, @RequestContext() context: Context, @Body() body: ContractCreateDto) {
        if (body.resellerId && !context.resellerIds.includes(body.resellerId)) {
            throw new BadRequestException(`Provided reseller is not allowed for this user`);
        }

        const result = await this.contractsService.createContract(body);

        return this.sendOk(res, result);
    }

    @RequireResourceAction(
        { resource: ResourceType.CONTRACT, action: ActionType.CREATE },
        { resource: ResourceType.CONTRACT_ITEM, action: ActionType.CREATE },
    )
    @Post('assets')
    @ApiOperation({
        summary: 'Add assets to contract',
    })
    @ApiOkResponse()
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async addAssetsToContract(
        @Res() res: Response,
        @RequestContext() context: Context,
        @Body() body: ContractItemsCreateDto,
    ) {
        // Try to load contract to check if it exists and belongs to the user
        await this.contractsService.checkIfContractExistsByContractId(
            context.resellerIds,
            context.endUserIds,
            body.contractId,
        );

        await this.contractItemsService.createContractItems(body.contractId, body);

        return this.sendOk(res, null);
    }
}
