import { Entity, EntityRepositoryType, Enum, Index, ManyToOne, OneToMany, PrimaryKey, Property } from '@mikro-orm/core';

import { CurrencyEnum } from '../../common/enums/currency.enum';
import { EntityEntity } from '../../entities/entities/entity.entity';
import { ContractDataField } from '../contract.schema';
import { ContractUiStatusEnum } from '../enums';
import { ContractRepository } from '../repositories/contract.repository';
import { ContractItem } from './contract-item.entity';

@Entity({
    tableName: 'contracts',
    repository: () => ContractRepository,
})
@Index({ name: 'idx_contracts_r_id', properties: ['resellerId'] })
export class Contract {
    [EntityRepositoryType]?: ContractRepository;

    @PrimaryKey({ type: 'text' })
    id: string;

    @Index()
    @Property({ type: 'text', nullable: true })
    contractNo?: string;

    @Property({ type: 'text', nullable: true })
    sar?: string;

    @Property({ type: 'text', nullable: true })
    status?: string;

    @Index()
    @Enum({ items: () => ContractUiStatusEnum, nativeEnumName: 'contract_ui_status', nullable: true })
    uiStatus?: ContractUiStatusEnum;

    @Property({ type: 'text', nullable: true })
    groupId?: string;

    @Property({ type: 'text', nullable: true })
    startDate?: Date;

    @Property({ type: 'text', nullable: true })
    endDate?: Date;

    @Property({ type: 'text', nullable: true })
    currency?: CurrencyEnum;

    @Property({ type: 'numeric', precision: 20, scale: 4, nullable: true })
    resellerTotalPrice?: number;

    @Property({ type: 'numeric', precision: 20, scale: 4, nullable: true })
    endCustomerTotalPrice?: number;

    @Index()
    @Property({ type: 'text', nullable: true })
    vendorId?: string;

    @Index()
    @Property({ type: 'text', nullable: true })
    distributorId?: string;

    @Index()
    @Property({ type: 'text', nullable: true })
    resellerId?: string;

    @Index()
    @Property({ type: 'text', nullable: true })
    endUserId?: string;

    @Property({ type: 'object', nullable: true })
    data?: ContractDataField;

    @OneToMany(() => ContractItem, (contractItem) => contractItem.contract, { nullable: true, persist: false })
    contractItems?: ContractItem[];

    @ManyToOne(() => EntityEntity, { nullable: true, persist: false })
    distributor?: EntityEntity;

    @ManyToOne(() => EntityEntity, { nullable: true, persist: false })
    reseller?: EntityEntity;

    @ManyToOne(() => EntityEntity, { nullable: true, persist: false })
    endUser?: EntityEntity;

    @ManyToOne(() => EntityEntity, { nullable: true, persist: false })
    vendor?: EntityEntity;
}
