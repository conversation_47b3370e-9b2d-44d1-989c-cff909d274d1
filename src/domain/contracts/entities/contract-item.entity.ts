import { Entity, EntityRepositoryType, Index, ManyToOne, OneToMany, PrimaryKey, Property } from '@mikro-orm/core';

import { QuoteItem } from '../../quotes/entities/quote-item.entity';
import { ContractItemDataField } from '../contract-item.schema';
import { Contract } from './contract.entity';

@Entity({
    tableName: 'contract_items',
})
@Index({ name: 'idx_contract_items_c_id', properties: 'contractId' })
@Index({ name: 'idx_contract_items_s_no', properties: 'serialNo' })
@Index({ name: 'idx_contract_items_p_sku', properties: 'productSku' })
export class ContractItem {
    @PrimaryKey({ type: 'text' })
    id: string;

    @Property({ type: 'integer', nullable: true })
    itemNo?: number;

    @Property({ type: 'text', nullable: true })
    contractId?: string;

    @Property({ type: 'text', nullable: true })
    vendorId?: string;

    @Property({ type: 'text', nullable: true })
    serialNo?: string;

    @Property({ type: 'text', nullable: true })
    productSku?: string;

    @Property({ type: 'text', nullable: true })
    serviceLevelSku?: string;

    @Property({ type: 'text', nullable: true })
    serviceGroupSku?: string;

    @Property({ type: 'text', nullable: true })
    serviceGroupLabel?: string;

    @Property({ type: 'numeric', precision: 20, scale: 4, nullable: true, default: null })
    resellerPrice?: number;

    @Property({ type: 'numeric', precision: 20, scale: 4, nullable: true, default: null })
    endCustomerPrice?: number;

    @Property({ type: 'numeric', nullable: true, default: null })
    quantity?: number;

    @Property({ type: 'object', nullable: true })
    data?: ContractItemDataField;

    @ManyToOne(() => Contract, { nullable: true, persist: false })
    contract?: Contract;

    @OneToMany(() => QuoteItem, (quoteItem) => quoteItem.sourceContractItem, { nullable: true, persist: false })
    sourceQuoteItems?: QuoteItem[];

    @OneToMany(() => QuoteItem, (quoteItem) => quoteItem.resultingContractItem, { nullable: true, persist: false })
    resultingQuoteItems?: QuoteItem[];
}
