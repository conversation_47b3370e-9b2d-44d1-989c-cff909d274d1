import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { DateTime } from 'luxon';

import { Context } from '../../common/interfaces';
import { ContractsService } from '../contracts.service';
import { ContractJobStatusEnum } from '../enums';
import { ContractJobDrizzleRepository } from '../repositories/contract-job.drizzle.repository';

@Injectable()
export class HandleContractJobsJob {
    private readonly logger = new Logger(HandleContractJobsJob.name);

    constructor(
        private readonly contractJobRepository: ContractJobDrizzleRepository,
        private readonly contractService: ContractsService,
    ) {}

    @Cron(CronExpression.EVERY_MINUTE)
    async handleCancelExpiredPayments() {
        this.logger.debug('HandleContractJobsJob started');
        const activeJobs = await this.contractJobRepository.findActiveJobs();
        this.logger.debug(`Active jobs: ${activeJobs.length}`);
        const jobsToProcess = activeJobs.filter((job) => {
            const expiresAt = DateTime.fromJSDate(new Date(job.expiresAt));
            const now = DateTime.now();
            const isExpired = expiresAt < now;
            return isExpired;
        });
        this.logger.debug(`Jobs to process: ${jobsToProcess.length}`);

        for (const job of jobsToProcess) {
            const id = job.id;
            try {
                this.logger.debug(`Processing job: ${id}`);
                const contracts = job.data.contracts;

                await this.contractService.sendContractUpdatedNotifications({} as Context, {
                    contracts,
                });

                await this.contractJobRepository.updateContractJob({
                    id,
                    status: ContractJobStatusEnum.COMPLETED,
                });
            } catch (error) {
                this.logger.error(`Error processing job: ${id}`, error);
            }
        }
    }
}
