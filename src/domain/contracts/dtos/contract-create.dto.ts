import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { ContractSchema } from '../schemas';

const ContractCreateSchema = extendApi(
    z.object({
        contractNumber: z.string().describe('Contract Number'),
        groupId: ContractSchema.shape.groupId,
        sar: ContractSchema.shape.sar.optional(),
        startDate: z.string().datetime({ offset: true }).nullable().optional().describe('Start Date'),
        endDate: z.string().datetime({ offset: true }).describe('End Date'),
        vendorId: z.string().describe('Vendor ID'),
        distributorId: z.string().describe('Distributor ID'),
        resellerId: z.string().describe('Reseller ID'),
        endUserId: z.string().describe('End User ID'),
    }),
    {
        title: 'ContractCreateSchema',
        description: 'ContractCreateSchema model',
    },
);

export class ContractCreateDto extends createZodDto(ContractCreateSchema) {}
