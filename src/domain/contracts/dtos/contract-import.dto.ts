import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

export const ImportedContractItemSchema = extendApi(
    z.object({
        serialNumber: z.string().nullable().describe('Serial number'),
        name: z.string().nullable().describe('Name'),
        productSku: z.string().nullable().describe('Product SKU'),
        serviceGroupSku: z.string().nullable().describe('Service group SKU'),
        // serviceLevelName: z.string().nullable().describe('Service level name'),
    }),
);
export const ContractImportSchema = extendApi(
    z.object({
        assets: z
            .preprocess((val) => {
                if (typeof val === 'string') {
                    try {
                        return JSON.parse(val);
                    } catch {
                        return undefined;
                    }
                }
                return val;
            }, z.array(ImportedContractItemSchema).nullable())
            .describe('Array of contract items'),
    }),
);

export class ContractImportDto extends createZodDto(ContractImportSchema) {}

export class ContractImportCreateDto extends createZodDto(ContractImportSchema) {}
export class ImportedContractItemDto extends createZodDto(ImportedContractItemSchema) {}
