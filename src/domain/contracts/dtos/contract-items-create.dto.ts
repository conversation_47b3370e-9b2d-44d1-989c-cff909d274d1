import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

const ContractItemsCreateSchema = extendApi(
    z.object({
        contractId: z.string().describe('Contract ID'),
        sar: z.string().nullable().optional().describe('SAR Number'),
        said: z.string().nullable().optional().describe('SAID'),
        groupId: z.string().nullable().optional().describe('Group ID'),
        vendorId: z.string().describe('Vendor ID'),
        startDate: z.string().datetime({ offset: true }).optional().nullable().describe('Start Date'),
        endDate: z.string().datetime({ offset: true }).describe('End Date'),
        assets: z.array(
            z.object({
                itemNo: z.number().optional().describe('Item number'),
                serialNumber: z.string().describe('Serial Number'),
                productSku: z.string().describe('Product SKU'),
                serviceGroupSku: z.string().describe('Service Group SKU'),
            }),
        ),
    }),
    {
        title: 'ContractItemsCreateSchema',
        description: 'ContractItemsCreateSchema model',
    },
);

export class ContractItemsCreateDto extends createZodDto(ContractItemsCreateSchema) {}
