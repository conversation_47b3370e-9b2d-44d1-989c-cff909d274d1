import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

const ContractJobCreateSchema = extendApi(
    z.object({
        groupId: z.string().describe('Group ID'),
        contract: z.any().describe('Contract'),
    }),
    {
        title: 'ContractJobCreateSchema',
        description: 'ContractJobCreateSchema model',
    },
);

export class ContractJobCreateDto extends createZodDto(ContractJobCreateSchema) {}
