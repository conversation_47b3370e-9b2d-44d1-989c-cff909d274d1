import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { ContractFilterSchema } from '../schemas';

export const ContractGlobalSearchFilterSchema = extendApi(
    ContractFilterSchema.extend({
        globalSearchPhrase: z.string().optional().describe('Search by global search phrase'),
    }),
    {
        title: 'ContractGlobalSearchFilter',
        description: 'Contract global search filter parameters',
    },
);

export class ContractGlobalSearchFilterDto extends createZodDto(ContractGlobalSearchFilterSchema) {}
