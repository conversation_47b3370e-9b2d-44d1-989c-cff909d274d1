import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { ContractItemSchema } from '../schemas';

export const ContractAssetParamSchema = extendApi(
    z.object({
        contractId: ContractItemSchema.shape.contractId,
        assetId: ContractItemSchema.shape.id,
    }),
    {
        title: 'ContractAssetParamSchema',
        description: 'ContractAssetParamSchema model',
    },
);

export class ContractAssetParamDto extends createZodDto(ContractAssetParamSchema) {}
