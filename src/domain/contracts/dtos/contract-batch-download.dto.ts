import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

export const ContractBatchDownloadSchema = extendApi(
    z.object({
        contractNumbers: extendApi(
            z
                .union([z.string(), z.array(z.string())])
                .transform((val) => {
                    if (Array.isArray(val)) return val;
                    if (typeof val === 'string' && val.includes(',')) return val.split(',').map((s) => s.trim());
                    return [val];
                })
                .refine((arr) => arr.length > 0, { message: 'At least one contract number is required' }),
            {
                description:
                    'Contract number(s) to download as a single PDF. Can be a single string or array of strings.',
                example: ['1234567890', '9876543210'],
            },
        ),
        contractPrintName: extendApi(z.string().optional(), {
            description: 'Optional custom name for the printed contract',
            example: 'Q4 2024 Contracts Bundle',
        }),
    }),
    {
        title: 'Contract Batch Download Request',
        description: 'Request query parameters for downloading multiple contracts as a single PDF',
    },
);

export class ContractBatchDownloadDto extends createZodDto(ContractBatchDownloadSchema) {}
