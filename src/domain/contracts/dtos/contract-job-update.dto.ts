import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

const ContractJobUpdateSchema = extendApi(
    z.object({
        id: z.string().uuid('4').describe('ID'),
        status: z.string().describe('Status'),
        contract: z.any(),
    }),
    {
        title: 'ContractJobUpdateSchema',
        description: 'ContractJobUpdateSchema model',
    },
);

export class ContractJobUpdateDto extends createZodDto(ContractJobUpdateSchema) {}
