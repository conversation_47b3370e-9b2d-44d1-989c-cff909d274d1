import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

const ContractJobSchema = extendApi(
    z.object({
        id: z.string().uuid('4').describe('ID'),
        groupId: z.string().describe('Group ID'),
        status: z.string().describe('Status'),
        expiresAt: z.string().datetime({ offset: true }).describe('Expires At'),
        data: z
            .object({
                contracts: z.array(z.any()).describe('Contracts'),
            })
            .describe('Data'),
    }),
    {
        title: 'ContractJobSchema',
        description: 'ContractJobSchema model',
    },
);

export class ContractJobDto extends createZodDto(ContractJobSchema) {}
