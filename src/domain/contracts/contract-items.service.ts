import { InjectRepository } from '@mikro-orm/nestjs';
import { Inject, Injectable } from '@nestjs/common';

import { AssetEnrichment } from '../assets/entities/asset-enrichment.entity';
import { NoAssetsFoundForExportException } from '../assets/exceptions/no-assets-found-for-export.exception';
import { AssetEnrichmentRepository } from '../assets/repositories/asset-enrichment.repository';
import { AssetGlobalSearchListDto } from '../common/dtos/asset-global-search-list.dto';
import { AssetListModel } from '../common/models/asset-list.model';
import {
    PaginatedResponseModel,
    PaginationMetaModel,
    PaginationParamsModel,
} from '../common/pagination/pagination.model';
import { ExportFormat, ExportService } from '../common/services';
import { AssetType } from '../common/types';
import { ceilTwoDecimals } from '../files/services';
import { ProductDrizzleRepository } from '../products/repositories/product.drizzle.repository';
import { ContractItemsCreateDto } from './dtos/contract-items-create.dto';
import { ContractUiStatusEnum } from './enums/contract-ui-status.enum';
import { ContractNotFoundByIdException } from './exceptions/contract-not-found-by-id.exception';
import { ContractItemFilterModel } from './models/contract-item-filter.model';
import { ContractItemPresenter } from './presenters/contract-item.presenter';
import { ContractDrizzleRepository } from './repositories/contract.drizzle.repository';
import { ContractItemDrizzleRepository } from './repositories/contract-item.drizzle.repository';
import { ContractItemViewDrizzleRepository } from './repositories/contract-item-view.drizzle.repository';

@Injectable()
export class ContractItemsService {
    constructor(
        private readonly contractItemPresenter: ContractItemPresenter,
        private readonly exportService: ExportService,

        @InjectRepository(AssetEnrichment)
        private readonly assetEnrichmentRepository: AssetEnrichmentRepository,

        @Inject(ContractDrizzleRepository)
        private readonly contractDrizzleRepository: ContractDrizzleRepository,

        @Inject(ContractItemViewDrizzleRepository)
        private readonly contractItemViewDrizzleRepository: ContractItemViewDrizzleRepository,

        @Inject(ContractItemDrizzleRepository)
        private readonly contractItemDrizzleRepository: ContractItemDrizzleRepository,

        @Inject(ProductDrizzleRepository)
        private readonly productDrizzleRepository: ProductDrizzleRepository,
    ) {}

    async getAssetsByContractId(
        resellerIds: string[],
        endUserIds: string[] | null,
        contractId: string,
        paginationParams?: PaginationParamsModel,
        filter?: ContractItemFilterModel,
    ): Promise<AssetListModel> {
        const contract = await this.contractDrizzleRepository.findContractById(resellerIds, endUserIds, contractId);

        if (!contract) {
            throw new ContractNotFoundByIdException(contractId);
        }

        const result = await this.contractItemViewDrizzleRepository.findAssetsByContractId(
            resellerIds,
            endUserIds,
            contractId,
            paginationParams,
            filter,
        );

        let assets: AssetType[];
        let meta: PaginationMetaModel | undefined;

        if (paginationParams && result && 'data' in result && 'meta' in result) {
            assets = (result as PaginatedResponseModel<AssetType>).data;
            meta = (result as PaginatedResponseModel<AssetType>).meta;
        } else if (!paginationParams && Array.isArray(result)) {
            assets = result as AssetType[];
            meta = undefined;
        } else if (!paginationParams && result && 'data' in result && 'meta' in result) {
            assets = (result as PaginatedResponseModel<AssetType>).data;
            meta = (result as PaginatedResponseModel<AssetType>).meta;
        } else {
            console.error('Unexpected result type from findAssetsByContractId', {
                contractId,
                paginationParams,
                filter,
                resultType: typeof result,
            });
            assets = [];
            meta = undefined;
        }

        let coverageStatusMap = new Map<string, string | null>();

        if (contract.uiStatus === ContractUiStatusEnum.ACTIVE && assets.length > 0) {
            coverageStatusMap = await this.getBulkCoverageStatus(assets, contract.endDate);
        }

        const items = assets.map((entity) => {
            entity.coverage_status = coverageStatusMap.get(entity.serial_number) ?? null;
            return this.contractItemPresenter.toAssetModel(entity);
        });

        const model = new AssetListModel();
        model.setData(items);
        if (meta) {
            model.setMeta(meta);
        }

        return model;
    }

    async findAssetsByResellerIdForGlobalSearch(
        resellerId: string,
        serialNumber: string,
    ): Promise<AssetGlobalSearchListDto> {
        const assets = await this.contractItemViewDrizzleRepository.findAssetsByResellerIdForGlobalSearch(
            resellerId,
            serialNumber,
        );

        const model = new AssetGlobalSearchListDto();
        model.setData(assets);

        // Simulate meta
        model.setMeta({
            hasNextPage: false,
            nextCursor: null,
            totalCount: assets.length,
        });

        return model;
    }

    async globalSearch(resellerIds: string[], serialNumber?: string) {
        return this.contractItemViewDrizzleRepository.globalSearch(resellerIds, serialNumber);
    }

    async exportAssetsByContractId(
        resellerIds: string[],
        endUserIds: string[] | null,
        contractId: string,
        format: ExportFormat,
        filter?: ContractItemFilterModel,
    ): Promise<{ data: Buffer; filename: string }> {
        const limit = 1000;
        const exportItems = [];
        let cursor: string | undefined = undefined;

        while (true) {
            const assetList = await this.getAssetsByContractId(
                resellerIds,
                endUserIds,
                contractId,
                {
                    limit,
                    cursor,
                },
                filter,
            );

            const entries = assetList.getData();

            exportItems.push(...entries);

            const meta = assetList.getMeta();
            if (!meta.hasNextPage) {
                break;
            }

            cursor = meta.nextCursor;
        }

        if (!exportItems.length) {
            throw new NoAssetsFoundForExportException();
        }

        const exportData = exportItems.map((asset) => ({
            Name: asset.name || '',
            SerialNumber: asset.serialNumber || '',
            CoverageStatus: asset.coverageStatus || 'UNKNOWN',
            ServiceGroupLabel: asset.serviceGroupLabel || '',
            ServiceGroupSku: asset.serviceGroupSku || '',
            ProductSku: asset.productSku || '',
            Quantity: asset.quantity || 0,
            ResellerPriceFinalSum: asset.resellerPriceFinalSum ? ceilTwoDecimals(asset.resellerPriceFinalSum) : 0,
            EndCustomerPriceFinalSum: asset.endCustomerPriceFinalSum
                ? ceilTwoDecimals(asset.endCustomerPriceFinalSum)
                : 0,
            Currency: asset.currency || 'CHF',
        }));

        return this.exportService.exportData(exportData, format, `assets-${contractId}-${new Date().toISOString()}`);
    }

    private async getBulkCoverageStatus(
        assets: AssetType[],
        contractEndDate: Date | null,
    ): Promise<Map<string, string>> {
        const identifiers = assets
            .map((asset) => ({
                serial_number: asset.serial_number,
                product_sku: asset.product_sku,
            }))
            .filter((id) => id.serial_number && id.product_sku);

        if (identifiers.length === 0) {
            return new Map();
        }

        const latestCoverageDates = await this.assetEnrichmentRepository.findLatestCoverageDates(identifiers);
        const coverageStatusMap = new Map<string, string>();
        const now = new Date();

        for (const asset of assets) {
            if (!asset.serial_number) continue;

            const latestDateStr = latestCoverageDates.get(asset.serial_number);
            let comparisonDate: Date | null = null;

            if (latestDateStr) {
                comparisonDate = new Date(latestDateStr);
            } else if (contractEndDate) {
                comparisonDate = contractEndDate;
            }

            let status = 'UNKNOWN';
            if (comparisonDate) {
                if (comparisonDate >= now) {
                    status = 'COVERED';
                } else {
                    status = 'EXPIRED';
                }
            }
            coverageStatusMap.set(asset.serial_number, status);
        }

        return coverageStatusMap;
    }

    public async createContractItems(contractId: string, contractItemsData: ContractItemsCreateDto): Promise<void> {
        const sgSkus = contractItemsData.assets.map((item) => item.serviceGroupSku);
        const sgDescriptions = await this.productDrizzleRepository.findDescriptionsBySkus(sgSkus);

        for (const itemData of contractItemsData.assets) {
            const productId = await this.productDrizzleRepository.findIdByVendorAndSku(
                contractItemsData.vendorId,
                itemData.productSku,
            );
            if (!productId) {
                throw new Error(`Product not found for SKU: ${itemData.productSku}`);
            }

            const serviceGroupDescription = sgDescriptions[itemData.serviceGroupSku];
            if (!serviceGroupDescription) {
                throw new Error(`Service group not found for SKU: ${itemData.serviceGroupSku}`);
            }

            const startDate = contractItemsData.startDate ? new Date(contractItemsData.startDate) : null;
            const endDate = new Date(contractItemsData.endDate);

            const id = await this.contractItemDrizzleRepository.createContractItem(
                contractId,
                contractItemsData.sar,
                contractItemsData.said,
                contractItemsData.groupId,
                contractItemsData.vendorId,
                startDate,
                endDate,
                itemData.itemNo ?? null,
                itemData.serialNumber,
                itemData.productSku,
                productId,
                itemData.serviceGroupSku,
                serviceGroupDescription,
            );
        }
    }
}
