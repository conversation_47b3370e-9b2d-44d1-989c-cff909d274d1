import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { ContractSchema } from '../schemas';

const ContractIdParamSchema = extendApi(
    z.object({
        contractId: ContractSchema.shape.id,
    }),
    {
        title: 'ContractIdParam',
        description: 'ContractIdParam model',
    },
);

export class ContractIdParamModel extends createZodDto(ContractIdParamSchema) {}
