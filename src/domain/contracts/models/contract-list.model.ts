import { createZodDto } from '@anatine/zod-nestjs';

import { PaginationMetaModel } from '../../common/pagination/pagination.model';
import { ContractListSchema } from '../schemas/contract-list.schema';
import { ContractListItemModel } from './contract-list-item.model';

export class ContractListModel extends createZodDto(ContractListSchema) {
    zodSchema = ContractListSchema;

    data: ContractListItemModel[] = [];
    meta: PaginationMetaModel = new PaginationMetaModel();

    public setData(data: ContractListItemModel[], meta: PaginationMetaModel = new PaginationMetaModel()) {
        this.data = data;
        this.meta = meta;
    }

    public getData() {
        return this.zodSchema.parse({
            data: this.data,
            meta: this.meta,
        });
    }
}
