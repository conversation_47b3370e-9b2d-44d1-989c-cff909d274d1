import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { addMetaProperties } from '../../../helpers/zod.helper';

const maxFileSize = 20 * 1024 * 1024; // 20MB
const allowedFileTypes = {
    'application/pdf': ['.pdf'],
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
    'application/vnd.ms-excel': ['.xls'],
    'text/csv': ['.csv'],
    'application/msword': ['.doc'],
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
};

const BaseContractRequestQuoteSchema = z
    .object({
        customerId: z.string().nullable().describe('FIELDS.CUSTOMER_ID'),
        groupId: z.string().nullable().optional().describe('FIELDS.GROUP_ID'),
        contractNo: z.string().nullable().describe('FIELDS.CONTRACT_NUMBER'),
        requestForContractGroupId: z.string().nullable().describe('FIELDS.REQUEST_FOR_CONTRACT_GROUP_ID'),
        generalRequest: addMetaProperties(z.string().nullable().optional().describe('FIELDS.GENERAL_REQUEST'), {
            'ui:widget': 'text-editor',
        }),
        files: addMetaProperties(
            z
                .array(
                    extendApi(
                        z
                            .custom<Express.Multer.File>(
                                (file) => {
                                    // Check if file exists and is an object
                                    if (!file || typeof file !== 'object') return false;

                                    // Check if file has mimetype property
                                    const fileObj = file as any;
                                    return fileObj.mimetype && Object.keys(allowedFileTypes).includes(fileObj.mimetype);
                                },
                                {
                                    path: ['attachPo'],
                                    message: 'FIELDS.INVALID_FILE_TYPE',
                                },
                            )
                            .describe('FIELDS.FILE'),
                        {
                            type: 'string',
                            format: 'file',
                        },
                    ),
                )
                .optional()
                .nullable()
                .describe('FIELDS.FILES'),
            {
                'ui:options': {
                    multipleFiles: true,
                    acceptedFormats: allowedFileTypes,
                    maxSize: maxFileSize,
                },
            },
        ),
    })
    .describe('Request Quote from Contract');

export class ContractRequestQuoteModel extends createZodDto(BaseContractRequestQuoteSchema) {}

export class ContractRequestQuoteFilesModel extends createZodDto(
    z
        .object({
            files: BaseContractRequestQuoteSchema.shape.files,
        })
        .optional()
        .nullable(),
) {}
