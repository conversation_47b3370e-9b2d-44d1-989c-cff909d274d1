import { Inject, Injectable } from '@nestjs/common';
import { and, asc, desc, eq, gt, ilike, inArray, isNotNull, or, sql, SQLWrapper } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

import * as schema from '../../../../drizzle/schema';
import { DRIZZLE_PROVIDER } from '../../../drizzle/drizzle.provider';
import {
    PaginatedResponseModel,
    PaginationMetaModel,
    PaginationParamsModel,
} from '../../common/pagination/pagination.model';
import { BaseDrizzleRepository } from '../../common/repositories/base.drizzle.repository';
import { AssetType } from '../../common/types';
import { ContractItem } from '../entities/contract-item.entity';
import { ContractItemFilterModel } from '../models/contract-item-filter.model';

@Injectable()
export class ContractItemViewDrizzleRepository extends BaseDrizzleRepository<ContractItem> {
    constructor(
        @Inject(DRIZZLE_PROVIDER)
        db: NodePgDatabase<typeof schema>,
    ) {
        super(db, schema.vContractItems);
    }

    async findAssetsByContractId(
        resellerIds: string[],
        endUserIds: string[] | null,
        contractId: string,
        paginationParams: PaginationParamsModel,
        filter?: ContractItemFilterModel,
    ): Promise<PaginatedResponseModel<AssetType>> {
        const sortBy = this.buildSortBy(filter?.sortBy);
        const sortOrder = filter?.sortOrder || 'asc';

        const whereConditions: SQLWrapper[] = [
            inArray(schema.vContractItems.resellerId, resellerIds),
            ...(endUserIds !== null ? [inArray(schema.vContractItems.endUserId, endUserIds)] : []),
            eq(schema.vContractItems.contractId, contractId),
        ];

        if (filter?.search) {
            whereConditions.push(...this.buildSearchConditions(filter.search));
        }

        if (filter?.hasEndCustomerPrice !== undefined) {
            whereConditions.push(
                and(isNotNull(schema.vContractItems.endCustomerPrice), gt(schema.vContractItems.endCustomerPrice, '0')),
            );
        }

        if (filter.hasEndOfServiceLifeDate === true) {
            whereConditions.push(isNotNull(schema.vContractItems.supportLifeEndDate));
        }

        const DEFAULT_PAGINATION_LIMIT = parseInt(process.env.PAGINATION_DEFAULT_LIMIT, 10) || 50;
        const MAX_PAGINATION_LIMIT = parseInt(process.env.PAGINATION_MAX_LIMIT, 10) || 100;

        const limit = paginationParams?.limit || DEFAULT_PAGINATION_LIMIT;
        const effectiveLimit = Math.min(limit, MAX_PAGINATION_LIMIT);
        const queryLimit = effectiveLimit + 1;

        let offset = 0;
        if (paginationParams?.cursor) {
            offset = parseInt(paginationParams.cursor, 10);
        }

        const query = this.db
            .select({
                name: schema.vContractItems.productName,
                serial_number: schema.vContractItems.serialNo,
                coverage_status: schema.vContractItems.coverageStatus,
                service_group_sku: schema.vContractItems.serviceGroupSku,
                service_group_label: schema.vContractItems.serviceGroupLabel,
                product_sku: schema.vContractItems.productSku,
                currency: schema.vContractItems.currency,
                quantity: schema.vContractItems.quantity,
                items_count: sql<number>`count(*)`,
                reseller_price_final_sum: sql<number>`sum(${schema.vContractItems.resellerPriceFinal})::float`,
                end_customer_price_final_sum: sql<number>`sum(${schema.vContractItems.endCustomerPriceFinal})::float`,
                items: sql<any[]>`JSON_AGG(to_json(${schema.vContractItems}))`,
                start_date: schema.vContractItems.startDate,
                end_date: schema.vContractItems.endDate,
                support_life_end_date: schema.vContractItems.supportLifeEndDate,
            })
            .from(schema.vContractItems)
            .where(and(...whereConditions))
            .groupBy(
                schema.vContractItems.productName,
                schema.vContractItems.serialNo,
                schema.vContractItems.productSku,
                schema.vContractItems.serviceGroupSku,
                schema.vContractItems.serviceGroupLabel,
                schema.vContractItems.coverageStatus,
                schema.vContractItems.currency,
                schema.vContractItems.quantity,
                schema.vContractItems.startDate,
                schema.vContractItems.endDate,
                schema.vContractItems.supportLifeEndDate,
            )
            .limit(queryLimit)
            .offset(offset);

        if (sortOrder.toLowerCase() === 'desc') {
            switch (sortBy) {
                case 'name':
                    query.orderBy(desc(schema.vContractItems.productName));
                    break;
                case 'serial_number':
                    query.orderBy(desc(schema.vContractItems.serialNo));
                    break;
                case 'product_sku':
                    query.orderBy(desc(schema.vContractItems.productSku));
                    break;
                case 'service_group_sku':
                    query.orderBy(desc(schema.vContractItems.serviceGroupSku));
                    break;
                case 'reseller_price_final_sum':
                case 'resellerPriceFinalSum':
                    query.orderBy(desc(sql`sum(${schema.vContractItems.resellerPriceFinal})::float`));
                    break;
                case 'end_customer_price_final_sum':
                case 'endCustomerPriceFinalSum':
                    query.orderBy(desc(sql`sum(${schema.vContractItems.endCustomerPriceFinal})::float`));
                    break;
                case 'quantity':
                    query.orderBy(desc(schema.vContractItems.quantity));
                    break;
                case 'startDate':
                    query.orderBy(desc(schema.vContractItems.startDate));
                    break;
                case 'endDate':
                    query.orderBy(desc(schema.vContractItems.endDate));
                    break;
                case 'supportLifeEndDate':
                    query.orderBy(desc(schema.vContractItems.supportLifeEndDate));
                    break;
                default:
                    // For custom columns or computed values, use SQL expressions
                    query.orderBy(desc(sql`${sortBy}`));
                    break;
            }
        } else {
            switch (sortBy) {
                case 'name':
                    query.orderBy(asc(schema.vContractItems.productName));
                    break;
                case 'serial_number':
                    query.orderBy(asc(schema.vContractItems.serialNo));
                    break;
                case 'product_sku':
                    query.orderBy(asc(schema.vContractItems.productSku));
                    break;
                case 'service_group_sku':
                    query.orderBy(asc(schema.vContractItems.serviceGroupSku));
                    break;
                case 'reseller_price_final_sum':
                case 'resellerPriceFinalSum':
                    query.orderBy(asc(sql`sum(${schema.vContractItems.resellerPriceFinal})::float`));
                    break;
                case 'end_customer_price_final_sum':
                case 'endCustomerPriceFinalSum':
                    query.orderBy(asc(sql`sum(${schema.vContractItems.endCustomerPriceFinal})::float`));
                    break;
                case 'quantity':
                    query.orderBy(asc(schema.vContractItems.quantity));
                    break;
                case 'startDate':
                    query.orderBy(asc(schema.vContractItems.startDate));
                    break;
                case 'endDate':
                    query.orderBy(asc(schema.vContractItems.endDate));
                    break;
                case 'supportLifeEndDate':
                    query.orderBy(asc(schema.vContractItems.supportLifeEndDate));
                    break;
                default:
                    // For custom columns or computed values, use SQL expressions
                    query.orderBy(asc(sql`${sortBy}`));
                    break;
            }
        }

        // Execute query
        const assets = await query;

        // Handle pagination result
        const hasNextPage = assets.length > effectiveLimit;
        const data = hasNextPage ? assets.slice(0, effectiveLimit) : assets;

        const nextCursor = hasNextPage
            ? paginationParams?.cursor
                ? (parseInt(paginationParams.cursor, 10) + effectiveLimit).toString()
                : effectiveLimit.toString()
            : null;

        const items = data.map((record) => this.mapToEntity(record));

        return new PaginatedResponseModel<AssetType>(items, new PaginationMetaModel(hasNextPage, nextCursor));
    }

    async globalSearch(resellerIds: string[] | null, serialNumber?: string) {
        const whereConditions: SQLWrapper[] = [
            ...(serialNumber ? [eq(sql`UPPER(${schema.vContractItems.serialNo})`, serialNumber)] : []),
            inArray(schema.vContractItems.resellerId, resellerIds),
        ];

        const query = this.db
            .select({
                covered: sql`${schema.vContractItems.endDate}::date >= now()`,
                serialNumber: schema.vContractItems.serialNo,
                endDate: schema.vContractItems.endDate,
                productSku: schema.vContractItems.productSku,
                endUser: {
                    id: schema.entities.id,
                    name: schema.entities.name,
                },
                product: {
                    description: schema.products.description,
                },
            })
            .from(schema.vContractItems)
            .leftJoin(schema.entities, eq(schema.vContractItems.endUserId, schema.entities.id))
            .leftJoin(
                schema.products,
                and(
                    eq(schema.vContractItems.vendorId, schema.products.vendorId),
                    eq(schema.vContractItems.productSku, schema.products.sku),
                ),
            )
            .where(and(...whereConditions))
            .orderBy(desc(schema.vContractItems.endDate))
            .limit(1);

        const items = await query;

        return items;
    }

    async findAssetsByResellerIdForGlobalSearch(resellerId: string, serialNumber: string): Promise<any[]> {
        const whereConditions: SQLWrapper[] = [
            eq(schema.vContractItems.resellerId, resellerId),
            eq(schema.vContractItems.serialNo, serialNumber),
        ];

        const query = this.db
            .select({
                isCovered: sql`${schema.vContractItems.endDate}::date >= now()`,
                serialNumber: schema.vContractItems.serialNo,
                endDate: schema.vContractItems.endDate,
                productSku: schema.vContractItems.productSku,
                endUser: {
                    id: schema.entities.id,
                    name: schema.entities.name,
                },
                product: {
                    description: schema.products.description,
                },
            })
            .from(schema.vContractItems)
            .where(and(...whereConditions))
            .leftJoin(schema.entities, eq(schema.vContractItems.endUserId, schema.entities.id))
            .leftJoin(
                schema.products,
                and(
                    eq(schema.vContractItems.vendorId, schema.products.vendorId),
                    eq(schema.vContractItems.productSku, schema.products.sku),
                ),
            )
            .orderBy(desc(schema.vContractItems.endDate))
            .limit(1);

        // Execute query
        return await query;
    }

    protected mapToEntity(record: any): AssetType {
        return {
            ...record,
            quantity: record.quantity ? Number(record.quantity) : null,
            items_count: record.items_count ? Number(record.items_count) : null,
            reseller_price_final_sum: record.reseller_price_final_sum ? Number(record.reseller_price_final_sum) : null,
            end_customer_price_final_sum: record.end_customer_price_final_sum
                ? Number(record.end_customer_price_final_sum)
                : null,
        } as AssetType;
    }

    protected buildSortBy(sortBy: ContractItemFilterModel['sortBy']): string {
        if (!sortBy) {
            return 'name';
        }

        switch (sortBy) {
            case 'serialNo':
                return 'serial_number';
            case 'productName':
                return 'name';
            case 'endCustomerPrice':
                return 'end_customer_price_final_sum';
            case 'productSku':
                return 'product_sku';
            case 'resellerPrice':
                return 'reseller_price_final_sum';
            case 'serviceGroupSku':
                return 'service_group_sku';
            default:
                return sortBy;
        }
    }

    protected buildSearchConditions(searchTerm: string): SQLWrapper[] {
        if (!searchTerm) return [];

        return [
            or(
                ilike(schema.vContractItems.productName, `%${searchTerm}%`),
                ilike(schema.vContractItems.serialNo, `%${searchTerm}%`),
                ilike(schema.vContractItems.productSku, `%${searchTerm}%`),
            ),
        ];
    }
}
