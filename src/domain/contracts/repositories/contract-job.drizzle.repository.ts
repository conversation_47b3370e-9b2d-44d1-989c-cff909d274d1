import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { and, eq, InferSelectModel, SQLWrapper } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { DateTime } from 'luxon';
import { v4 } from 'uuid';

import * as schema from '../../../../drizzle/schema';
import { DRIZZLE_PROVIDER } from '../../../drizzle/drizzle.provider';
import { BaseDrizzleRepository } from '../../common/repositories/base.drizzle.repository';
import { ContractJobCreateDto, ContractJobUpdateDto } from '../dtos';
import { ContractJobStatusEnum } from '../enums';

export type ContractJobDrizzle = Partial<InferSelectModel<typeof schema.contractJobs>>;

@Injectable()
export class ContractJobDrizzleRepository extends BaseDrizzleRepository<any> {
    constructor(
        @Inject(DRIZZLE_PROVIDER)
        db: NodePgDatabase<typeof schema>,
    ) {
        super(db, schema.contractJobs);
    }

    async findActiveJobs(): Promise<ContractJobDrizzle[]> {
        const query = this.db
            .select()
            .from(schema.contractJobs)
            .where(eq(schema.contractJobs.status, ContractJobStatusEnum.PROCESSING));

        return await query;
    }

    async findJobById(id: string): Promise<ContractJobDrizzle | null> {
        const query = this.db.select().from(schema.contractJobs).where(eq(schema.contractJobs.id, id)).limit(1);

        const data = await query;

        return data.length > 0 ? data[0] : null;
    }

    async findJobByGroupId(groupId: string): Promise<ContractJobDrizzle | null> {
        const where: SQLWrapper[] = [
            eq(schema.contractJobs.groupId, groupId),
            eq(schema.contractJobs.status, ContractJobStatusEnum.PROCESSING),
        ];

        const query = this.db
            .select()
            .from(schema.contractJobs)
            .where(and(...where))
            .limit(1);

        const data = await query;

        return data.length > 0 ? data[0] : null;
    }

    async createContractJob(data: ContractJobCreateDto): Promise<ContractJobDrizzle> {
        const contractJob = await this.db
            .insert(schema.contractJobs)
            .values({
                id: v4(),
                groupId: data.groupId,
                status: ContractJobStatusEnum.PROCESSING,
                expiresAt: DateTime.now().plus({ minutes: 15 }).toISO(),
                data: {
                    contracts: [data.contract],
                },
            })
            .returning();

        return contractJob[0];
    }

    async updateContractJob(data: ContractJobUpdateDto): Promise<ContractJobDrizzle> {
        const job = await this.findJobById(data.id);

        if (!job) {
            throw new NotFoundException('Job not found');
        }

        const contracts = [...job.data.contracts, data.contract].filter(Boolean);
        let expiresAt = job.expiresAt;
        if (!data.status || data.status !== ContractJobStatusEnum.PROCESSING) {
            expiresAt = DateTime.now().plus({ minutes: 15 }).toISO();
        }

        const contractJob = await this.db
            .update(schema.contractJobs)
            .set({
                expiresAt,
                status: data.status,
                data: {
                    contracts: contracts,
                },
            })
            .where(eq(schema.contractJobs.id, data.id))
            .returning();

        return contractJob[0];
    }
}
