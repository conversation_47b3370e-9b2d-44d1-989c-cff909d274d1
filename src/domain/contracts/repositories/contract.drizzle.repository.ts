import { Inject, Injectable } from '@nestjs/common';
import { and, desc, eq, gte, ilike, inArray, lte, ne, or, SQL, sql, SQLWrapper } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { alias, PgSelect, SelectedFields } from 'drizzle-orm/pg-core';
import { v4 as uuidv4 } from 'uuid';

import * as schema from '../../../../drizzle/schema';
import { DRIZZLE_PROVIDER } from '../../../drizzle/drizzle.provider';
import { PaginatedResponseModel, PaginationParamsModel } from '../../common/pagination/pagination.model';
import { BaseDrizzleRepository } from '../../common/repositories/base.drizzle.repository';
import { AssetsContractListSelect } from '../consts/assets-contract-list.select';
import { ContractListSelect } from '../consts/contract-list.select';
import { contractListQueryModifier } from '../consts/contract-list-query.modifier';
import { ContractCreateDto } from '../dtos/contract-create.dto';
import { ContractGlobalSearchFilterDto } from '../dtos/contract-global-search-filter.dto';
import { Contract } from '../entities/contract.entity';
import { ContractUiStatusEnum } from '../enums';
import { ContractFilterModel } from '../models/contract-filter.model';

/**
 * 1. Ensure schema.contracts has the correct column types - numerics are strings now
 * 2. Update filter conditions to match the actual column types
 * 3. Handle EnumColumns properly (for status filtering)
 * 4. Ensure date handling matches your schema's date storage format
 */
@Injectable()
export class ContractDrizzleRepository extends BaseDrizzleRepository<Contract> {
    constructor(
        @Inject(DRIZZLE_PROVIDER)
        db: NodePgDatabase<typeof schema>,
    ) {
        super(db, schema.contracts);
    }

    async createContract(contractData: ContractCreateDto): Promise<string> {
        const contractId = uuidv4();

        const startDate = contractData.startDate ? new Date(contractData.startDate) : null;
        const endDate = new Date(contractData.endDate);
        const uiStatus = endDate < new Date() ? ContractUiStatusEnum.EXPIRED : ContractUiStatusEnum.ACTIVE;

        const contract = await this.db
            .insert(schema.contracts)
            .values({
                id: contractId,
                contractNo: contractData.contractNumber,
                groupId: contractData.groupId,
                sar: contractData.sar,
                startDate: startDate ? startDate.toISOString() : null,
                endDate: endDate.toISOString(),
                uiStatus,
                vendorId: contractData.vendorId,
                distributorId: contractData.distributorId,
                resellerId: contractData.resellerId,
                endUserId: contractData.endUserId,
                data: {
                    ContractId: contractId,
                    ContractNo: contractData.contractNumber,
                    GroupID: contractData.groupId,
                    SAR: contractData.sar,
                    ContractContacts: [],
                },
            })
            .returning();

        return contract[0].id;
    }

    async checkIfContractExistsByContractId(
        resellerIds: string[],
        endUserIds: string[] | null,
        contractId: string,
    ): Promise<boolean> {
        const res = await this.findContractNumberByContractId(resellerIds, endUserIds, contractId);
        return res !== null;
    }

    async findContractNumberByContractId(
        resellerIds: string[],
        endUserIds: string[] | null,
        contractId: string,
    ): Promise<string | null> {
        const result = await this.db
            .select({ contractNo: schema.contracts.contractNo })
            .from(schema.contracts)
            .where(
                and(
                    ...[
                        inArray(schema.contracts.resellerId, resellerIds),
                        ...(endUserIds !== null ? [inArray(schema.contracts.endUserId, endUserIds)] : []),
                        eq(schema.contracts.id, contractId),
                    ],
                ),
            )
            .limit(1);

        return result.length > 0 ? result[0].contractNo : null;
    }

    async findContractsByContractNumbers(
        resellerIds: string[],
        endUserIds: string[] | null,
        contractNumbers: string[],
    ): Promise<{ id: string; contractNo: string; groupId: string }[]> {
        const results = await this.db
            .select({
                id: schema.contracts.id,
                contractNo: schema.contracts.contractNo,
                groupId: schema.contracts.groupId,
            })
            .from(schema.contracts)
            .where(
                and(
                    ...[
                        inArray(schema.contracts.contractNo, contractNumbers),
                        inArray(schema.contracts.resellerId, resellerIds),
                        ...(endUserIds !== null ? [inArray(schema.contracts.endUserId, endUserIds)] : []),
                    ],
                ),
            );

        return results.filter((r) => r.contractNo !== null) as { id: string; contractNo: string; groupId: string }[];
    }

    async findContractById(resellerIds: string[], endUserIds: string[] | null, id: string): Promise<Contract | null> {
        const result = await this.db
            .select()
            .from(schema.contracts)
            .where(
                and(
                    ...[
                        inArray(schema.contracts.resellerId, resellerIds),
                        ...(endUserIds !== null ? [inArray(schema.contracts.endUserId, endUserIds)] : []),
                        eq(schema.contracts.id, id),
                    ],
                ),
            )
            .limit(1);

        return result.length > 0 ? this.mapToEntity(result[0]) : null;
    }

    async findContractByResellerIdAndId(
        resellerIds: string[],
        endUserIds: string[] | null,
        contractId: string,
        fields?: SelectedFields,
        queryModifier?: (query: PgSelect) => void,
    ): Promise<Contract | null> {
        const query = this.db
            .select(fields || undefined)
            .from(schema.contracts)
            .where(
                and(
                    ...[
                        inArray(schema.contracts.resellerId, resellerIds),
                        ...(endUserIds !== null ? [inArray(schema.contracts.endUserId, endUserIds)] : []),
                        eq(schema.contracts.id, contractId),
                    ],
                ),
            )
            .limit(1)
            .$dynamic();

        if (queryModifier) {
            queryModifier(query);
        }

        const result = await query;

        return result.length > 0 ? this.mapToEntity(result[0]) : null;
    }

    async globalSearch(resellerIds: string[], endUserIds: string[] | null, searchPhrase?: string) {
        const whereConditions: SQLWrapper[] = [
            ...(searchPhrase
                ? [
                      or(
                          eq(sql`UPPER(${schema.contracts.contractNo})`, searchPhrase),
                          eq(sql`UPPER(${schema.contracts.groupId})`, searchPhrase),
                          eq(sql`UPPER(${schema.contracts.sar})`, searchPhrase),
                      ),
                  ]
                : []),
            inArray(schema.contracts.resellerId, resellerIds),
            ...(endUserIds !== null ? [inArray(schema.contracts.endUserId, endUserIds)] : []),
            ne(schema.contracts.uiStatus, ContractUiStatusEnum.INVALID),
        ];

        const query = this.db
            .select({
                id: schema.contracts.id,
                contractNo: schema.contracts.contractNo,
                sar: schema.contracts.sar,
                groupId: schema.contracts.groupId,
                endUser: {
                    id: schema.entities.id,
                    name: schema.entities.name,
                },
                uiStatus: schema.contracts.uiStatus,
                resellerTotalPrice: schema.contracts.resellerTotalPrice,
                startDate: schema.contracts.startDate,
                endDate: schema.contracts.endDate,
                expiresIn: sql`${schema.contracts.endDate}::date - now()`,
            })
            .from(schema.contracts)
            .innerJoin(schema.entities, eq(schema.entities.id, schema.contracts.endUserId))
            .limit(5)
            .orderBy(desc(schema.contracts.endDate))
            .where(and(...whereConditions));

        const items = await query;

        return items;
    }

    async findContracts(
        resellerIds: string[],
        endUserIds: string[] | null,
        paginationParams?: PaginationParamsModel,
        filter?: ContractFilterModel | ContractGlobalSearchFilterDto,
        fields?: SelectedFields,
        queryModifier?: (query: PgSelect) => void,
    ): Promise<PaginatedResponseModel<Contract>> {
        const entityConditions = [
            inArray(schema.contracts.resellerId, resellerIds),
            ...(endUserIds !== null ? [inArray(schema.contracts.endUserId, endUserIds)] : []),
        ];
        return this.findContractsWithFilter(paginationParams, filter, entityConditions, fields, queryModifier);
    }

    async findAllEndUserIdsByResellerId(resellerIds: string[], endUserIds: string[] | null): Promise<string[]> {
        const query = this.db
            .select({
                endUserId: schema.contracts.endUserId,
            })
            .from(schema.contracts)
            .where(
                and(
                    inArray(schema.contracts.resellerId, resellerIds),
                    ...(endUserIds !== null ? [inArray(schema.contracts.endUserId, endUserIds)] : []),
                    ne(schema.contracts.uiStatus, 'Invalid'),
                ),
            )
            .groupBy(schema.contracts.endUserId);

        const result = await query;
        return result.map((record) => record.endUserId).filter((id): id is string => !!id);
    }

    async findContractsByResellerAndEndUserId(
        resellerIds: string[],
        endUserIds: string[] | null,
        endUserId: string,
        paginationParams: PaginationParamsModel,
        filter?: ContractFilterModel,
        fields?: SelectedFields,
        queryModifier?: (query: PgSelect) => void,
    ): Promise<PaginatedResponseModel<Contract>> {
        const entityConditions = [
            inArray(schema.contracts.resellerId, resellerIds),
            ...(endUserIds !== null ? [inArray(schema.contracts.endUserId, endUserIds)] : []),
            eq(schema.contracts.endUserId, endUserId),
        ];

        return this.findContractsWithFilter(paginationParams, filter, entityConditions, fields, queryModifier);
    }

    protected buildSearchConditions(searchTerm: string): SQLWrapper[] {
        if (!searchTerm) return [];

        return [
            or(
                ilike(schema.contracts.contractNo, `%${searchTerm}%`),
                ilike(schema.contracts.groupId, `%${searchTerm}%`),
                ilike(schema.contracts.sar, `%${searchTerm}%`),
                ilike(schema.entities.name, `%${searchTerm}%`),
            ),
        ];
    }

    protected buildStatusConditions(status: string | string[] | undefined): SQLWrapper[] {
        if (!status) {
            return [ne(schema.contracts.uiStatus, 'Invalid')];
        }

        const statusArray = Array.isArray(status) ? status : [status];

        if (statusArray.length === 0) {
            return [ne(schema.contracts.uiStatus, 'Invalid')];
        }

        const statusConditions = statusArray.map((s) => eq(schema.contracts.uiStatus, s as any));
        return [or(...statusConditions)];
    }

    protected buildDateConditions(endDateAfter?: string, endDateBefore?: string): SQLWrapper[] {
        const conditions: SQLWrapper[] = [];

        if (endDateAfter) {
            conditions.push(gte(schema.contracts.endDate, endDateAfter));
        }

        if (endDateBefore) {
            conditions.push(lte(schema.contracts.endDate, endDateBefore));
        }

        return conditions;
    }

    async findContractsByQuote(
        resellerIds: string[],
        endUserIds: string[] | null,
        quoteId: string,
        paginationParams: PaginationParamsModel,
        filter?: ContractFilterModel,
    ): Promise<PaginatedResponseModel<Contract>> {
        const where: SQL[] = [
            inArray(schema.contracts.resellerId, resellerIds),
            ...(endUserIds !== null ? [inArray(schema.contracts.endUserId, endUserIds)] : []),
            eq(schema.quoteItems.quoteId, quoteId),
            ...this.buildSqlFromFilter(filter),
        ];

        if (!filter) {
            filter = { sortBy: 'contractNo', sortOrder: 'asc' } as ContractFilterModel;
        } else if (!filter.sortBy) {
            filter.sortBy = 'contractNo';
            filter.sortOrder = 'asc';
        }

        if (filter.sortBy === 'endUser') {
            filter.sortBy = 'endUserName';
        }
        const vendorSchema = alias(schema.entities, 'vendor');

        const modifier = (query: PgSelect) =>
            contractListQueryModifier(query)
                .leftJoin(schema.contractItems, eq(schema.contractItems.contractId, schema.contracts.id))
                .leftJoin(
                    schema.quoteItems,
                    sql`(${schema.quoteItems.resultingContractItemId} =
                            ${schema.contractItems.id}
                            OR ${schema.quoteItems.sourceContractItemId}
                            =
                            ${schema.contractItems.id})`,
                )
                .innerJoin(
                    vendorSchema,
                    and(
                        eq(vendorSchema.id, schema.contracts.vendorId),
                        filter?.vendorNames?.length ? inArray(vendorSchema.name, filter.vendorNames) : undefined,
                    ),
                )
                .groupBy(schema.contracts.id, schema.entities.id, vendorSchema.id);

        const result = await this.findPaginated(
            where,
            paginationParams,
            filter,
            {
                ...ContractListSelect,
                vendor: {
                    id: vendorSchema.id,
                    name: vendorSchema.name,
                },
            },
            modifier,
        );

        const mappedData = result.data.map((record) => this.mapToEntity(record));

        return new PaginatedResponseModel<Contract>(mappedData, result.meta);
    }

    protected mapToEntity(record: any): Contract {
        return {
            ...record,
            startDate: record.startDate ? new Date(record.startDate) : null,
            endDate: record.endDate ? new Date(record.endDate) : null,
            createdAt: record.createdAt ? new Date(record.createdAt) : null,
            updatedAt: record.updatedAt ? new Date(record.updatedAt) : null,
            resellerTotalPrice: record.resellerTotalPrice ? Number(record.resellerTotalPrice) : null,
            endCustomerTotalPrice: record.endCustomerTotalPrice ? Number(record.endCustomerTotalPrice) : null,
        } as Contract;
    }

    async findContractsWithFilter(
        paginationParams?: PaginationParamsModel,
        filter?: ContractFilterModel | ContractGlobalSearchFilterDto,
        additionalConditions: SQLWrapper[] = [],
        fields?: SelectedFields,
        queryModifier?: (query: PgSelect) => void,
    ): Promise<PaginatedResponseModel<Contract>> {
        const whereConditions: SQLWrapper[] = [...additionalConditions];

        if (filter?.search) {
            whereConditions.push(...this.buildSearchConditions(filter.search));
        }

        // Global search conditions
        if (filter && 'globalSearchPhrase' in filter && filter.globalSearchPhrase) {
            whereConditions.push(
                or(
                    eq(schema.contracts.contractNo, filter.globalSearchPhrase),
                    eq(schema.contracts.groupId, filter.globalSearchPhrase),
                    eq(schema.contracts.sar, filter.globalSearchPhrase),
                ),
            );
        }

        whereConditions.push(...this.buildStatusConditions(filter?.status));

        if (filter?.endDateAfter || filter?.endDateBefore) {
            whereConditions.push(...this.buildDateConditions(filter.endDateAfter, filter.endDateBefore));
        }

        if (!filter) {
            filter = { sortBy: 'contractNo', sortOrder: 'asc' } as ContractFilterModel;
        } else if (!filter.sortBy) {
            filter.sortBy = 'contractNo';
            filter.sortOrder = 'asc';
        }

        if (filter.sortBy === 'endUser') {
            filter.sortBy = 'endUserName';
        }

        if (filter.sortBy === 'vendor') {
            filter.sortBy = 'vendorName';
        }

        const result = await this.findPaginated(
            whereConditions,
            paginationParams,
            filter,
            fields || ContractListSelect,
            queryModifier || contractListQueryModifier,
        );

        const mappedData = result.data.map((record) => this.mapToEntity(record));
        return new PaginatedResponseModel<Contract>(mappedData, result.meta);
    }

    async ContractsByResellerIdAndSerialNumber(
        resellerIds: string[],
        endUserIds: string[] | null,
        serialNo: string,
        paginationParams: PaginationParamsModel,
        filter?: ContractFilterModel,
    ): Promise<PaginatedResponseModel<Contract>> {
        const vendorSchema = alias(schema.entities, 'vendor');

        const whereConditions: SQLWrapper[] = [
            inArray(schema.contracts.resellerId, resellerIds),
            ...(endUserIds !== null ? [inArray(schema.contracts.endUserId, endUserIds)] : []),
            eq(schema.contractItems.serialNo, serialNo),
            ...this.buildSqlFromFilter(filter),
        ];

        const queryModifier = (query: PgSelect) => {
            query.leftJoin(schema.contractItems, eq(schema.contractItems.contractId, schema.contracts.id));
            query.innerJoin(schema.entities, eq(schema.entities.id, schema.contracts.endUserId));
            query.innerJoin(
                vendorSchema,
                and(
                    eq(vendorSchema.id, schema.contracts.vendorId),
                    filter?.vendorNames?.length ? inArray(vendorSchema.name, filter.vendorNames) : undefined,
                ),
            );
            query.groupBy(schema.contracts.id, vendorSchema.id);
        };

        const result = await this.findPaginated(
            whereConditions,
            paginationParams,
            filter,
            {
                ...AssetsContractListSelect,
                vendor: {
                    id: vendorSchema.id,
                    name: vendorSchema.name,
                },
            },
            queryModifier,
        );
        const mappedData = result.data.map((record) => this.mapToEntity(record));

        return new PaginatedResponseModel<Contract>(mappedData, result.meta);
    }

    protected buildSqlFromFilter(filter: ContractFilterModel): SQL[] {
        const result = [];

        if (!filter) {
            return result;
        }

        if (filter?.search) {
            result.push(...this.buildSearchConditions(filter.search));
        }

        if (filter?.status) {
            result.push(...this.buildStatusConditions(filter.status));
        }

        if (filter?.endDateAfter || filter?.endDateBefore) {
            result.push(...this.buildDateConditions(filter.endDateAfter, filter.endDateBefore));
        }

        return result;
    }

    async findContractsByGroupId(groupId: string) {
        const results = await this.db.select().from(schema.contracts).where(eq(schema.contracts.groupId, groupId));

        if (results.length === 0) {
            return [];
        }

        return results;
    }
}
