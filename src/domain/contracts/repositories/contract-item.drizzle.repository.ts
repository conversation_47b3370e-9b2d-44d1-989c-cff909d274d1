import { Inject, Injectable } from '@nestjs/common';
import { eq, gte, ilike, lte, ne, or, SQLWrapper } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { v4 as uuidv4 } from 'uuid';

import * as schema from '../../../../drizzle/schema';
import { DRIZZLE_PROVIDER } from '../../../drizzle/drizzle.provider';
import { PaginatedResponseModel, PaginationParamsModel } from '../../common/pagination/pagination.model';
import { BaseDrizzleRepository } from '../../common/repositories/base.drizzle.repository';
import { ContractItem } from '../entities/contract-item.entity';
import { ContractItemFilterModel } from '../models/contract-item-filter.model';

/**
 * 1. Ensure schema.contracts has the correct column types - numerics are strings now
 * 2. Update filter conditions to match the actual column types
 * 3. Handle EnumColumns properly (for status filtering)
 * 4. Ensure date handling matches your schema's date storage format
 */
@Injectable()
export class ContractItemDrizzleRepository extends BaseDrizzleRepository<ContractItem> {
    constructor(
        @Inject(DRIZZLE_PROVIDER)
        db: NodePgDatabase<typeof schema>,
    ) {
        super(db, schema.contractItems);
    }

    async findContractItemsWithFilter(
        paginationParams: PaginationParamsModel,
        filter?: ContractItemFilterModel,
        additionalConditions: SQLWrapper[] = [],
    ): Promise<PaginatedResponseModel<ContractItem>> {
        const whereConditions: SQLWrapper[] = [...additionalConditions];

        if (filter?.search) {
            whereConditions.push(...this.buildSearchConditions(filter.search));
        }

        if (filter?.hasEndCustomerPrice !== undefined) {
            whereConditions.push(ne(schema.contractItems.endCustomerPrice, null));
        }

        if (!filter) {
            filter = { sortBy: 'serialNo', sortOrder: 'asc' } as ContractItemFilterModel;
        } else if (!filter.sortBy) {
            filter.sortBy = 'serialNo';
            filter.sortOrder = 'asc';
        }

        const result = await this.findPaginated(whereConditions, paginationParams, filter);

        const mappedData = result.data.map((record) => this.mapToEntity(record));

        return new PaginatedResponseModel<ContractItem>(mappedData, result.meta);
    }

    protected buildSearchConditions(searchTerm: string): SQLWrapper[] {
        if (!searchTerm) return [];

        return [
            or(
                ilike(schema.contracts.contractNo, `%${searchTerm}%`),
                ilike(schema.contracts.groupId, `%${searchTerm}%`),
                ilike(schema.contracts.sar, `%${searchTerm}%`),
            ),
        ];
    }

    protected buildStatusConditions(status: string | string[] | undefined): SQLWrapper[] {
        if (!status) {
            return [ne(schema.contracts.uiStatus, 'Invalid')];
        }

        const statusArray = Array.isArray(status) ? status : [status];

        if (statusArray.length === 0) {
            return [ne(schema.contracts.uiStatus, 'Invalid')];
        }

        const statusConditions = statusArray.map((s) => eq(schema.contracts.uiStatus, s as any));
        return [or(...statusConditions)];
    }

    protected buildDateConditions(endDateAfter?: string, endDateBefore?: string): SQLWrapper[] {
        const conditions: SQLWrapper[] = [];

        if (endDateAfter) {
            conditions.push(gte(schema.contracts.endDate, endDateAfter));
        }

        if (endDateBefore) {
            conditions.push(lte(schema.contracts.endDate, endDateBefore));
        }

        return conditions;
    }

    public async createContractItem(
        contractId: string,
        sar: string | null,
        said: string | null,
        groupId: string | null,
        vendorId: string,
        startDate: Date | null,
        endDate: Date,
        itemNo: number | null,
        serialNumber: string,
        productSku: string,
        productId: string,
        serviceGroupSku: string,
        serviceGroupDescription: string,
    ): Promise<string> {
        const contractItemId = uuidv4();

        const contractItem = await this.db
            .insert(schema.contractItems)
            .values({
                id: contractItemId,
                itemNo: itemNo,
                contractId: contractId,
                vendorId: vendorId,
                serialNo: serialNumber,
                productSku: productSku,
                serviceGroupSku: serviceGroupSku,
                serviceGroupLabel: serviceGroupDescription,
                data: {
                    ContractItemId: contractItemId,
                    ContractItemNo: itemNo ?? undefined,
                    ServiceLevel: serviceGroupDescription,
                    SupplierId: vendorId,
                    CoveredProductId: productId,
                    CoveredProductSku: productSku,
                    SAR: sar,
                    SAID: said,
                    SvcLvlSku: serviceGroupSku,
                    GroupID: groupId,
                    ServiceStartDate: startDate ? startDate.toISOString() : null,
                    ServiceEndDate: endDate.toISOString(),
                },
            })
            .returning();

        return contractItem[0].id;
    }

    protected mapToEntity(record: any): ContractItem {
        return {
            ...record,
            startDate: record.startDate ? new Date(record.startDate) : null,
            endDate: record.endDate ? new Date(record.endDate) : null,
            createdAt: record.createdAt ? new Date(record.createdAt) : null,
            updatedAt: record.updatedAt ? new Date(record.updatedAt) : null,
            resellerPrice: record.resellerPrice ? Number(record.resellerPrice) : null,
            endCustomerPrice: record.endCustomerPrice ? Number(record.endCustomerPrice) : null,
        } as ContractItem;
    }
}
