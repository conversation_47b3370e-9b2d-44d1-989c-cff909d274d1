import { FilterQuery, OrderDefinition } from '@mikro-orm/core';
import { SqlEntityManager } from '@mikro-orm/postgresql';
import { Inject, Injectable } from '@nestjs/common';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

import * as schema from '../../../../drizzle/schema';
import { DRIZZLE_PROVIDER } from '../../../drizzle/drizzle.provider';
import { PaginatedResponseModel, PaginationParamsModel } from '../../common/pagination/pagination.model';
import { BaseRepository } from '../../common/repositories/base.repository';
import { Contract } from '../entities/contract.entity';
import { ContractFilterModel } from '../models/contract-filter.model';

@Injectable()
export class ContractRepository extends BaseRepository<Contract> {
    constructor(
        em: SqlEntityManager,
        @Inject(DRIZZLE_PROVIDER)
        db: NodePgDatabase<typeof schema>,
    ) {
        super(em, Contract.name, db);
    }

    async findContractById(contractId: string): Promise<Contract> {
        return await this.findOne({ id: contractId });
    }

    async findContractByContractNo(contractNo: string): Promise<Contract> {
        return await this.findOne({ contractNo });
    }

    async findContractsBySerialNo(
        serialNo: string,
        paginationParams: PaginationParamsModel,
        filter?: ContractFilterModel,
    ): Promise<PaginatedResponseModel<Contract>> {
        const where: FilterQuery<Contract> = {
            contractItems: {
                serialNo,
            },
        };

        return this.findContractsWithFilter(paginationParams, filter, where);
    }

    async findContractsWithFilter(
        paginationParams: PaginationParamsModel,
        filter?: ContractFilterModel,
        additionalWhere: FilterQuery<Contract> = {},
    ): Promise<PaginatedResponseModel<Contract>> {
        const where: FilterQuery<Contract> = Object.assign({}, additionalWhere);

        if (filter?.search) {
            where['$or'] = [
                { contractNo: { $ilike: `%${filter.search}%` } },
                { groupId: { $ilike: `%${filter.search}%` } },
                { sar: { $ilike: `%${filter.search}%` } },
            ];
        }

        if (filter?.status && filter.status.length > 0) {
            where['uiStatus'] = { $in: filter.status, $ne: 'Invalid' };
        } else {
            where['uiStatus'] = { $ne: 'Invalid' };
        }

        if (filter?.endDateAfter || filter?.endDateBefore) {
            const endDateParams = {};

            if (filter?.endDateAfter) {
                endDateParams['$gte'] = new Date(filter?.endDateAfter);
            }

            if (filter?.endDateBefore) {
                endDateParams['$lte'] = new Date(filter?.endDateBefore);
            }
            where['endDate'] = {
                ...endDateParams,
            };
        }

        if (!filter) {
            filter = { sortBy: 'contractNo', sortOrder: 'asc' } as ContractFilterModel;
        } else if (!filter.sortBy) {
            filter.sortBy = 'contractNo';
            filter.sortOrder = 'asc';
        }

        const orderBy: OrderDefinition<Contract> = {};
        if (filter.sortBy === 'endUser') {
            delete filter.sortBy;
            orderBy.endUser = {
                name: filter.sortOrder,
            };
        }

        return this.findPaginated(
            where,
            paginationParams,
            filter,
            {
                ...(Object.keys(orderBy).length > 0 ? { orderBy } : {}),
            },
            ['endUser'],
        );
    }
}
