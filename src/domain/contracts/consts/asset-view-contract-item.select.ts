import { sql } from 'drizzle-orm';

import * as schema from '../../../../drizzle/schema';

export const AssetViewContractItemSelect = {
    id: schema.vContractItems.id,
    vendorId: schema.vContractItems.vendorId,
    productSku: schema.vContractItems.productSku,
    serialNo: schema.vContractItems.serialNo,
    contractNo: schema.vContractItems.contractNo,
    contractStatus: schema.vContractItems.contractStatus,
    quoteNo: sql`NULL::text as quote_no`,
    quoteStatus: sql`NULL::text as quote_status`,
    startDate: schema.vContractItems.startDate,
    endDate: schema.vContractItems.endDate,
    resellerId: schema.vContractItems.resellerId,
    endUserId: schema.vContractItems.endUserId,
    distributorId: schema.vContractItems.distributorId,
    contractId: schema.vContractItems.contractId,
    quoteId: sql`NULL::text as quote_id`,
    productName: schema.vContractItems.productName,
    resellerPriceFinal: schema.vContractItems.resellerPriceFinal,
    distributorPriceFinal: sql`NULL::numeric as distributor_price_final`,
    endCustomerPriceFinal: schema.vContractItems.endCustomerPriceFinal,
    quantity: schema.vContractItems.quantity,
    currency: schema.vContractItems.currency,
    coverageStatus: schema.vContractItems.coverageStatus,
    serviceGroupSku: schema.vContractItems.serviceGroupSku,
    serviceGroupLabel: schema.vContractItems.serviceGroupLabel,
    serviceLevelSku: schema.vContractItems.serviceLevelSku,
    serviceName: schema.vContractItems.serviceName,
    support_life_end_date: schema.vContractItems.supportLifeEndDate,
};
