import * as schema from '../../../../drizzle/schema';

export const ContractListSelect = {
    id: schema.contracts.id,
    contractNo: schema.contracts.contractNo,
    sar: schema.contracts.sar,
    status: schema.contracts.status,
    groupId: schema.contracts.groupId,
    startDate: schema.contracts.startDate,
    endDate: schema.contracts.endDate,
    resellerTotalPrice: schema.contracts.resellerTotalPrice,
    currency: schema.contracts.currency,
    uiStatus: schema.contracts.uiStatus,
    endCustomerTotalPrice: schema.contracts.endCustomerTotalPrice,
    endUserName: schema.entities.name,
    endUser: {
        id: schema.entities.id,
        name: schema.entities.name,
    },
};
