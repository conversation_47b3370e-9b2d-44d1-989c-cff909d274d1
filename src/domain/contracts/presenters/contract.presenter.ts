import { Injectable } from '@nestjs/common';

import { Contract } from '../entities/contract.entity';
import { ContractModel } from '../models/contract.model';
import { ContractListItemModel } from '../models/contract-list-item.model';

@Injectable()
export class ContractPresenter {
    private adaptDate(date: string | Date | null | undefined): string | null {
        if (!date) {
            return null;
        }

        if (date instanceof Date) {
            return date.toISOString();
        }

        const dateObj = new Date(date);
        return dateObj.toISOString();
    }

    toFullModel(entity: Contract): ContractModel {
        const model = new ContractModel();

        model.id = entity.id;
        model.contractNumber = entity.contractNo;
        model.sar = entity.sar;
        model.status = entity.status;
        model.uiStatus = entity.uiStatus;
        model.groupId = entity.groupId;
        model.startDate = this.adaptDate(entity.startDate);
        model.endDate = this.adaptDate(entity.endDate);
        model.currency = entity.currency;
        model.vendorId = entity.vendorId;
        model.distributorId = entity.distributorId;
        model.resellerId = entity.resellerId;
        model.endUserId = entity.endUserId;
        model.resellerTotalPrice = Number(entity.resellerTotalPrice);
        model.endCustomerTotalPrice = Number(entity.endCustomerTotalPrice);
        model.endUser = entity?.endUser;
        model.distributor = entity?.distributor;

        model._links = {} as any;

        model._links.self = {
            uri: `/contracts/${entity.id}`,
        };

        model._links.assets = {
            count: 0,
            uri: `/contracts/${entity.id}/assets`,
        };

        model._links.pdf = {
            count: 0,
            uri: `/contracts/${entity.id}/pdfs`,
        };

        model._links.quotes = {
            count: 0,
            uri: `/contracts/${entity.id}/quotes`,
        };

        model._links.contacts = {
            count: 0,
            uri: `/contracts/${entity.id}/contacts`,
        };

        // model._links.notes = {
        //     count: 0,
        //     uri: `/contracts/${entity.id}/notes`,
        // };

        model._links.parties = {
            count: 0,
            uri: `/contracts/${entity.id}/entities`,
        };

        return model;
    }

    toListItemModel(entity: Contract): ContractListItemModel {
        const model = new ContractListItemModel();
        model.id = entity.id;
        model.contractNumber = entity.contractNo;
        model.sar = entity.sar;
        model.status = entity.status;
        model.uiStatus = entity.uiStatus;
        model.groupId = entity.groupId;
        model.startDate = entity.startDate?.toISOString();
        model.endDate = entity.endDate?.toISOString();
        model.currency = entity.currency;
        model.resellerTotalPrice = entity.resellerTotalPrice;
        model.endCustomerTotalPrice = entity.endCustomerTotalPrice;
        model.endUser = entity?.endUser;
        model.vendor = entity?.vendor;

        // Links
        model._links = {};

        model._links.self = {
            uri: `/contracts/${entity.id}`,
        };

        model._links.endUser = {
            name: 'N/A',
            uri: `/entities/${entity.endUserId}`,
        };

        return model;
    }
}
