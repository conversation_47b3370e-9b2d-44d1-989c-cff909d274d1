import { index, jsonb, numeric, pgEnum, pgTable, text, timestamp } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm/relations';

import { entities } from '../entities/entity.schema';
import { DataSourceEnum } from './enums/data-source.enum';

export const contractUiStatus = pgEnum('contract_ui_status', [
    'Invalid',
    'Active',
    'Expiring',
    'Expired',
    'Terminated',
    'Renewed',
]);

const dataSource = pgEnum('data_source', DataSourceEnum);

export type ContractDataField = {
    [x: string]: any;

    ContractId?: number | string;
    ContractNo?: string;
    ContractType?: number;
    ContractTypeId?: number;
    ContractStatusId?: number;
    ContractSubTypeId?: number;
    TesediResellerTotalPrice?: number; // Reseller Total Price (Total)
    TesediEndCustomerTotalPrice?: number; // End Customer Total Price (Total)
    GroupID?: string;
    BillingCycleCode?: string;
    HwContact?: string;
    SwContact?: string;
    SysContact?: string;
    SAR?: string;
    ContractContacts?: { ContactId?: string; ContactTypeName?: string; EntityId: number; EmailAddress?: string }[];
};

export const contracts = pgTable(
    'contracts',
    {
        id: text().primaryKey().notNull(),
        contractNo: text('contract_no'),
        sar: text(),
        status: text(),
        groupId: text('group_id'),
        startDate: text('start_date'),
        endDate: text('end_date'),
        currency: text(),
        vendorId: text('vendor_id'),
        distributorId: text('distributor_id'),
        resellerId: text('reseller_id'),
        endUserId: text('end_user_id'),
        resellerTotalPrice: numeric('reseller_total_price', { precision: 20, scale: 4 }),
        data: jsonb().$type<ContractDataField>(),
        uiStatus: contractUiStatus('ui_status'),
        endCustomerTotalPrice: numeric('end_customer_total_price', { precision: 20, scale: 4 }),

        createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
        updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }).defaultNow().notNull(),

        source: dataSource('source').default(DataSourceEnum.iAsset).notNull(),
    },
    (table) => [
        index().using('btree', table.contractNo.asc().nullsLast().op('text_ops')),
        index().using('btree', table.distributorId.asc().nullsLast().op('text_ops')),
        index().using('btree', table.endUserId.asc().nullsLast().op('text_ops')),
        index().using('btree', table.resellerId.asc().nullsLast().op('text_ops')),
        index().using('btree', table.uiStatus.asc().nullsLast().op('enum_ops')),
        index().using('btree', table.vendorId.asc().nullsLast().op('text_ops')),
        index('idx_contracts_r_id').using('btree', table.resellerId.asc().nullsLast().op('text_ops')),
        index('idx_contracts_group_id').on(table.groupId),
        index('idx_contracts_sar').on(table.sar),
    ],
);

export const contractsRelations = relations(contracts, ({ one }) => ({
    endUser: one(entities, {
        fields: [contracts.endUserId],
        references: [entities.id],
    }),
}));
