export const CONTRACT_CONFIG = {
    DEFAULT_PAGINATION_LIMIT: parseInt(process.env.PAGINATION_DEFAULT_LIMIT, 10) || 50,
    PDF_DOWNLOAD_TIMEOUT: 30000,
    CACHE_TTL: 300, // 5 minutes
    MAX_BATCH_CONTRACTS: 100,
    FRESHDESK_TICKET_TAGS: {
        QUOTE_REQUEST: 'quote-request',
        CONTRACT_UPDATE: 'contract-update',
    },
} as const;

export const CONTRACT_VALIDATION = {
    MIN_RESELLER_IDS: 1,
    MAX_CONTRACT_NUMBERS: 50,
    REQUIRED_FIELDS: {
        CONTRACT_ID: 'contractId',
        RESELLER_IDS: 'resellerIds',
        CONTRACT_NUMBER: 'contractNumber',
    },
} as const;

export const CONTRACT_LOGGING = {
    CONTEXTS: {
        CONTRACT_RETRIEVAL: 'ContractRetrieval',
        CONTRACT_CREATION: 'ContractCreation',
        PDF_GENERATION: 'PdfGeneration',
        NOTIFICATION: 'ContractNotification',
        QUOTE_REQUEST: 'QuoteRequest',
        VALIDATION: 'ContractValidation',
    },
    PERFORMANCE_THRESHOLDS: {
        SLOW_QUERY_MS: 1000,
        VERY_SLOW_QUERY_MS: 5000,
    },
} as const;
