export enum EventNameEnum {
    DB_WILDCARD = 'db.*',
    DB_WATCHER_WILDCARD = 'db.watcher.*',
    DB_WATCHER_CONTRACT_WILDCARD = 'db.watcher.contract.*',
    DB_WATCHER_CONTRACT_UPDATED = 'db.watcher.contract.updated',
    DB_WATCHER_CONTRACT_CREATED = 'db.watcher.contract.created',
    DB_WATCHER_CONTRACT_DELETED = 'db.watcher.contract.deleted',
    DB_WATCHER_QUOTE_WILDCARD = 'db.watcher.quote.*',
    DB_WATCHER_QUOTE_UPDATED = 'db.watcher.quote.updated',
    DB_WATCHER_QUOTE_CREATED = 'db.watcher.quote.created',
    DB_WATCHER_QUOTE_DELETED = 'db.watcher.quote.deleted',
    ORGANIZATION_WILDCARD = 'asset-hub.organization.*',
    ORGANIZATION_CREATED_V1 = 'asset-hub.organization.created:v1',
    ORGANIZATION_UPDATED_V1 = 'asset-hub.organization.updated:v1',
    ORGANIZATION_DELETED_V1 = 'asset-hub.organization.deleted:v1',
}
