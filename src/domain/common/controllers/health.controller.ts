import { Controller, Get } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { DisableAuthenticatedGuard } from '../../../auth/authenticated.guard';
import { DisablePermissionsGuard } from '../../../rbac/permissions.decorator';

@ApiTags('Health')
@Controller('health')
export class HealthController {
    @Get()
    @DisableAuthenticatedGuard()
    @DisablePermissionsGuard()
    @ApiOperation({ summary: 'Get application health status' })
    @ApiResponse({
        status: 200,
        description: 'Application health status',
        schema: {
            type: 'object',
            properties: {
                status: { type: 'string' },
                timestamp: { type: 'string', format: 'date-time' },
            },
        },
    })
    async getHealth(): Promise<{ status: string; timestamp: string }> {
        return {
            status: 'healthy',
            timestamp: new Date().toISOString(),
        };
    }
}
