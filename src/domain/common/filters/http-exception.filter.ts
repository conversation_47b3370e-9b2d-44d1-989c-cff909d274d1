import { ArgumentsHost, Catch, ExceptionFilter, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { Response } from 'express';

import { ErrorModel } from '../models/error.model';

@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
    private readonly logger = new Logger(HttpExceptionFilter.name);

    catch(exception: any, host: ArgumentsHost) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse<Response>();
        const request = ctx.getRequest();

        const errorModel = new ErrorModel();
        let status = exception instanceof HttpException ? exception.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR;

        let message = exception.message || 'Internal server error';
        let error = exception.message || 'Internal server error';
        let errorCode: string | undefined;
        let errors: [] | undefined;

        if (exception.response) {
            message = JSON.stringify(exception.response?.message);
            error = exception.response.error;
            status = exception.response.statusCode;
            errorCode = exception.response.errorCode;
            errors = exception.response.errors;
        }

        errorModel.statusCode = status;
        errorModel.message = message;
        errorModel.error = error;
        errorModel.errors = errors;
        if (errorCode) {
            errorModel.errorCode = errorCode;
        }

        const correlationId = request.headers['x-correlation-id'];
        const userId = request.user?.sub;
        const organizationId = request.context?.organizationId;

        this.logger.error('HTTP Exception caught', {
            correlationId,
            userId,
            organizationId,
            method: request.method,
            url: request.url,
            userAgent: request.headers['user-agent'],
            ip: request.ip || request.connection?.remoteAddress,
            status,
            message,
            error,
            exceptionType: exception.constructor.name,
            isHttpException: exception instanceof HttpException,
            stack: exception.stack,
            requestBody: request.method !== 'GET' ? this.sanitizeRequestBody(request.body) : undefined,
            queryParams: Object.keys(request.query || {}).length > 0 ? request.query : undefined,
        });

        response.status(status).json(errorModel.getData());
    }

    private sanitizeRequestBody(body: any): any {
        if (!body || typeof body !== 'object') return body;

        const sanitized = { ...body };
        const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth', 'credential'];

        const sanitizeObject = (obj: any): any => {
            if (Array.isArray(obj)) {
                return obj.map((item) => sanitizeObject(item));
            }

            if (obj && typeof obj === 'object') {
                const result = {};
                for (const [key, value] of Object.entries(obj)) {
                    if (sensitiveFields.some((field) => key.toLowerCase().includes(field))) {
                        result[key] = '[REDACTED]';
                    } else {
                        result[key] = sanitizeObject(value);
                    }
                }
                return result;
            }

            return obj;
        };

        return sanitizeObject(sanitized);
    }
}
