import { sql } from 'drizzle-orm';
import { bigserial, integer, jsonb, pgEnum, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

export const eventLogActionEnum = pgEnum('event_log_action', ['CREATED', 'UPDATED', 'DELETED']);

export const eventLogStatusEnum = pgEnum('event_log_status', ['pending', 'processing', 'completed', 'failed']);

export const eventLog = pgTable('event_log', {
    id: bigserial('id', { mode: 'bigint' }).primaryKey().notNull(),
    entity: text('entity').notNull(),
    entityId: text('entity_id').notNull(),
    action: eventLogActionEnum('action').notNull(),
    changes: jsonb('changes'),
    organizationId: uuid('organization_id'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' })
        .default(sql`CURRENT_TIMESTAMP`)
        .notNull(),
    processedAt: timestamp('processed_at', { withTimezone: true, mode: 'string' }),
    status: eventLogStatusEnum('status').default('pending').notNull(),
    retryCount: integer('retry_count').default(0).notNull(),
    errorMessage: text('error_message'),
});
