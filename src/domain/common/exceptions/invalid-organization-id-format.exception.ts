import { BadRequestException } from '@nestjs/common';
import { I18nContext } from 'nestjs-i18n';

import { I18nPath } from '../../../../i18n/generated/i18n.generated';

export class InvalidOrganizationIdFormatException extends BadRequestException {
    constructor(organizationId?: string) {
        super(
            I18nContext.current().t<I18nPath>('errors.ORGANIZATION.INVALID_ORGANIZATION_ID_FORMAT', {
                args: {
                    id: organizationId,
                },
            }),
        );
    }
}
