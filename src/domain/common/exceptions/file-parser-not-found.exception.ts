import { NotFoundException } from '@nestjs/common';
import { I18nContext } from 'nestjs-i18n';

import { I18nPath } from '../../../../i18n/generated/i18n.generated';

export class FileParserNotFoundException extends NotFoundException {
    constructor(format: string) {
        super(
            I18nContext.current().t<I18nPath>('errors.FILES.NO_PARSER_FOR_FILE_FORMAT', {
                args: {
                    format,
                },
            }),
        );
    }
}
