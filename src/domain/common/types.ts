import { CurrencyEnum } from './enums';

export type ContractItemType = {
    id?: string | null;
    item_no?: number | null;
    contract_id?: string | null;
    vendor_id?: string | null;
    serial_no?: string | null;
    product_sku?: string | null;
    service_level_sku?: string | null;
    service_name?: string | null;
    service_group_sku?: string | null;
    service_group_label?: string | null;
    reseller_price?: number;
    // distributor_price?: number; // Hidden because of AH-101
    end_customer_price?: number;
    quantity?: number;
    coverage_status?: string | null;
    start_date?: Date | null;
    end_date?: Date | null;
    support_life_end_date?: Date | null;
    data?: null | {
        ContractItemId?: number;
        ContractItemNo?: number;
        ServiceProductID?: number; // Service Level Product ID - Service Level Description can be extracted from there
        ServiceProductSku?: string; // Service Level SKU
        ServiceLevel?: string; // Service Group Description
        Quantity?: number; // Units quantity
        SupplierId?: number; // Vendor ID
        SupplierContractNo?: string;
        CoveredProductId?: number; // Product ID
        CoveredProductSku?: string; // Product SKU
        CustomerInvoiceAmount?: number | null; // [DEPRECATED] Reseller Price (per unit) - USE TesediResellerPrice INSTEAD
        CustomerRetailPrice?: number | null; // [DEPRECATED] End Customer Price (per unit) - USE TesediEndCustomerPrice INSTEAD
        EndUserSiteId?: number;
        EndUserEntityId?: number;
        EndUserContactId?: number;
        RenewalContractItemId?: number;
        TesediResellerPrice?: number;
        TesediEndCustomerPrice?: number;
        AssetId?: string | null;
        SAR?: string;
        SAID?: string;
        DocId?: string;
        DocType?: string;
        EquipNo?: string;
        SvcLvlSku?: string; // Service Group SKU
        SvcLvlDesc?: string;
        MonthlyListPrice?: string;
        MonthlyDisc?: string;
        MonthlyNetPrice?: string;
        MonthlyListPriceEC?: string;
        EnvID?: string;
        SalesOrg?: string;
        PSPId?: string;
        PSPName?: string;
        GroupID?: string;
        MyDisc?: string;
        SndDisc?: string;
        PpDisc?: string;
        HwContact?: string;
        SwContact?: string;
        SysContact?: string;
        ProductLine?: string;
        ProductType?: string;
        SupportLifeEndDate?: string;
    };
};

export type QuoteItemType = {
    id?: string | null;
    item_no?: number | null;
    quote_id?: string | null;
    vendor_id?: string | null;
    serial_no?: string | null;
    product_sku?: string | null;
    service_level_sku?: string | null;
    service_name?: string | null;
    service_group_sku?: string | null;
    service_group_label?: string | null;
    reseller_price?: number;
    // distributor_price?: number; // Hidden because of AH-101
    end_customer_price?: number;
    quantity?: number;
    currency?: CurrencyEnum | null;
    coverage_status?: string | null;
    start_date?: Date | null;
    end_date?: Date | null;
    data?: null | {
        [x: string]: any;

        QuoteItemId?: number;
        ItemNo?: number;
        Sku?: string; // Service Level SKU
        Description?: string; // Service Level Description
        Quantity?: number; // Units quantity
        SerialNo?: string;
        CoveredSku?: string; // Product SKU
        VendorEntityId?: number;
        SellRate?: number;
        MSRP?: number;
        RetailPrice?: number; // [DEPRECATED] End Customer Price (per unit) - USE TesediEndCustomerPrice INSTEAD
        // BuyPrice?: number; // [DEPRECATED] Distributor Price (per unit) - USE TesediDistributorPrice INSTEAD // Hidden because of AH-101
        TotalBuyPrice?: number;
        SellPrice?: number; // [DEPRECATED] Reseller Price (per unit) - USE TesediResellerPrice INSTEAD
        TotalValue?: number;
        UnitCOGS?: number;
        TotalCOGS?: number;
        BuyRate?: number;
        MDFAmount?: number;
        UpliftAmount?: number | null;
        EnduserSiteId?: number;
        AttachToContractItemId?: number;
        SourceContractItemId?: number;
        CoveredProductId?: number; // Product ID
        ServiceLevel?: string | null; // Service Group Description
        SAR?: string; // Support Account Reference ID
        SAID?: string; // Service Agreement ID
        DocId?: string;
        DocType?: string;
        EquipNo?: string;
        SvcLvlSku?: string; // Service Group SKU
        SvcLvlDesc?: string;
        MonthlyListPrice?: string;
        MonthlyDisc?: string;
        MonthlyNetPrice?: string;
        MonthlyListPriceEC?: string;
        EnvID?: string;
        SalesOrg?: string;
        PSPId?: string;
        PSPName?: string;
        GroupID?: string; // Covered SKU Description
        MyDisc?: string;
        SndDisc?: string;
        PpDisc?: string;
        HwContact?: string;
        SwContact?: string;
        SysContact?: string;
        SupportLifeEndDate?: string;
    };
};

export type AssetType = {
    id?: string | null;
    name?: string | null;
    vendor_id?: string | null;
    end_user_id?: string | null;
    serial_number?: string | null;
    coverage_status?: string | null;
    service_group_sku?: string | null;
    service_group_label?: string | null;
    service_sku?: string | null;
    product_sku?: string | null;
    items_count?: string | null;
    quantity?: number | null;
    reseller_price_final_sum?: number | null;
    // distributor_price_final_sum?: number | null; // Hidden because of AH-101
    end_customer_price_final_sum?: number | null;
    currency?: CurrencyEnum | null;
    start_date?: Date | null;
    end_date?: Date | null;
    items?: (ContractItemType | QuoteItemType)[] | null;
    support_life_end_date?: Date | null;
};
