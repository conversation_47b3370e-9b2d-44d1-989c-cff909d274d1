import { BlobServiceClient } from '@azure/storage-blob';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { v4 } from 'uuid';

import { StoreRequestDto, StoreResponseDto } from '../../dtos';
import { Context } from '../../interfaces/context.interface';
import { ObjectStorageService } from './object-storage.service';

@Injectable()
export class AzureStorageService extends ObjectStorageService {
    private readonly logger = new Logger(AzureStorageService.name);
    private readonly azureStorageClient: BlobServiceClient;

    constructor(private readonly configService: ConfigService) {
        super();

        this.azureStorageClient = BlobServiceClient.fromConnectionString(
            configService.get('AZURE_STORAGE_CONNECTION_STRING'),
        );
    }

    async store(
        context: Context,
        { file, path: basePath, bucket, useFilename = false, region = 'us-east-1' }: StoreRequestDto,
    ): Promise<StoreResponseDto> {
        const id = v4();
        const ext = file.originalname.split('.')[1];
        const filename = useFilename ? file.originalname : `${id}.${ext}`;
        const path = !!basePath ? `${basePath}/${filename}` : `${filename}`;

        this.logger.log('Azure storage upload started', {
            userId: context.user?.id,
            organizationId: context.organizationId,
            fileId: id,
            fileName: file.originalname,
            fileSize: file.size,
            mimeType: file.mimetype,
            bucket,
            path,
            useFilename,
        });

        try {
            const container = await this.getContainer(bucket);

            this.logger.debug('Container obtained for Azure storage upload', {
                userId: context.user?.id,
                fileId: id,
                bucket,
                containerName: container.containerName,
            });

            const blockBlobClient = container.getBlockBlobClient(path);
            await blockBlobClient.upload(file.buffer, file.size, {
                blobHTTPHeaders: {
                    blobContentType: file.mimetype,
                },
            });

            const url = blockBlobClient.url;

            this.logger.log('Azure storage upload completed successfully', {
                userId: context.user?.id,
                organizationId: context.organizationId,
                fileId: id,
                fileName: file.originalname,
                fileSize: file.size,
                mimeType: file.mimetype,
                bucket,
                path,
                url,
            });

            return {
                id,
                url,
            };
        } catch (error) {
            this.logger.error('Azure storage upload failed', {
                userId: context.user?.id,
                organizationId: context.organizationId,
                fileId: id,
                fileName: file.originalname,
                fileSize: file.size,
                mimeType: file.mimetype,
                bucket,
                path,
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined,
            });
            throw error;
        }
    }

    private async getContainer(container: string) {
        let containerClient = this.azureStorageClient.getContainerClient(container);
        const containerExists = await containerClient.exists();

        if (!containerExists) {
            await containerClient.create();
        }

        containerClient = this.azureStorageClient.getContainerClient(container);

        return containerClient;
    }
}
