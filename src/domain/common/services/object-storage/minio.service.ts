import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { MinioService } from 'nestjs-minio-client';
import { v4 } from 'uuid';

import { StoreRequestDto, StoreResponseDto } from '../../dtos';
import { Context } from '../../interfaces/context.interface';
import { ObjectStorageService } from './object-storage.service';

@Injectable()
export class MinioStorageService extends ObjectStorageService {
    private readonly logger = new Logger(MinioStorageService.name);

    constructor(
        private readonly configService: ConfigService,
        private readonly minioService: MinioService,
    ) {
        super();
    }

    async store(
        context: Context,
        { file, path: basePath, bucket, useFilename = false, region = 'us-east-1' }: StoreRequestDto,
    ): Promise<StoreResponseDto> {
        const id = v4();
        const ext = file.originalname.split('.')[1];
        const filename = useFilename ? file.originalname : `${id}.${ext}`;
        const path = !!basePath ? `${basePath}/${filename}` : `${filename}`;

        this.logger.log('MinIO storage upload started', {
            userId: context.user?.id,
            organizationId: context.organizationId,
            fileId: id,
            fileName: file.originalname,
            fileSize: file.size,
            mimeType: file.mimetype,
            bucket,
            path,
            region,
            useFilename,
        });

        try {
            await this.prepareBucket(bucket, region);

            this.logger.debug('Bucket prepared for MinIO upload', {
                userId: context.user?.id,
                fileId: id,
                bucket,
                region,
            });

            await this.minioService.client.putObject(bucket, path, file.buffer, file.size, {
                'Content-Type': file.mimetype,
            });

            const url = `${this.configService.get('MINIO_DOMAIN')}/${bucket}/${path}`;

            this.logger.log('MinIO storage upload completed successfully', {
                userId: context.user?.id,
                organizationId: context.organizationId,
                fileId: id,
                fileName: file.originalname,
                fileSize: file.size,
                mimeType: file.mimetype,
                bucket,
                path,
                url,
                region,
            });

            return {
                id,
                url,
            };
        } catch (error) {
            this.logger.error('MinIO storage upload failed', {
                userId: context.user?.id,
                organizationId: context.organizationId,
                fileId: id,
                fileName: file.originalname,
                fileSize: file.size,
                mimeType: file.mimetype,
                bucket,
                path,
                region,
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined,
            });
            throw error;
        }
    }

    private async prepareBucket(bucketName: string, region: string): Promise<void> {
        try {
            const bucketExists = await this.minioService.client.bucketExists(bucketName);

            if (!bucketExists) {
                this.logger.log('Creating new MinIO bucket', {
                    bucketName,
                    region,
                });

                await this.minioService.client.makeBucket(bucketName, region);

                this.logger.log('MinIO bucket created successfully', {
                    bucketName,
                    region,
                });
            } else {
                this.logger.debug('MinIO bucket already exists', {
                    bucketName,
                    region,
                });
            }
        } catch (error) {
            this.logger.error('Failed to prepare MinIO bucket', {
                bucketName,
                region,
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined,
            });
            throw error;
        }
    }
}
