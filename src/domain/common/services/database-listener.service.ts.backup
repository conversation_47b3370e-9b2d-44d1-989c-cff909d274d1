import { Inject, Injectable, Logger, OnModuleDestroy, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { backOff } from 'exponential-backoff';
import { Client, ClientConfig, Pool } from 'pg';

import { PG_POOL_PROVIDER } from '../../../drizzle/drizzle.provider';

interface DataChangePayload<T = unknown> {
    action: 'CREATE' | 'UPDATE' | 'DELETE';
    entity: string;
    entityId: string;
    before: T | null;
    after: T | null;
}

export interface ListenerHealthStatus {
    connected: boolean;
    lastNotification: Date | null;
    reconnectAttempts: number;
    totalNotifications: number;
    uptime: number;
    lastError: string | null;
    enabled: boolean;
    consecutiveFailures: number;
    lastHeartbeat: Date | null;
}

enum ConnectionState {
    DISCONNECTED = 'DISCONNECTED',
    CONNECTING = 'CONNECTING',
    CONNECTED = 'CONNECTED',
    RECONNECTING = 'RECONNECTING',
    FAILED = 'FAILED',
}

@Injectable()
export class DataChangesListenerService implements OnModuleInit, OnModuleDestroy {
    private client: Client | null = null;
    private connectionState: ConnectionState = ConnectionState.DISCONNECTED;
    private ending: boolean = false;
    private readonly logger = new Logger(DataChangesListenerService.name);

    // Health monitoring
    private startTime: Date = new Date();
    private lastNotification: Date | null = null;
    private lastHeartbeat: Date | null = null;
    private reconnectAttempts: number = 0;
    private consecutiveFailures: number = 0;
    private totalNotifications: number = 0;
    private lastError: string | null = null;
    private isEnabled: boolean = true;

    // Intervals
    private heartbeatInterval: NodeJS.Timeout | null = null;
    private reconnectTimeout: NodeJS.Timeout | null = null;

    // Configuration
    private readonly maxConsecutiveFailures: number;
    private readonly heartbeatIntervalMs: number;
    private readonly connectionTimeout: number;
    private readonly isRailway: boolean;
    private readonly dbConfig: ClientConfig;

    constructor(
        private readonly configService: ConfigService,
        private readonly eventEmitter: EventEmitter2,
        @Inject(PG_POOL_PROVIDER) pool: Pool,
    ) {
        const listenerEnabled = this.configService.get('DATABASE_LISTENER_ENABLED', 'true');
        this.isEnabled = listenerEnabled === 'true';

        this.isRailway = !!process.env.RAILWAY_ENVIRONMENT;

        this.maxConsecutiveFailures = this.configService.get('DATABASE_LISTENER_MAX_FAILURES', 5);
        this.heartbeatIntervalMs = this.configService.get(
            'DATABASE_LISTENER_HEARTBEAT_INTERVAL',
            this.isRailway ? 10000 : 30000,
        );
        this.connectionTimeout = this.configService.get(
            'DATABASE_LISTENER_CONNECTION_TIMEOUT',
            this.isRailway ? 5000 : 10000,
        );

        const poolConfig = pool.options || {};
        this.dbConfig = {
            host: (poolConfig as any).host || this.configService.get('DATABASE_HOST'),
            port: (poolConfig as any).port || this.configService.get('DATABASE_PORT'),
            database: (poolConfig as any).database || this.configService.get('DATABASE_NAME'),
            user: (poolConfig as any).user || this.configService.get('DATABASE_USER'),
            password: (poolConfig as any).password || this.configService.get('DATABASE_PASSWORD'),
            connectionTimeoutMillis: this.connectionTimeout,
            keepAlive: true,
            keepAliveInitialDelayMillis: 10000,
            ...(this.isRailway && {
                keepAliveInitialDelayMillis: 1000,
                statement_timeout: 30000,
                query_timeout: 30000,
                application_name: 'assethub-listener',
            }),
        };

        if (!this.isEnabled) {
            this.logger.warn('Database listener is disabled');
        } else {
            this.logger.log(
                `Database listener initialized (Railway: ${this.isRailway}, Heartbeat: ${this.heartbeatIntervalMs}ms)`,
            );
        }
    }

    async onModuleInit(): Promise<void> {
        if (!this.isEnabled) {
            return;
        }

        await this.connect();
    }

    async onModuleDestroy(): Promise<void> {
        await this.end();
    }

    public async connect() {
        if (!this.isEnabled) {
            return;
        }

        if (this.ending) {
            throw new Error('Connection already closing');
        }

        if (this.connectionState === ConnectionState.CONNECTING) {
            this.logger.warn('Connection already in progress');
            return;
        }

        try {
            await this.establishConnection();
        } catch (err) {
            this.lastError = err.message;
            this.logger.error({ err }, 'Failed to connect listener');
            throw err;
        }
    }

    private async establishConnection() {
        if (this.ending || !this.isEnabled) {
            this.logger.debug('Skipping connection establishment - service ending or disabled');
            return;
        }

        this.connectionState = ConnectionState.CONNECTING;
        this.reconnectAttempts++;

        this.logger.log(
            `Attempting to establish connection (attempt: ${this.reconnectAttempts}, consecutive failures: ${this.consecutiveFailures})`,
        );

        try {
            this.logger.debug('Cleaning up existing connection');
            await this.cleanupConnection();

            this.logger.debug('Creating new database client');
            this.client = new Client(this.dbConfig);

            this.logger.debug('Setting up client event handlers');
            this.setupClientHandlers();

            this.logger.debug(`Connecting to database with ${this.connectionTimeout}ms timeout`);
            await Promise.race([
                this.client.connect(),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('Connection timeout')), this.connectionTimeout),
                ),
            ]);

            this.logger.debug('Registering LISTEN data_changes');
            await this.client.query('LISTEN data_changes');

            this.connectionState = ConnectionState.CONNECTED;
            this.lastError = null;
            this.consecutiveFailures = 0;
            this.reconnectAttempts = 0;

            this.logger.log(
                `Successfully connected to PostgreSQL for LISTEN/NOTIFY (Railway: ${this.isRailway}, Host: ${this.dbConfig.host})`,
            );

            this.logger.debug('Starting heartbeat monitoring');
            this.startHeartbeat();
        } catch (error) {
            this.connectionState = ConnectionState.FAILED;
            this.lastError = error.message;
            this.consecutiveFailures++;

            this.logger.error(
                {
                    err: error,
                    attempt: this.reconnectAttempts,
                    consecutiveFailures: this.consecutiveFailures,
                    host: this.dbConfig.host,
                    database: this.dbConfig.database,
                    timeout: this.connectionTimeout,
                },
                `Connection establishment failed (attempt: ${this.reconnectAttempts}, consecutive failures: ${this.consecutiveFailures})`,
            );

            if (this.consecutiveFailures < this.maxConsecutiveFailures) {
                this.logger.warn(
                    `Scheduling reconnection attempt (${this.consecutiveFailures}/${this.maxConsecutiveFailures} failures)`,
                );
                await this.scheduleReconnection();
            } else {
                this.logger.error(
                    `Max consecutive failures reached (${this.maxConsecutiveFailures}), disabling database listener`,
                );
                this.isEnabled = false;
            }

            throw error;
        }
    }

    private setupClientHandlers() {
        if (!this.client) return;

        this.client.on('notification', (notification) => {
            this.handleNotification(notification.channel, notification.payload);
        });

        this.client.on('error', async (err) => {
            this.lastError = err.message;
            this.connectionState = ConnectionState.FAILED;
            this.consecutiveFailures++;

            this.logger.error(
                {
                    err,
                    consecutiveFailures: this.consecutiveFailures,
                    maxConsecutiveFailures: this.maxConsecutiveFailures,
                    ending: this.ending,
                    enabled: this.isEnabled,
                },
                `Postgres client error (consecutive failures: ${this.consecutiveFailures}/${this.maxConsecutiveFailures})`,
            );

            if (!this.ending) {
                this.logger.warn('Client error triggered reconnection scheduling');
                await this.scheduleReconnection();
            } else {
                this.logger.debug('Skipping reconnection due to service ending');
            }
        });

        this.client.on('end', async () => {
            if (this.connectionState === ConnectionState.CONNECTED) {
                this.connectionState = ConnectionState.DISCONNECTED;
                this.logger.warn(
                    `Client connection ended unexpectedly (ending: ${this.ending}, enabled: ${this.isEnabled})`,
                );

                if (!this.ending) {
                    this.logger.warn('Connection end triggered reconnection scheduling');
                    await this.scheduleReconnection();
                } else {
                    this.logger.debug('Skipping reconnection due to service ending');
                }
            } else {
                this.logger.debug(`Client connection ended (state was: ${this.connectionState})`);
            }
        });
    }

    private async scheduleReconnection() {
        if (this.ending || !this.isEnabled || this.connectionState === ConnectionState.RECONNECTING) {
            this.logger.debug(
                `Skipping reconnection scheduling - ending: ${this.ending}, enabled: ${this.isEnabled}, state: ${this.connectionState}`,
            );
            return;
        }

        this.connectionState = ConnectionState.RECONNECTING;

        if (this.reconnectTimeout) {
            clearTimeout(this.reconnectTimeout);
        }

        const numOfAttempts = 10;
        const startingDelay = this.isRailway ? 500 : 1000;
        const maxDelay = this.isRailway ? 15000 : 30000;

        this.logger.warn(
            `Starting reconnection with exponential backoff (${numOfAttempts} attempts, ${startingDelay}ms-${maxDelay}ms delay)`,
        );

        try {
            await backOff(() => this.establishConnection(), {
                numOfAttempts,
                startingDelay,
                maxDelay,
                timeMultiple: 2,
                jitter: 'full',
                retry: (error, attemptNumber) => {
                    this.logger.warn(`Reconnection attempt ${attemptNumber}/${numOfAttempts} failed: ${error.message}`);
                    const shouldRetry = !this.ending && this.isEnabled;
                    if (!shouldRetry) {
                        this.logger.warn(
                            `Stopping reconnection attempts - ending: ${this.ending}, enabled: ${this.isEnabled}`,
                        );
                    }
                    return shouldRetry;
                },
            });

            this.logger.log('Reconnection successful after backoff attempts');
        } catch (error) {
            this.logger.error(
                {
                    err: error,
                    totalAttempts: numOfAttempts,
                    consecutiveFailures: this.consecutiveFailures,
                    maxConsecutiveFailures: this.maxConsecutiveFailures,
                },
                `All ${numOfAttempts} reconnection attempts failed`,
            );
            this.connectionState = ConnectionState.FAILED;

            if (this.consecutiveFailures >= this.maxConsecutiveFailures) {
                this.logger.error('Disabling database listener after max consecutive failures');
                this.isEnabled = false;
            }
        }
    }

    private async cleanupConnection() {
        this.logger.debug('Starting connection cleanup');

        if (this.heartbeatInterval) {
            this.logger.debug('Clearing heartbeat interval');
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }

        if (this.reconnectTimeout) {
            this.logger.debug('Clearing reconnect timeout');
            clearTimeout(this.reconnectTimeout);
            this.reconnectTimeout = null;
        }

        if (this.client) {
            this.logger.debug('Cleaning up database client');
            try {
                if (this.connectionState === ConnectionState.CONNECTED) {
                    this.logger.debug('Unregistering LISTEN data_changes');
                    await this.client.query('UNLISTEN data_changes').catch((error) => {
                        this.logger.debug('Failed to UNLISTEN data_changes', error);
                    });
                }
                this.logger.debug('Ending client connection');
                await this.client.end();
            } catch (error) {
                this.logger.warn('Error during client cleanup', error);
            } finally {
                this.client = null;
                this.logger.debug('Client reference cleared');
            }
        }

        this.logger.debug('Connection cleanup completed');
    }

    public async end() {
        this.ending = true;
        this.connectionState = ConnectionState.DISCONNECTED;

        await this.cleanupConnection();

        this.logger.log('Database listener service ended');
    }

    private toKebabCase(str: string): string {
        return str
            .trim()
            .replace(/([a-z0-9])([A-Z])/g, '$1-$2')
            .replace(/([A-Z])([A-Z][a-z])/g, '$1-$2')
            .toLowerCase();
    }

    /**
     * Handles the notification received from Postgres.
     */
    private handleNotification(channel: string, payload: string): void {
        try {
            const data: DataChangePayload = JSON.parse(payload);
            this.lastNotification = new Date();
            this.totalNotifications++;

            this.logger.log(`Received notification on channel ${channel}`, JSON.stringify(data, null, 2));

            const eventName = `db.watcher.${this.toKebabCase(data.entity)}.${data.action.toLowerCase()}`;

            this.eventEmitter.emit(eventName, data);
            this.logger.log(`Emitted event ${eventName}`);
        } catch (error) {
            this.lastError = `Failed to parse payload: ${error.message}`;
            this.logger.error({ err: error }, 'Failed to parse notification payload');
        }
    }

    /**
     * Start heartbeat to check connection health
     */
    private startHeartbeat() {
        if (!this.isEnabled || this.heartbeatInterval) {
            return;
        }

        this.heartbeatInterval = setInterval(async () => {
            if (this.client && this.connectionState === ConnectionState.CONNECTED) {
                try {
                    const start = Date.now();
                    await this.client.query('SELECT 1');
                    const duration = Date.now() - start;

                    this.lastHeartbeat = new Date();
                    this.consecutiveFailures = 0;

                    if (duration > 1000) {
                        this.logger.warn(`Heartbeat query took ${duration}ms`);
                    }
                } catch (error) {
                    this.consecutiveFailures++;
                    this.lastError = `Heartbeat failed: ${error.message}`;
                    this.logger.error(
                        `Heartbeat check failed (consecutive failures: ${this.consecutiveFailures})`,
                        error,
                    );

                    if (!this.ending) {
                        this.connectionState = ConnectionState.FAILED;
                        await this.scheduleReconnection();
                    }
                }
            }
        }, this.heartbeatIntervalMs);

        this.logger.log(`Heartbeat started with interval: ${this.heartbeatIntervalMs}ms`);
    }

    public getHealthStatus(): ListenerHealthStatus {
        const now = new Date();
        const uptime = Math.floor((now.getTime() - this.startTime.getTime()) / 1000);

        return {
            connected: this.connectionState === ConnectionState.CONNECTED,
            lastNotification: this.lastNotification,
            reconnectAttempts: this.reconnectAttempts,
            totalNotifications: this.totalNotifications,
            uptime,
            lastError: this.lastError,
            enabled: this.isEnabled,
            consecutiveFailures: this.consecutiveFailures,
            lastHeartbeat: this.lastHeartbeat,
        };
    }

    public async forceReconnect(): Promise<void> {
        if (!this.isEnabled) {
            throw new Error('Database listener is disabled');
        }

        this.logger.warn('Forcing reconnection of database listener');
        this.consecutiveFailures = 0;
        await this.establishConnection();
    }

    public getConnectionState(): ConnectionState {
        return this.connectionState;
    }

    public isConnected(): boolean {
        return this.connectionState === ConnectionState.CONNECTED;
    }
}
