import { Injectable } from '@nestjs/common';
import * as XLSX from 'xlsx';

import { IFileParser } from '../interfaces/file-parser.interface';

@Injectable()
export class XlsxParserService implements IFileParser {
    toJson(file: Express.Multer.File): Record<string, any>[] {
        const { buffer } = file;
        const workbook = XLSX.read(buffer, { type: 'buffer' });
        const sheetName = workbook.SheetNames[0];
        const sheet = workbook.Sheets[sheetName];

        return XLSX.utils.sheet_to_json(sheet, { defval: null });
    }

    toMatrix(file: Express.Multer.File): any[][] {
        const { buffer } = file;
        const workbook = XLSX.read(buffer, { type: 'buffer' });
        const sheet = workbook.Sheets[workbook.SheetNames[0]];

        return XLSX.utils.sheet_to_json(sheet, { header: 1, defval: '' });
    }
}
