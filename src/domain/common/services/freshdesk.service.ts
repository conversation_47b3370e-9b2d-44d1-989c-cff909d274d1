import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as FreshdeskType from 'freshdesk-api';
import { Readable } from 'stream';

// eslint-disable-next-line @typescript-eslint/no-require-imports
const Freshdesk = require('freshdesk-api');

export enum TicketType {
    APPROVED = 'Approved',
    QUOTE_CHANGE = 'Quote change',
    NEW_BUSINESS = 'New Business',
    DECLINED = 'Declined',
}

export enum TicketStatus {
    OPEN = 2,
    PENDING = 3,
    RESOLVED = 4,
    CLOSED = 5,
}

export enum TicketPriority {
    LOW = 1,
    MEDIUM = 2,
    HIGH = 3,
    URGENT = 4,
}

// Freshdesk API interfaces
export interface FreshdeskError {
    status?: number;
    data?: {
        errors?: Array<{
            code: string;
            additional_info?: {
                company_id?: number;
                user_id?: number;
            };
        }>;
    };
    message?: string;
}

export interface FreshdeskCompanyResponse {
    id: number;
    name: string;
}

export interface FreshdeskContactResponse {
    id: number;
    name: string;
    email: string;
    company_id: number;
}

export interface FreshdeskTicketResponse {
    id: number;
    subject: string;
    description: string;
    status: number;
    priority: number;
}

export interface FreshdeskAgent {
    id: string;
    email: string;
    name: string;
}

export interface TicketAttachment extends Partial<Express.Multer.File> {
    objectType?: string;
}

export interface CreateTicketData {
    requester_id: number;
    type: string;
    subject: string;
    status: number;
    priority: number;
    description: string;
    tags: string[];
    responder_id?: string;
    custom_fields?: {
        cf_amp_id?: string;
        cf_quote_id?: string;
        cf_customer?: string;
    };
    attachments?: (Readable | TicketAttachment)[];
}

export class TicketDto {
    contactId: number;
    subject: string;
    ticketType: TicketType;
    ticketStatus: TicketStatus;
    ticketPriority: number;
    description: string;
    attachments: TicketAttachment[] = [];
    tags: string[] = [];
    ampId?: string;
    quoteId?: string;
    responderId?: string;
    customerName?: string;
}

export class ContactDto {
    name: string;
    email: string;
    companyId: number;
}

export class CompanyDto {
    name: string;
}

export type ExpressMulterFile = Express.Multer.File & { objectType: string };

@Injectable()
export class FreshdeskService {
    private readonly logger = new Logger(FreshdeskService.name);
    private client: FreshdeskType;
    private environment: string;

    constructor(private configService: ConfigService) {
        const host = this.configService.get<string>('FRESHDESK_DOMAIN');
        const apiKey = this.configService.get<string>('FRESHDESK_API_KEY');

        this.client = new Freshdesk(`https://${host}.freshdesk.com`, apiKey);
        this.environment = this.configService.get<string>('NODE_ENV');

        this.logger.log('Freshdesk service initialized', {
            host: `${host}.freshdesk.com`,
            environment: this.environment,
            hasApiKey: !!apiKey,
        });
    }

    async createCompany(data: CompanyDto): Promise<number> {
        this.logger.log('Creating company in Freshdesk', {
            companyName: data.name,
            environment: this.environment,
        });

        return new Promise((resolve, reject) => {
            this.client.createCompany(data, (err: FreshdeskError | null, response: FreshdeskCompanyResponse) => {
                if (err) {
                    // Handle duplicate company error
                    if (err.status === 409 && err.data?.errors?.[0]) {
                        const error = err.data.errors[0];

                        if (error.code === 'duplicate_value' && error.additional_info?.company_id) {
                            this.logger.log('Company already exists in Freshdesk, using existing ID', {
                                companyName: data.name,
                                existingCompanyId: error.additional_info.company_id,
                                environment: this.environment,
                            });
                            resolve(error.additional_info.company_id);
                            return;
                        }
                    }

                    // Handle other API errors
                    this.logger.error('Failed to create company in Freshdesk', {
                        companyName: data.name,
                        environment: this.environment,
                        error: err.data?.errors || err.message || 'Unknown error',
                        status: err.status,
                    });

                    if (err.data?.errors) {
                        reject(new Error(`Freshdesk API error: ${JSON.stringify(err.data.errors)}`));
                        return;
                    }

                    reject(err);
                    return;
                }

                if (response?.id) {
                    this.logger.log('Company created successfully in Freshdesk', {
                        companyName: data.name,
                        companyId: response.id,
                        environment: this.environment,
                    });
                    resolve(response.id);
                    return;
                }

                this.logger.error('Unexpected response from Freshdesk company creation', {
                    companyName: data.name,
                    response,
                    environment: this.environment,
                });
                reject(new Error('Unknown Freshdesk API error'));
            });
        });
    }

    async createContact(data: ContactDto): Promise<number> {
        this.logger.log('Creating contact in Freshdesk', {
            contactName: data.name,
            contactEmail: data.email,
            companyId: data.companyId,
            environment: this.environment,
        });

        const item = {
            name: data.name,
            email: data.email,
            company_id: data.companyId,
        };

        return new Promise((resolve, reject) => {
            this.client.createContact(item, (err: FreshdeskError | null, response: FreshdeskContactResponse) => {
                if (err) {
                    // Handle duplicate contact error
                    if (err.status === 409 && err.data?.errors?.[0]) {
                        const error = err.data.errors[0];

                        if (error.code === 'duplicate_value' && error.additional_info?.user_id) {
                            this.logger.log('Contact already exists in Freshdesk, using existing ID', {
                                contactName: data.name,
                                contactEmail: data.email,
                                existingContactId: error.additional_info.user_id,
                                environment: this.environment,
                            });
                            resolve(error.additional_info.user_id);
                            return;
                        }
                    }

                    // Handle other API errors
                    this.logger.error('Failed to create contact in Freshdesk', {
                        contactName: data.name,
                        contactEmail: data.email,
                        companyId: data.companyId,
                        environment: this.environment,
                        error: err.data?.errors || err.message || 'Unknown error',
                        status: err.status,
                    });

                    if (err.data?.errors) {
                        reject(new Error(`Freshdesk API error: ${JSON.stringify(err.data.errors)}`));
                        return;
                    }

                    reject(err);
                    return;
                }

                if (response?.id) {
                    this.logger.log('Contact created successfully in Freshdesk', {
                        contactName: data.name,
                        contactEmail: data.email,
                        contactId: response.id,
                        companyId: data.companyId,
                        environment: this.environment,
                    });
                    resolve(response.id);
                    return;
                }

                this.logger.error('Unexpected response from Freshdesk contact creation', {
                    contactName: data.name,
                    contactEmail: data.email,
                    companyId: data.companyId,
                    response,
                    environment: this.environment,
                });
                reject(new Error('Unknown Freshdesk API error'));
            });
        });
    }

    async createTicket(ticketData: TicketDto): Promise<number> {
        this.logger.log('Creating ticket in Freshdesk', {
            subject: ticketData.subject,
            ticketType: ticketData.ticketType,
            contactId: ticketData.contactId,
            priority: ticketData.ticketPriority,
            status: ticketData.ticketStatus,
            hasAttachments: ticketData.attachments.length > 0,
            attachmentCount: ticketData.attachments.length,
            tags: ticketData.tags,
            ampId: ticketData.ampId,
            quoteId: ticketData.quoteId,
            customerName: ticketData.customerName,
            environment: this.environment,
        });

        return new Promise<number>(async (resolve, reject) => {
            // Convert data
            const data: CreateTicketData = {
                requester_id: ticketData.contactId,
                type: ticketData.ticketType.valueOf(),
                subject: ticketData.subject,
                status: ticketData.ticketStatus.valueOf(),
                priority: ticketData.ticketPriority,
                description: ticketData.description,
                tags: ticketData.tags,
            };

            if (ticketData.responderId) {
                data.responder_id = ticketData.responderId;
                this.logger.log('Assigning responder to ticket', {
                    responderId: ticketData.responderId,
                    subject: ticketData.subject,
                    environment: this.environment,
                });
            }

            if (ticketData.ampId || ticketData.quoteId || ticketData.customerName) {
                data.custom_fields = {};

                if (ticketData.ampId) {
                    data.custom_fields.cf_amp_id = ticketData.ampId;
                }
                if (ticketData.quoteId) {
                    data.custom_fields.cf_quote_id = ticketData.quoteId;
                }
                if (ticketData.customerName) {
                    data.custom_fields.cf_customer = ticketData.customerName;
                }

                this.logger.log('Adding custom fields to ticket', {
                    customFields: data.custom_fields,
                    subject: ticketData.subject,
                    environment: this.environment,
                });
            }

            if (ticketData.attachments.length > 0) {
                this.logger.log('Processing ticket attachments', {
                    attachmentCount: ticketData.attachments.length,
                    subject: ticketData.subject,
                    environment: this.environment,
                });

                data.attachments = [];
                for (const [index, attachment] of ticketData.attachments.entries()) {
                    try {
                        if (attachment.objectType == 'ExpressMulterFile') {
                            const readStream = this.createReadableStream(attachment as Express.Multer.File);
                            data.attachments.push(readStream);
                            this.logger.log('Processed attachment as readable stream', {
                                index,
                                filename: (attachment as Express.Multer.File).originalname,
                                size: (attachment as Express.Multer.File).size,
                                subject: ticketData.subject,
                                environment: this.environment,
                            });
                        } else {
                            data.attachments.push(attachment);
                            this.logger.log('Added attachment directly', {
                                index,
                                attachmentType: attachment.objectType || 'unknown',
                                subject: ticketData.subject,
                                environment: this.environment,
                            });
                        }
                    } catch (attachmentError) {
                        this.logger.error('Failed to process attachment', {
                            index,
                            error: attachmentError,
                            subject: ticketData.subject,
                            environment: this.environment,
                        });
                        // Continue with other attachments
                    }
                }
            }

            this.client.createTicket(data, (err: FreshdeskError | null, ticket: FreshdeskTicketResponse) => {
                if (err) {
                    // Handle other API errors
                    this.logger.error('Failed to create ticket in Freshdesk', {
                        subject: ticketData.subject,
                        ticketType: ticketData.ticketType,
                        contactId: ticketData.contactId,
                        environment: this.environment,
                        error: err.data?.errors || err.message || 'Unknown error',
                        status: err.status,
                    });

                    if (err.data?.errors) {
                        reject(new Error(`Freshdesk API error: ${JSON.stringify(err.data.errors)}`));
                        return;
                    }

                    reject(err);
                    return;
                }

                if (ticket?.id) {
                    this.logger.log('Ticket created successfully in Freshdesk', {
                        ticketId: ticket.id,
                        subject: ticketData.subject,
                        ticketType: ticketData.ticketType,
                        contactId: ticketData.contactId,
                        priority: ticketData.ticketPriority,
                        status: ticketData.ticketStatus,
                        environment: this.environment,
                    });
                    resolve(ticket.id);
                    return;
                }

                this.logger.error('Unexpected response from Freshdesk ticket creation', {
                    subject: ticketData.subject,
                    contactId: ticketData.contactId,
                    response: ticket,
                    environment: this.environment,
                });
                reject(new Error('Unknown Freshdesk API error'));
            });
        }).catch((err) => {
            this.logger.error('Exception during ticket creation', {
                subject: ticketData.subject,
                contactId: ticketData.contactId,
                error: err.message || err,
                environment: this.environment,
            });
            throw err;
        });
    }

    public getCapitalizedEnvironment(): string {
        if (!this.environment) {
            this.logger.debug('No environment configured, returning empty string');
            return '';
        }
        const capitalized = this.environment.charAt(0).toUpperCase() + this.environment.slice(1).toLowerCase();
        this.logger.debug('Capitalized environment', {
            original: this.environment,
            capitalized,
        });
        return capitalized;
    }

    private createReadableStream(file: Express.Multer.File): Readable {
        this.logger.log('Creating readable stream from file', {
            filename: file.originalname,
            size: file.size,
            mimetype: file.mimetype,
            environment: this.environment,
        });

        // Create a proper Readable stream with full event implementation
        const readableStream = new Readable({
            read() {}, // Empty read function since we'll push all data at once
        });

        readableStream.push(file.buffer);
        readableStream.push(null); // Signal end of stream

        // Add filename and path properties that form-data might expect
        Object.assign(readableStream, {
            path: file.originalname,
            name: file.originalname,
            filename: file.originalname,
        });

        this.logger.log('Readable stream created successfully', {
            filename: file.originalname,
            streamProps: {
                path: file.originalname,
                name: file.originalname,
                filename: file.originalname,
            },
            environment: this.environment,
        });

        return readableStream;
    }

    async findAgentIdByEmail(email: string): Promise<string | null> {
        this.logger.log('Finding Freshdesk agent by email', {
            email,
            environment: this.environment,
        });

        return new Promise((resolve, reject) => {
            this.client.listAllAgents({ email }, (err: FreshdeskError | null, agents: FreshdeskAgent[]) => {
                if (err) {
                    // Handle API errors
                    this.logger.error('Failed to find agent in Freshdesk', {
                        email,
                        environment: this.environment,
                        error: err.data?.errors || err.message || 'Unknown error',
                        status: err.status,
                    });

                    if (err.data?.errors) {
                        reject(new Error(`Freshdesk API error: ${JSON.stringify(err.data.errors)}`));
                        return;
                    }
                    reject(err);
                    return;
                }

                if (!agents || agents.length === 0) {
                    this.logger.log('No agent found with specified email', {
                        email,
                        environment: this.environment,
                    });
                    resolve(null);
                    return;
                }

                // Get the first agent that matches the email
                const agent = agents[0];
                this.logger.log('Agent found successfully', {
                    email,
                    agentId: agent.id,
                    agentName: agent.name,
                    totalMatches: agents.length,
                    environment: this.environment,
                });

                if (agents.length > 1) {
                    this.logger.warn('Multiple agents found with same email, using first one', {
                        email,
                        totalMatches: agents.length,
                        selectedAgentId: agent.id,
                        environment: this.environment,
                    });
                }

                resolve(agent.id);
            });
        });
    }
}
