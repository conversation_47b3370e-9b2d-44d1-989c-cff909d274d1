import * as Brevo from '@getbrevo/brevo';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { SendProps } from '../../interfaces/send-email-props.interface';
import { EmailTemplateRepository } from '../../repositories';
import { EmailService } from './email.service';

@Injectable()
export class BrevoService extends EmailService {
    private readonly logger = new Logger(BrevoService.name);
    private readonly contactsApi: Brevo.ContactsApi;
    private transactionalEmailsApi: Brevo.TransactionalEmailsApi;

    constructor(
        private readonly configService: ConfigService,
        private readonly emailTemplateRepository: EmailTemplateRepository,
    ) {
        super();
        this.contactsApi = new Brevo.ContactsApi();
        this.contactsApi.setApiKey(Brevo.ContactsApiApiKeys.apiKey, this.configService.get<string>('BREVO_API_KEY'));

        this.transactionalEmailsApi = new Brevo.TransactionalEmailsApi();
        this.transactionalEmailsApi.setApiKey(
            Brevo.TransactionalEmailsApiApiKeys.apiKey,
            this.configService.get<string>('BREVO_API_KEY'),
        );
    }

    async send(props: SendProps): Promise<void> {
        const environment = this.configService.get<string>('NODE_ENV');
        const isTest = ['local', 'test', 'github', 'dev', 'staging'].includes(environment);

        this.logger.log('Email send request started', {
            templateName: props.templateName,
            locale: props.locale,
            environment,
            isTest,
            recipientCount: props.toEmails.length,
            ccCount: props.ccEmails?.length || 0,
            bccCount: props.bccEmails?.length || 0,
            hasSubject: !!props.subject,
            hasParams: Object.keys(props.params).length > 0,
            tags: props.tags,
        });

        let toEmails: { email: string }[] = [];
        let ccEmails: { email: string }[] = [];
        let bccEmails: { email: string }[] = [];

        if (isTest) {
            const testEmail = this.configService.get<string>('BREVO_TEST_EMAIL');

            this.logger.log('Test environment: overriding email recipients', {
                originalToEmails: props.toEmails,
                originalCcEmails: props.ccEmails,
                testEmail,
                templateName: props.templateName,
            });

            toEmails.push({ email: testEmail });
            ccEmails = [];
            bccEmails = [];
        } else {
            toEmails = props.toEmails.map((email) => ({ email }));
            ccEmails = props.ccEmails?.map((email) => ({ email })) ?? [];
            bccEmails = props.bccEmails?.map((email) => ({ email })) ?? [];
        }

        const sendSmtpEmail = new Brevo.SendSmtpEmail();

        sendSmtpEmail.to = toEmails;
        if (ccEmails.length) sendSmtpEmail.cc = ccEmails;
        if (bccEmails.length) sendSmtpEmail.bcc = bccEmails;

        this.logger.debug('Looking up email template', {
            templateName: props.templateName,
            locale: props.locale,
            provider: 'brevo',
        });

        let template = await this.emailTemplateRepository.findOne({
            provider: 'brevo',
            name: props.templateName,
            locale: props.locale,
        });

        if (!template) {
            this.logger.warn('Template not found for requested locale, falling back to default', {
                templateName: props.templateName,
                requestedLocale: props.locale,
                fallbackLocale: 'de',
            });

            template = await this.emailTemplateRepository.findOne({
                provider: 'brevo',
                name: props.templateName,
                locale: 'de',
            });

            if (!template) {
                this.logger.error('Template not found for any locale', {
                    templateName: props.templateName,
                    requestedLocale: props.locale,
                    fallbackLocale: 'de',
                });
                throw new Error(`Template ${props.templateName} not found`);
            }
        }

        this.logger.debug('Email template found', {
            templateName: props.templateName,
            templateId: template.providerId,
            locale: template.locale,
        });

        sendSmtpEmail.templateId = +template.providerId;
        if (Object.keys(props.params).length) sendSmtpEmail.params = props.params;
        if (props.subject) sendSmtpEmail.subject = props.subject;
        if (props.tags) sendSmtpEmail.tags = props.tags;

        try {
            this.logger.log('Sending email via Brevo API', {
                templateName: props.templateName,
                templateId: template.providerId,
                toCount: toEmails.length,
                ccCount: ccEmails.length,
                bccCount: bccEmails.length,
                environment,
            });

            const { body } = await this.transactionalEmailsApi.sendTransacEmail(sendSmtpEmail);

            this.logger.log('Email sent successfully', {
                templateName: props.templateName,
                templateId: template.providerId,
                messageId: body.messageId,
                recipientCount: toEmails.length,
                environment,
            });
        } catch (error) {
            this.logger.error('Failed to send email via Brevo API', {
                templateName: props.templateName,
                templateId: template?.providerId,
                toEmails: toEmails.map((e) => e.email),
                environment,
                error: error?.response?.body || error?.message || 'Unknown error',
                statusCode: error?.response?.status,
            });
            throw error;
        }
    }

    async getContact(identifier: string | number): Promise<Brevo.GetExtendedContactDetails | null> {
        this.logger.debug('Fetching contact from Brevo', { identifier });

        try {
            const { body } = await this.contactsApi.getContactInfo(identifier.toString());

            this.logger.debug('Contact fetched successfully', {
                identifier,
                email: body.email,
                id: body.id,
                emailBlacklisted: body.emailBlacklisted,
            });

            return body;
        } catch (error) {
            this.logger.error('Failed to fetch contact from Brevo', {
                identifier,
                error: error?.response?.body || error?.message || 'Unknown error',
                statusCode: error?.response?.status,
            });
            return null;
        }
    }

    async createContact(
        email: string,
        firstName?: string,
        lastName?: string,
        phone?: string,
        status?: string,
        locale?: string,
    ): Promise<Brevo.CreateUpdateContactModel | null> {
        this.logger.log('Creating contact in Brevo', {
            email,
            firstName,
            lastName,
            phone: phone ? '[REDACTED]' : undefined,
            status,
            locale,
        });

        const attributes = {
            ...(firstName ? { FIRSTNAME: firstName } : {}),
            ...(lastName ? { LASTNAME: lastName } : {}),
            ...(phone ? { SMS: phone } : {}),
            ...(status ? { STATUS: status } : {}),
            ...(locale ? { LANGUAGE: locale } : {}),
        };

        const data = {
            email,
            ...(attributes ? { attributes } : {}),
        };

        try {
            const res = await this.contactsApi.createContact(data);

            this.logger.log('Contact created successfully in Brevo', {
                email,
                contactId: res.body.id,
                attributesCount: Object.keys(attributes).length,
            });

            return res.body;
        } catch (error) {
            this.logger.error('Failed to create contact in Brevo', {
                email,
                firstName,
                lastName,
                status,
                error: error?.response?.body || error?.message || 'Unknown error',
                statusCode: error?.response?.status,
            });
            return null;
        }
    }

    async updateContact(
        identifier: string | number,
        firstName?: string,
        lastName?: string,
        phone?: string,
        status?: string,
        locale?: string,
    ): Promise<Brevo.GetExtendedContactDetails | null> {
        this.logger.log('Updating contact in Brevo', {
            identifier,
            firstName,
            lastName,
            phone: phone ? '[REDACTED]' : undefined,
            status,
            locale,
        });

        const attributes = {
            ...(firstName ? { FIRSTNAME: firstName } : {}),
            ...(lastName ? { LASTNAME: lastName } : {}),
            ...(phone ? { SMS: phone } : {}),
            ...(status ? { STATUS: status } : {}),
            ...(locale ? { LANGUAGE: locale } : {}),
        };

        const data = {
            ...(attributes ? { attributes } : {}),
        };

        try {
            await this.contactsApi.updateContact(identifier.toString(), data);

            this.logger.log('Contact updated successfully in Brevo', {
                identifier,
                attributesUpdated: Object.keys(attributes),
            });

            return await this.getContact(identifier);
        } catch (error) {
            this.logger.error('Failed to update contact in Brevo', {
                identifier,
                firstName,
                lastName,
                locale,
                contactStatus: status,
                error: error?.response?.body || error?.message || 'Unknown error',
                statusCode: error?.response?.status,
            });
            return null;
        }
    }
}
