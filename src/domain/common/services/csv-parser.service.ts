import { Injectable } from '@nestjs/common';
import { parse } from 'csv-parse/sync';

import { IFileParser } from '../interfaces/file-parser.interface';

@Injectable()
export class CsvParserService implements IFileParser {
    toJson(file: Express.Multer.File): Record<string, any>[] {
        const { buffer } = file;
        const content = buffer.toString('utf-8');
        return parse(content, {
            columns: true,
            skip_empty_lines: true,
            trim: true,
        });
    }

    toMatrix(file: Express.Multer.File): any[][] {
        const { buffer } = file;
        const content = buffer.toString('utf-8');

        return parse(content, {
            columns: false,
            skip_empty_lines: true,
            trim: true,
        });
    }
}
