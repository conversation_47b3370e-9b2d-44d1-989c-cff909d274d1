import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

export const ErrorSchema = extendApi(
    z.object({
        message: extendApi(z.string(), {
            description: 'Error description',
        }),

        error: extendApi(z.string().optional(), {
            description: 'Error message',
        }),

        statusCode: extendApi(z.number(), {
            description: 'HTTP status code',
        }),

        errorCode: extendApi(z.string().optional(), {
            description: 'Error code for programmatic handling',
        }),
        errors: extendApi(z.any().array().nullable().optional()),
    }),
    {
        title: 'Error',
        description: 'Error object',
    },
);
