import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

export const AddressSchema = extendApi(
    z.object({
        address: extendApi(z.string().nullable().optional(), {
            description: 'Address',
        }),
        city: extendApi(z.string().nullable().optional(), {
            description: 'City',
        }),
        zip: extendApi(z.string().nullable().optional(), {
            description: 'Zip Code',
        }),
        country: extendApi(z.string().nullable().optional(), {
            description: 'Country',
        }),
    }),
);
