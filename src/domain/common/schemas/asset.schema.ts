import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { ContractItemSchema } from '../../contracts/schemas/contract-item.schema';
import { ContractItemApiSchema } from '../../contracts/schemas/contract-item-api.schema';
import { QuoteItemApiSchema } from '../../quotes/schemas/quote-item-api.schema';
import { CurrencyEnum } from '../enums';

export const AssetSchema = extendApi(
    ContractItemSchema.pick({
        serviceGroupSku: true,
        serviceGroupLabel: true,
        serviceLevelSku: true,
        productSku: true,
        quantity: true,
    })
        .extend({
            _links: ContractItemSchema.shape._links.optional().nullable(),
        })
        .setKey(
            'name',
            extendApi(z.string().nullable().optional(), {
                description: 'Product Name',
            }),
        )
        .setKey(
            'vendorId',
            extendApi(z.string().nullable().optional(), {
                description: 'Vendor',
            }),
        )
        .setKey('serialNumber', ContractItemSchema.shape.serialNo)
        .setKey(
            'currency',
            extendApi(z.nativeEnum(CurrencyEnum).nullable().optional(), {
                description: 'Currency code',
            }),
        )
        .setKey(
            'coverageStatus',
            extendApi(z.string().nullable().optional(), {
                description: 'Coverage Status',
            }),
        )
        .setKey(
            'itemsCount',
            extendApi(z.number().nullable().optional(), {
                description: 'Items Count',
            }),
        )
        .setKey('resellerPriceFinalSum', z.number().nullable().optional().describe('Reseller Price Sum'))
        // .setKey('distributorPriceFinalSum', z.number().nullable().optional().describe('Distributor Price Sum')) // Hidden because of AH-101
        .setKey('endCustomerPriceFinalSum', z.number().nullable().optional().describe('Reseller Price Sum'))
        .setKey(
            'items',
            extendApi(
                z
                    .array(z.union([ContractItemApiSchema, QuoteItemApiSchema]))
                    .nullable()
                    .optional(),
                {
                    description: 'Items',
                },
            ),
        )
        .setKey('startDate', z.string().datetime({ offset: true }).optional().nullable().describe('Start Date'))
        .setKey('endDate', z.string().datetime({ offset: true }).optional().nullable().describe('End Date'))
        .setKey(
            'supportLifeEndDate',
            z.string().datetime({ offset: true }).optional().nullable().describe('End OfService Life'),
        ),
    {
        title: 'Asset',
        description: 'Asset model',
    },
);
