import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

export const ServiceRoleSchema = extendApi(
    z
        .object({
            userId: z.string().nullable().optional(),
            serviceName: z.string().nullable().optional(),
            serviceDetails: z.string().nullable().optional(),
        })
        .refine(
            ({ userId, serviceName, serviceDetails }) =>
                (userId && !serviceName && !serviceDetails) || (!userId && serviceName && serviceDetails),
            {
                message:
                    'Either userId must be provided, or both serviceName and serviceDetails must be provided, but not a mix of both.',
                path: ['userId', 'serviceName', 'serviceDetails'], // Affects all three fields
            },
        ),

    {
        title: 'ServiceRole',
        description: 'Service Role model',
    },
);

export class ServiceRoleDto extends createZodDto(ServiceRoleSchema) {}
