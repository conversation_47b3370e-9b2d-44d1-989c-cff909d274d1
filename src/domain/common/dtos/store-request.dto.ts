import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

export const StoreRequestSchema = extendApi(
    z.object({
        file: extendApi(
            z
                .custom<Express.Multer.File>(
                    (file) => {
                        // Check if file exists and is an object
                        if (!file || typeof file !== 'object') return false;
                    },
                    {
                        path: ['attachPo'],
                        message: 'Invalid file type',
                    },
                )
                .describe('File'),
            {
                type: 'string',
                format: 'file',
            },
        ),
        path: z.string().optional().nullable().describe('Path to file'),
        useFilename: z.boolean().optional().default(false).describe('If file name should be used as name of file'),
        bucket: z.string().describe('Bucke name'),
        region: z.string().optional().nullable().default('us-east-1').describe('File region'),
    }),
    {
        title: 'StoreRequest',
        description: 'Store Request model',
    },
);

export class StoreRequestDto extends createZodDto(StoreRequestSchema) {}
