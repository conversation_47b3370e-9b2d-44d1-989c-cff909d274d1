import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { AssetModel } from '../models/asset.model';
import { PaginationMetaModel } from '../pagination/pagination.model';
import { AssetListSchema } from '../schemas/asset-list.schema';

const AssetGlobalSearchListSchema = extendApi(
    z.array(
        z.object({
            isCovered: z.boolean().optional(),
            serialNumber: z.string().optional(),
            endDate: z.string().optional(),
            productSku: z.string().optional(),
            endUser: z
                .object({
                    id: z.string().optional(),
                    name: z.string().optional(),
                })
                .optional(),
            product: z
                .object({
                    description: z.string().optional(),
                })
                .optional(),
        }),
    ),
    {
        title: 'AssetGlobalSearchList',
        description: 'Asset global search list dto',
    },
);

export class AssetGlobalSearchListDto extends createZodDto(AssetGlobalSearchListSchema) {
    zodSchema = AssetListSchema;

    private data: AssetModel[];
    private meta?: PaginationMetaModel;

    public setData(data: AssetModel[]) {
        this.data = data;
    }

    public setMeta(meta: PaginationMetaModel) {
        this.meta = meta;
    }

    public getData() {
        return this.zodSchema.parse(this.data);
    }

    public getMeta() {
        return this.meta;
    }
}
