import { forwardRef, Global, Module, Provider } from '@nestjs/common';
import { ConditionalModule, ConfigModule, ConfigService } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { makeCounterProvider, makeHistogramProvider } from '@willsoto/nestjs-prometheus';
import { MinioModule, MinioService } from 'nestjs-minio-client';

import { HttpMetricsInterceptor } from '../../common/interceptors/http-metrics.interceptor';
import { DrizzleModule } from '../../drizzle/drizzle.module';
import { EntitiesModule } from '../entities/entities.module';
import { ProductsModule } from '../products/products.module';
import { HealthController } from './controllers/health.controller';
import { EmailTemplateRepository } from './repositories';
import {
    AzureStorageService,
    BrevoService,
    EmailService,
    EventsService,
    ExportService,
    FreshdeskService,
    MinioStorageService,
    ObjectStorageService,
    TemplateService,
} from './services';

const providers: Provider[] = [
    HttpMetricsInterceptor,
    EventsService,
    ExportService,
    FreshdeskService,
    TemplateService,
    BrevoService,
    EmailTemplateRepository,
    makeCounterProvider({
        name: 'http_requests_total',
        help: 'Total number of HTTP requests',
        labelNames: ['method', 'path', 'status_code', 'status_class'],
    }),
    makeHistogramProvider({
        name: 'http_request_duration_seconds',
        help: 'HTTP request latencies in seconds',
        labelNames: ['method', 'path', 'status_code', 'status_class'],
        buckets: [0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10],
    }),
    makeCounterProvider({
        name: 'http_request_errors_total',
        help: 'Total number of HTTP request errors',
        labelNames: ['method', 'path', 'status_code', 'error_type'],
    }),
    {
        provide: EmailService,
        useFactory: (configService: ConfigService, emailTemplateRepository: EmailTemplateRepository) =>
            new BrevoService(configService, emailTemplateRepository),
        inject: [ConfigService, EmailTemplateRepository],
    },
    {
        provide: ObjectStorageService,
        useFactory: (configService: ConfigService, minioService: MinioService) => {
            const storageProvider = configService.get<string>('OBJECT_STORAGE_PROVIDER');
            if (!storageProvider) {
                return null;
            }

            switch (storageProvider) {
                case 'minio':
                    if (!minioService) {
                        throw new Error('MinioService is required for minio storage provider');
                    }
                    return new MinioStorageService(configService, minioService);
                case 'azure':
                    return new AzureStorageService(configService);
                default:
                    throw new Error(`Unsupported storage type: ${storageProvider}`);
            }
        },
        inject: [ConfigService, { token: MinioService, optional: true }],
    },
];

@Global()
@Module({
    imports: [
        ConfigModule,
        DrizzleModule,
        ProductsModule,
        EventEmitterModule.forRoot(),
        forwardRef(() => EntitiesModule),
        ConditionalModule.registerWhen(
            MinioModule.registerAsync({
                imports: [ConfigModule],
                inject: [ConfigService],
                useFactory: async (configService: ConfigService) => ({
                    endPoint: configService.getOrThrow('MINIO_ENDPOINT'),
                    port: Number(configService.getOrThrow('MINIO_PORT')),
                    useSSL: configService.getOrThrow('MINIO_SSL') === 'true',
                    accessKey: configService.getOrThrow('MINIO_ACCESS_KEY'),
                    secretKey: configService.getOrThrow('MINIO_SECRET_KEY'),
                    isGlobal: true,
                }),
            }),
            (env: NodeJS.ProcessEnv) => !!env['OBJECT_STORAGE_PROVIDER'] && env['OBJECT_STORAGE_PROVIDER'] === 'minio',
        ),
    ],
    controllers: [HealthController],
    providers: providers,
    exports: providers,
})
export class CommonModule {}
