import { EventActionEnum } from '../enums/event-actions.enum';
import { EventNameEnum } from '../enums/event-names.enum';

export interface DbChangeField {
    old: unknown;
    new: unknown;
}

export interface DbChanges {
    type: 'created' | 'updated' | 'deleted';
    data?: unknown;
    changed_fields?: Record<string, DbChangeField>;
}

export interface GenericDbEvent {
    eventName: EventNameEnum;
    changes: DbChanges;
    action: EventActionEnum;
    entity: string;
    entityId: string;
    timestamp: Date;
}
