import { Permission } from '../../users/entities/permission.entity';
import { User } from '../../users/entities/user.entity';

export interface Context {
    user: User;
    organizationId: string;

    // @deprecated Use `resellerIds` instead
    entityId?: string;

    hasAdvancedPermissions: boolean;
    resellerIds?: string[];
    endUserIds?: string[] | null; // If NULL - advanced permissioning is disabled for organization
    permissions: Permission[];
}
