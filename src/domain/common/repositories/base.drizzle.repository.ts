import { Logger } from '@nestjs/common';
import { and, asc, desc, eq, gt, isNotNull, isNull, lt, or, sql, SQLWrapper } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { PgSelect, SelectedFields } from 'drizzle-orm/pg-core';

import * as schema from '../../../../drizzle/schema';
import {
    BaseFilterModel,
    PaginatedResponseModel,
    PaginationMetaModel,
    PaginationParamsModel,
} from '../pagination/pagination.model';

const DEFAULT_PAGINATION_LIMIT = parseInt(process.env.PAGINATION_DEFAULT_LIMIT, 10) || 50;
const MAX_PAGINATION_LIMIT = parseInt(process.env.PAGINATION_MAX_LIMIT, 10) || 100;

export type TableWithId = { id: string } & Record<string, any>;

/**
 * Base repository for Drizzle ORM
 *
 * NOTE: This implementation requires adjustments based on the actual Drizzle schema.
 * The type errors are related to the specific structure of your schema tables.
 * You will need to adjust the query building based on your actual schema definitions.
 */
export abstract class BaseDrizzleRepository<T extends TableWithId> {
    protected readonly logger = new Logger(this.constructor.name);

    constructor(
        protected db: NodePgDatabase<typeof schema>,
        protected table: any,
    ) {}

    protected applyPaginationCursor(
        fields: any,
        cursor: string | undefined,
        sortField: string,
        sortOrder: 'asc' | 'desc',
    ): SQLWrapper[] {
        if (!cursor) {
            return [];
        }

        try {
            // Parse the cursor format: value:id or just id
            const lastColonIndex = cursor.lastIndexOf(':');
            let cursorValue: string | null;
            let cursorId: string;

            if (lastColonIndex !== -1) {
                const rawValue = cursor.substring(0, lastColonIndex);
                cursorValue = rawValue === 'null' ? null : rawValue;
                cursorId = cursor.substring(lastColonIndex + 1);
            } else {
                cursorId = cursor;
                cursorValue = cursor === 'null' ? null : cursor; // Will only be used if sortField === 'id'
            }

            // If sorting by ID only
            if (sortField === 'id') {
                return [sortOrder === 'asc' ? gt(this.table.id, cursorId) : lt(this.table.id, cursorId)];
            }

            // If sorting by another field (with ID as tie-breaker)
            const fieldRef = this.table[sortField] || fields[sortField];
            const idRef = this.table.id;

            // Handle null values in cursor
            if (cursorValue === null) {
                // When cursor value is null, we need to handle pagination differently
                // depending on sort order and NULL positioning
                if (sortOrder === 'asc') {
                    // ASC with NULLS FIRST: after null, we want non-null values or same null with higher ID
                    return [
                        or(
                            isNotNull(fieldRef), // All non-null values come after null
                            and(isNull(fieldRef), gt(idRef, cursorId)), // Same null value with higher ID
                        ),
                    ];
                } else {
                    // DESC with NULLS LAST: after null, we want non-null values or same null with lower ID
                    return [
                        or(
                            isNotNull(fieldRef), // All non-null values come before null in DESC
                            and(isNull(fieldRef), lt(idRef, cursorId)), // Same null value with lower ID
                        ),
                    ];
                }
            }

            return [
                or(
                    sortOrder === 'asc' ? gt(fieldRef, cursorValue) : lt(fieldRef, cursorValue),
                    and(eq(fieldRef, cursorValue), sortOrder === 'asc' ? gt(idRef, cursorId) : lt(idRef, cursorId)),
                ),
            ];
        } catch (error) {
            this.logger.warn('Invalid cursor format, ignoring cursor', {
                cursor,
                sortField,
                sortOrder,
                error: error instanceof Error ? error.message : 'Unknown error',
            });
            return [];
        }
    }

    /**
     * Find entities with pagination
     *
     * NOTE: This implementation might need adjustments based on your actual
     * Drizzle schema definition and the types it uses. The current implementation
     * assumes generic SQL operations that might need to be adapted to your
     * specific schema structure.
     */
    async findPaginated(
        whereConditions: SQLWrapper[] = [],
        paginationParams: PaginationParamsModel,
        filter?: BaseFilterModel,
        fields?: SelectedFields,
        queryModifier?: (query: PgSelect) => void,
    ): Promise<PaginatedResponseModel<T>> {
        const startTime = Date.now();
        const limit = paginationParams?.limit || DEFAULT_PAGINATION_LIMIT;
        const effectiveLimit = Math.min(limit, MAX_PAGINATION_LIMIT);
        const queryLimit = effectiveLimit + 1;

        const sortField = filter?.sortBy || 'id';
        const sortOrder = filter?.sortOrder || 'asc';

        this.logger.debug('Database paginated query started', {
            repository: this.constructor.name,
            requestedLimit: limit,
            effectiveLimit,
            sortField,
            sortOrder,
            cursor: paginationParams?.cursor,
            whereConditionsCount: whereConditions.length,
            hasQueryModifier: !!queryModifier,
            hasCustomFields: !!fields,
        });

        try {
            // Handle cursor-based pagination
            const cursorConditions = this.applyPaginationCursor(fields, paginationParams?.cursor, sortField, sortOrder);

            // Combine all where conditions
            const allConditions = [...whereConditions, ...cursorConditions];

            // Build query with method chaining
            const fieldRef = this.table[sortField] || fields[sortField];
            const orderByField =
                sortOrder === 'asc' ? sql`${fieldRef} ASC NULLS FIRST` : sql`${fieldRef} DESC NULLS LAST`;
            const orderById = sortOrder === 'asc' ? asc(this.table.id) : desc(this.table.id);

            const query = this.db
                .select(fields ? fields : undefined)
                .from(this.table)
                .where(allConditions.length > 0 ? and(...allConditions) : undefined)
                .orderBy(orderByField, orderById)
                .limit(queryLimit)
                .$dynamic();

            if (queryModifier) {
                queryModifier(query);
            }

            // Execute query with chaining instead of reassignment
            const items = (await query) as T[];

            // Check if there's a next page
            const hasNextPage = items.length > effectiveLimit;
            const data = hasNextPage ? items.slice(0, effectiveLimit) : items;

            // Create next cursor
            let nextCursor = null;
            if (hasNextPage && data.length > 0) {
                const lastItem = data[data.length - 1];
                if (sortField === 'id') {
                    nextCursor = lastItem.id;
                } else {
                    let value = lastItem[sortField];
                    if (value instanceof Date) {
                        value = value.toISOString();
                    }
                    nextCursor = `${value}:${lastItem.id}`;
                }
            }

            const duration = Date.now() - startTime;

            this.logger.debug('Database paginated query completed successfully', {
                repository: this.constructor.name,
                resultCount: data.length,
                hasNextPage,
                nextCursor,
                queryDurationMs: duration,
                effectiveLimit,
                sortField,
                sortOrder,
            });

            return new PaginatedResponseModel<T>(data, new PaginationMetaModel(hasNextPage, nextCursor));
        } catch (error) {
            const duration = Date.now() - startTime;

            this.logger.error('Database paginated query failed', {
                repository: this.constructor.name,
                requestedLimit: limit,
                effectiveLimit,
                sortField,
                sortOrder,
                cursor: paginationParams?.cursor,
                whereConditionsCount: whereConditions.length,
                queryDurationMs: duration,
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined,
            });
            throw error;
        }
    }
}
