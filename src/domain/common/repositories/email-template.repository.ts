import { Inject, Injectable } from '@nestjs/common';
import { and, eq } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

import * as schema from '../../../../drizzle/schema';
import { DRIZZLE_PROVIDER } from '../../../drizzle/drizzle.provider';
import { BaseDrizzleRepository } from '../../common/repositories/base.drizzle.repository';
import { EmailTemplate } from '../entities/email-template.entity';

@Injectable()
export class EmailTemplateRepository extends BaseDrizzleRepository<EmailTemplate> {
    constructor(
        @Inject(DRIZZLE_PROVIDER)
        db: NodePgDatabase<typeof schema>,
    ) {
        super(db, schema.emailTemplates);
    }

    private localeCodes = new Set([
        'af',
        'am',
        'ar',
        'az',
        'be',
        'bg',
        'bn',
        'bs',
        'ca',
        'ceb',
        'co',
        'cs',
        'cy',
        'da',
        'de',
        'dv',
        'el',
        'en',
        'eo',
        'es',
        'et',
        'eu',
        'fa',
        'fi',
        'fr',
        'fy',
        'ga',
        'gd',
        'gl',
        'gu',
        'ha',
        'haw',
        'he',
        'hi',
        'hmn',
        'hr',
        'ht',
        'hu',
        'hy',
        'id',
        'ig',
        'is',
        'it',
        'ja',
        'jw',
        'ka',
        'kk',
        'km',
        'kn',
        'ko',
        'ku',
        'ky',
        'la',
        'lb',
        'lo',
        'lt',
        'lv',
        'mg',
        'mi',
        'mk',
        'ml',
        'mn',
        'mr',
        'ms',
        'mt',
        'my',
        'ne',
        'nl',
        'no',
        'ny',
        'pa',
        'pl',
        'ps',
        'pt',
        'ro',
        'ru',
        'rw',
        'sd',
        'si',
        'sk',
        'sl',
        'sm',
        'sn',
        'so',
        'sq',
        'sr',
        'st',
        'su',
        'sv',
        'sw',
        'ta',
        'te',
        'tg',
        'th',
        'tk',
        'tl',
        'tr',
        'tt',
        'ug',
        'uk',
        'ur',
        'uz',
        'vi',
        'xh',
        'yi',
        'yo',
        'zh',
        'zu',
    ]);

    async findOne({ provider, name, locale }: { provider: string; name: string; locale: string }) {
        const validLocale = this.localeCodes.has(locale.toLowerCase());
        if (!validLocale) {
            throw new Error(`Invalid locale: ${locale}`);
        }

        const result = await this.db
            .select()
            .from(schema.emailTemplates)
            .where(
                and(
                    eq(schema.emailTemplates.provider, provider),
                    eq(schema.emailTemplates.name, name),
                    eq(schema.emailTemplates.locale, locale),
                ),
            )
            .limit(1);

        return result.length > 0 ? result[0] : null;
    }
}
