import * as Handlebars from 'handlebars';

export function registerHandlebarsHelpers() {
    Handlebars.registerHelper('formatDate', function (date: Date, format: string) {
        if (!date) return '';

        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();

        if (format === 'DD/MM/YYYY') {
            return `${day}/${month}/${year}`;
        }

        return `${day}/${month}/${year}`;
    });
}
