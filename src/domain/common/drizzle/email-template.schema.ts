import { index, pgTable, timestamp, unique, uuid, varchar } from 'drizzle-orm/pg-core';

export const emailTemplates = pgTable(
    'email_templates',
    {
        id: uuid().primaryKey().notNull(),
        name: varchar({ length: 255 }).notNull(),
        locale: varchar({ length: 2 }).notNull(),
        provider: varchar({ length: 255 }).notNull(),
        providerId: varchar('provider_id', { length: 255 }).notNull(),
        createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }).notNull(),
        updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }).notNull(),
    },
    (table) => [
        index('idx_email_templates_locale').on(table.locale),
        index('idx_email_templates_provider').on(table.provider),
        index('idx_email_templates_provider_id').on(table.providerId),
        unique('email_templates_provider_name_locale_unique').on(table.provider, table.name, table.locale),
    ],
);
