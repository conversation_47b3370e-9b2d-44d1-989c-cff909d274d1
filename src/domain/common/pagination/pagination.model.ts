import { createZodDto } from '@anatine/zod-nestjs';

import { BaseFilterSchema, PaginationMetaSchema, PaginationParamsSchema } from './pagination.schema';

export class PaginationParamsModel extends createZodDto(PaginationParamsSchema) {
    limit?: number;
    cursor?: string;
}

export class BaseFilterModel extends createZodDto(BaseFilterSchema) {
    sortBy?: string;
    sortOrder?: 'asc' | 'desc' = 'asc';
}

export class PaginationMetaModel extends createZodDto(PaginationMetaSchema) {
    hasNextPage: boolean;
    nextCursor: string | null;
    totalCount?: number;

    constructor(hasNextPage = false, nextCursor: string | null = null, totalCount?: number) {
        super();
        this.hasNextPage = hasNextPage;
        this.nextCursor = nextCursor;
        this.totalCount = totalCount;
    }
}

export class PaginatedResponseModel<T> {
    data: T[];
    meta: PaginationMetaModel;

    constructor(data: T[] = [], meta: PaginationMetaModel = new PaginationMetaModel()) {
        this.data = data;
        this.meta = meta;
    }
}
