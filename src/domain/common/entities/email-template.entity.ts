import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Property } from '@mikro-orm/core';
import { v4 as uuidv4 } from 'uuid';

@Entity({
    tableName: 'email_templates',
})
export class EmailTemplate {
    @PrimaryKey({ type: 'uuid' })
    id: string = uuidv4();

    @Property({ type: 'text' })
    name: string;

    @Property({ type: 'text' })
    locale: string;

    @Property({ type: 'text' })
    provider: string;

    @Property({ type: 'text' })
    providerId: string;

    @Property({ type: 'datetime' })
    createdAt: Date = new Date();

    @Property({ type: 'datetime', onUpdate: () => new Date() })
    updatedAt: Date = new Date();
}
