import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { makeGaugeProvider, PrometheusModule } from '@willsoto/nestjs-prometheus';

import { KeycloakAdminClientService } from '../../auth/services/keycloak-admin-client.service';
import { CommonModule } from '../common/common.module';
import { MetricsController } from './metrics.controller';
import { MetricsScheduler } from './metrics.scheduler';
import { MetricsService } from './metrics.service';

@Module({
    imports: [
        ConfigModule,
        CommonModule,
        ScheduleModule.forRoot(),
        PrometheusModule.register({
            global: true,
            customMetricPrefix: 'app',
            controller: MetricsController,
        }),
    ],
    controllers: [],
    providers: [
        KeycloakAdminClientService,
        MetricsService,
        MetricsScheduler,

        makeGaugeProvider({
            name: 'session_count',
            help: 'Number of sessions',
        }),
        makeGaugeProvider({
            name: 'offline_session_count',
            help: 'Number of offline sessions',
        }),
        makeGaugeProvider({
            name: 'database_listener_connected',
            help: 'Database listener connection status (1=connected, 0=disconnected)',
        }),
        makeGaugeProvider({
            name: 'database_listener_reconnect_attempts',
            help: 'Number of database listener reconnection attempts',
        }),
        makeGaugeProvider({
            name: 'database_listener_notifications_total',
            help: 'Total number of database notifications received',
        }),
        makeGaugeProvider({
            name: 'database_listener_uptime_seconds',
            help: 'Database listener uptime in seconds',
        }),
    ],
    exports: [MetricsService],
})
export class MetricsModule {}
