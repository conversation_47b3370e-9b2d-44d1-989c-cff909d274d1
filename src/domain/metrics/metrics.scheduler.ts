import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';

import { MetricsService } from './metrics.service';

@Injectable()
export class MetricsScheduler {
    private readonly logger = new Logger(MetricsScheduler.name);

    constructor(private readonly metricsService: MetricsService) {}

    @Cron(CronExpression.EVERY_MINUTE)
    async updateSessionMetrics() {
        try {
            await Promise.all([
                this.metricsService.updateSessionCount(),
                this.metricsService.updateOfflineSessionCount(),
            ]);
        } catch (error) {
            this.logger.error({ err: error }, 'Failed to update session metrics');
        }
    }
}
