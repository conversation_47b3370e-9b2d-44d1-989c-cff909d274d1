import { Inject, Injectable } from '@nestjs/common';
import { and, eq } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

import * as schema from '../../../../drizzle/schema';
import { DRIZZLE_PROVIDER } from '../../../drizzle/drizzle.provider';
import { BaseDrizzleRepository } from '../../common/repositories/base.drizzle.repository';
import { ActionType, Permission, ResourceType } from '../entities';

@Injectable()
export class PermissionDrizzleRepository extends BaseDrizzleRepository<Permission> {
    constructor(
        @Inject(DRIZZLE_PROVIDER)
        db: NodePgDatabase<typeof schema>,
    ) {
        super(db, schema.permissions);
    }

    async getPermissionsByUserAndOrganization(userId: string, organizationId: string) {
        return this.db
            .select()
            .from(schema.permissions)
            .innerJoin(schema.rolePermissions, eq(schema.rolePermissions.permissionId, schema.permissions.id))
            .innerJoin(schema.roles, eq(schema.roles.id, schema.rolePermissions.roleId))
            .innerJoin(schema.userRoles, eq(schema.userRoles.roleId, schema.roles.id))
            .where(and(eq(schema.userRoles.userId, userId), eq(schema.roles.organizationId, organizationId)));
    }

    async getPermissionByResourceAndAction(resource: ResourceType, action: ActionType) {
        const permissions = await this.db
            .select()
            .from(schema.permissions)
            .where(and(eq(schema.permissions.resourceType, resource), eq(schema.permissions.actionType, action)))
            .limit(1);

        return permissions.length > 0 ? permissions[0] : null;
    }
}
