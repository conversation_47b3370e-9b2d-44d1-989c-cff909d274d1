import { Inject, Injectable } from '@nestjs/common';
import { eq } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

import * as schema from '../../../../drizzle/schema';
import { DRIZZLE_PROVIDER } from '../../../drizzle/drizzle.provider';
import { BaseDrizzleRepository } from '../../common/repositories/base.drizzle.repository';

@Injectable()
export class UserDrizzleRepository extends BaseDrizzleRepository<typeof schema.users.$inferSelect> {
    constructor(
        @Inject(DRIZZLE_PROVIDER)
        db: NodePgDatabase<typeof schema>,
    ) {
        super(db, schema.users);
    }

    async getUserById(id: string): Promise<typeof schema.users.$inferSelect> {
        const result = await this.db.select().from(schema.users).where(eq(schema.users.id, id)).limit(1);

        return result[0];
    }

    async getUserByEmail(email: string): Promise<typeof schema.users.$inferSelect> {
        const normalizedEmail = email.toLowerCase();
        const result = await this.db
            .select()
            .from(schema.users)
            .where(eq(schema.users.email, normalizedEmail))
            .limit(1);

        return result[0];
    }

    async getUserByKeycloakId(keycloakId: string): Promise<typeof schema.users.$inferSelect> {
        const result = await this.db
            .select()
            .from(schema.users)
            .where(eq(schema.users.keycloakId, keycloakId))
            .limit(1);

        return result[0];
    }
}
