import { SqlEntityManager } from '@mikro-orm/postgresql';
import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { eq } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

import * as schema from '../../../../drizzle/schema'; // Adjust path as needed
import { DRIZZLE_PROVIDER } from '../../../drizzle/drizzle.provider';
import { BaseRepository } from '../../common/repositories/base.repository';
import { User } from '../entities/user.entity';
import { UserStatusEnum } from '../enums/user-status.enum';

@Injectable()
export class UserRepository extends BaseRepository<User> {
    constructor(
        em: SqlEntityManager,
        @Inject(DRIZZLE_PROVIDER)
        db: NodePgDatabase<typeof schema>,
    ) {
        super(em, User.name, db);
    }

    async getUserByKeycloakId(keycloakId: string): Promise<User> {
        return await this.findOne({ keycloakId: keycloakId });
    }

    async updateUserStatus(userId: string, status: UserStatusEnum): Promise<void> {
        const user = await this.db.select().from(schema.users).where(eq(schema.users.id, userId)).limit(1);
        if (user.length === 0) {
            throw new NotFoundException('User not found');
        }

        await this.db
            .update(schema.users)
            .set({
                status,
                updatedAt: new Date().toISOString(),
            })
            .where(eq(schema.users.id, userId));
    }
}
