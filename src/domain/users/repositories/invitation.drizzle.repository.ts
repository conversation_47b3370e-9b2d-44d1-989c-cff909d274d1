import { Inject, Injectable } from '@nestjs/common';
import { and, eq, sql } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { v4 } from 'uuid';

import * as schema from '../../../../drizzle/schema';
import { DRIZZLE_PROVIDER } from '../../../drizzle/drizzle.provider';
import { BaseDrizzleRepository } from '../../common/repositories/base.drizzle.repository';
import { Invitation } from '../entities/invitation.entity';

@Injectable()
export class InvitationDrizzleRepository extends BaseDrizzleRepository<typeof schema.invitations.$inferSelect> {
    constructor(
        @Inject(DRIZZLE_PROVIDER)
        db: NodePgDatabase<typeof schema>,
    ) {
        super(db, schema.invitations);
    }

    async findByEmail(email: string): Promise<typeof schema.invitations.$inferSelect | null> {
        const result = await this.db
            .select()
            .from(schema.invitations)
            .where(eq(schema.invitations.email, email))
            .limit(1);

        return result[0] || null;
    }

    async findByUserId(userId: string): Promise<typeof schema.invitations.$inferSelect | null> {
        const result = await this.db
            .select()
            .from(schema.invitations)
            .where(eq(schema.invitations.userId, userId))
            .limit(1);

        return result[0] || null;
    }

    async findByUserIdAndOrganizationId(
        userId: string,
        organizationId: string,
    ): Promise<typeof schema.invitations.$inferSelect | null> {
        const result = await this.db
            .select()
            .from(schema.invitations)
            .where(
                and(
                    eq(schema.invitations.userId, userId),
                    sql`${schema.invitations.organizationIds} @> ${JSON.stringify([organizationId])}::jsonb`,
                ),
            )
            .limit(1);

        return result[0] ?? null;
    }

    async create(
        invitationData: typeof schema.invitations.$inferInsert,
    ): Promise<typeof schema.invitations.$inferSelect> {
        const dataWithId = {
            ...invitationData,
            id: invitationData.id || v4(),
            createdAt: invitationData.createdAt || new Date().toISOString(),
        };

        const result = await this.db.insert(schema.invitations).values(dataWithId).returning();

        return result[0];
    }

    async update(
        id: string,
        updateData: Partial<typeof schema.invitations.$inferInsert>,
    ): Promise<typeof schema.invitations.$inferSelect> {
        const result = await this.db
            .update(schema.invitations)
            .set(updateData as any)
            .where(eq(schema.invitations.id, id))
            .returning();

        return result[0];
    }

    async updateByEmail(
        email: string,
        updateData: Partial<typeof schema.invitations.$inferInsert>,
    ): Promise<typeof schema.invitations.$inferSelect | null> {
        const result = await this.db
            .update(schema.invitations)
            .set(updateData as any)
            .where(eq(schema.invitations.email, email))
            .returning();

        return result[0] || null;
    }

    async updateById(
        id: string,
        updateData: Partial<typeof schema.invitations.$inferInsert>,
    ): Promise<typeof schema.invitations.$inferSelect | null> {
        const result = await this.db
            .update(schema.invitations)
            .set(updateData as any)
            .where(eq(schema.invitations.id, id))
            .returning();

        return result[0] || null;
    }

    async updateByUserId(
        userId: string,
        updateData: Partial<typeof schema.invitations.$inferInsert>,
    ): Promise<typeof schema.invitations.$inferSelect | null> {
        const result = await this.db
            .update(schema.invitations)
            .set(updateData as any)
            .where(eq(schema.invitations.userId, userId))
            .returning();

        return result[0] || null;
    }

    // Legacy methods for compatibility with MikroORM interface
    async createInvitation(invitation: Invitation): Promise<Invitation> {
        const dataWithId = {
            id: invitation.id || v4(),
            userId: invitation.userId,
            email: invitation.email,
            organizationIds: invitation.organizationIds,
            roleIds: invitation.roleIds,
            token: invitation.token,
            isAccepted: invitation.isAccepted,
            createdAt: invitation.createdAt?.toISOString() || new Date().toISOString(),
            expiresAt: invitation.expiresAt?.toISOString(),
        };

        const result = await this.db.insert(schema.invitations).values(dataWithId).returning();

        // Convert back to Invitation entity for compatibility
        const createdInvitation = new Invitation();
        Object.assign(createdInvitation, {
            ...result[0],
            createdAt: new Date(result[0].createdAt),
            expiresAt: new Date(result[0].expiresAt),
        });

        return createdInvitation;
    }

    async updateInvitation(invitation: Invitation): Promise<Invitation> {
        const updateData = {
            userId: invitation.userId,
            email: invitation.email,
            organizationIds: invitation.organizationIds,
            roleIds: invitation.roleIds,
            token: invitation.token,
            isAccepted: invitation.isAccepted,
            expiresAt: invitation.expiresAt?.toISOString(),
        };

        const result = await this.db
            .update(schema.invitations)
            .set(updateData)
            .where(eq(schema.invitations.id, invitation.id))
            .returning();

        // Convert back to Invitation entity for compatibility
        const updatedInvitation = new Invitation();
        Object.assign(updatedInvitation, {
            ...result[0],
            createdAt: new Date(result[0].createdAt),
            expiresAt: new Date(result[0].expiresAt),
        });

        return updatedInvitation;
    }
}
