import { Loaded, SqlEntityManager } from '@mikro-orm/postgresql';
import { Inject, Injectable } from '@nestjs/common';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

import * as schema from '../../../../drizzle/schema';
import { DRIZZLE_PROVIDER } from '../../../drizzle/drizzle.provider';
import { BaseRepository } from '../../common/repositories/base.repository';
import { Organization } from '../../organizations';
import { Role } from '../entities/role.entity';
import { User } from '../entities/user.entity';

@Injectable()
export class RoleRepository extends BaseRepository<Role> {
    constructor(
        em: SqlEntityManager,
        @Inject(DRIZZLE_PROVIDER)
        db: NodePgDatabase<typeof schema>,
    ) {
        super(em, Role.name, db);
    }

    async getRolesByUserAndOrganization(user: User, organizationId?: string | null): Promise<Loaded<Role, never>[]> {
        const qb = this.createQueryBuilder('r');
        qb.select('r.*');

        if (organizationId) {
            qb.andWhere({ organization: organizationId });
        }

        qb.andWhere({ userRoles: { user: user } });
        return qb.getResult();
    }

    async createRole({
        name,
        description,
        organization,
        visible = true,
    }: {
        name: string;
        description?: string;
        organization: Organization;
        visible?: boolean;
    }): Promise<Role> {
        const role = new Role();
        role.name = name;
        role.description = description;
        role.organization = organization;
        role.visible = visible;
        await this.em.persistAndFlush(role);

        return role;
    }

    async update({ id, name, description }: { id: string; name?: string; description?: string }): Promise<Role> {
        const role = await this.findOne({ id });
        role.name = name;
        role.description = description;

        await this.em.persistAndFlush(role);

        return role;
    }
}
