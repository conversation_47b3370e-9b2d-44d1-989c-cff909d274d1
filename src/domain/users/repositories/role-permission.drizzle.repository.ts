import { Inject, Injectable } from '@nestjs/common';
import { and, eq } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { v4 as uuidv4 } from 'uuid';

import * as schema from '../../../../drizzle/schema';
import { DRIZZLE_PROVIDER } from '../../../drizzle/drizzle.provider';
import { BaseDrizzleRepository } from '../../common/repositories/base.drizzle.repository';
import { RolePermission } from '../entities';

@Injectable()
export class RolePermissionDrizzleRepository extends BaseDrizzleRepository<RolePermission> {
    constructor(
        @Inject(DRIZZLE_PROVIDER)
        db: NodePgDatabase<typeof schema>,
    ) {
        super(db, schema.rolePermissions);
    }

    async getRolePermissionsByRole(roleId: string) {
        return this.db
            .select()
            .from(schema.rolePermissions)
            .innerJoin(schema.permissions, eq(schema.permissions.id, schema.rolePermissions.permissionId))
            .where(eq(schema.rolePermissions.roleId, roleId));
    }

    async getOneByRoleAndPermission(roleId: string, permissionId: string) {
        const result = await this.db
            .select()
            .from(schema.rolePermissions)
            .where(
                and(eq(schema.rolePermissions.roleId, roleId), eq(schema.rolePermissions.permissionId, permissionId)),
            )
            .limit(1);

        return result.length > 0 ? result[0] : null;
    }

    async createRolePermission(roleId: string, permissionId: string) {
        const [rolePermission] = await this.db
            .insert(schema.rolePermissions)
            .values({
                id: uuidv4(),
                roleId: roleId,
                permissionId: permissionId,
                createdAt: new Date().toISOString(),
            })
            .returning();

        return rolePermission;
    }

    async deleteRolePermission(id: string) {
        await this.db.delete(schema.rolePermissions).where(eq(schema.rolePermissions.id, id));
    }
}
