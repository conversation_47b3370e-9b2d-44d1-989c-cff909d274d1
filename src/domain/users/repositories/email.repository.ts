import { Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { EmailTemplateEnum } from '../../common/enums';
import { EmailService } from '../../common/services/email';

@Injectable()
export class UserEmailRepository {
    private readonly logger = new Logger(UserEmailRepository.name);

    constructor(
        private configService: ConfigService,
        @Inject(EmailService)
        private emailService: EmailService,
    ) {}

    public async sendInvitationEmail(email: string, url: string, locale: string) {
        try {
            this.logger.log(`Sending sign in invitation to ${email}`);
            await this.emailService.send({
                toEmails: [email],
                locale,
                templateName: EmailTemplateEnum.INVITATION,
                tags: ['Onboarding'],
                params: {
                    url,
                },
            });
        } catch (error) {
            this.logger.error({ err: error, email }, `Error sending sign in invitation to ${email}`);
        }
    }
}
