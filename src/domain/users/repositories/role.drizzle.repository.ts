import { Inject, Injectable } from '@nestjs/common';
import { and, eq } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

import * as schema from '../../../../drizzle/schema';
import { DRIZZLE_PROVIDER } from '../../../drizzle/drizzle.provider';
import { BaseDrizzleRepository } from '../../common/repositories/base.drizzle.repository';
import { Role } from '../entities';

@Injectable()
export class RoleDrizzleRepository extends BaseDrizzleRepository<Role> {
    constructor(
        @Inject(DRIZZLE_PROVIDER)
        db: NodePgDatabase<typeof schema>,
    ) {
        super(db, schema.roles);
    }

    async findRoleById(id: string) {
        const roles = await this.db.select().from(schema.roles).where(eq(schema.roles.id, id));
        return roles[0];
    }

    async createRole(name, description, organizationId, visible = true) {
        const result = await this.db
            .insert(schema.roles)
            .values({
                name,
                description,
                organizationId,
                visible,
            })
            .returning({ id: schema.roles.id });

        return result[0];
    }

    async getUserRoles(userId: string, organizationId: string) {
        return this.db
            .select()
            .from(schema.roles)
            .innerJoin(schema.userRoles, eq(schema.userRoles.roleId, schema.roles.id))
            .where(and(eq(schema.userRoles.userId, userId), eq(schema.roles.organizationId, organizationId)));
    }

    // getUserRolePermissions(
    //     userId: string,
    //     organizationId: string,
    // ): Promise<(typeof schema.rolePermissions.$inferSelect)[]> {
    //     return this.db
    //         .select()
    //         .from(schema.rolePermissions)
    //         .where(
    //             and(
    //                 eq(schema.rolePermissions.userId, userId),
    //                 eq(schema.rolePermissions.organizationId, organizationId),
    //             ),
    //         );
    // }
}
