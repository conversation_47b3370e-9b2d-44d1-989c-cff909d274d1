import { EntityRepository } from '@mikro-orm/postgresql';
import { BadRequestException, Injectable } from '@nestjs/common';

import * as schema from '../../../../drizzle/schema';
import { Permission } from '../entities/permission.entity';
import { Role } from '../entities/role.entity';
import { RolePermission } from '../entities/role-permissions.entity';
@Injectable()
export class RolePermissionRepository extends EntityRepository<RolePermission> {
    async getOneByRoleAndPermission(
        role: typeof schema.roles.$inferSelect | string,
        permission: Permission | string,
    ): Promise<RolePermission | null> {
        return this.findOne({ role: role, permission: permission });
    }

    async createOrUpdate(rolePermission: RolePermission): Promise<RolePermission> {
        return await this.upsert(rolePermission, {
            onConflictFields: ['role', 'permission'],
        });
    }

    async linkPermissionsToRole({ role, permissionIds }: { role: Role; permissionIds: string[] }) {
        const permissions = await this.em.find(Permission, {
            id: { $in: permissionIds },
        });

        if (permissions.some((permission) => permission.disabled)) {
            throw new BadRequestException('Some permissions are disabled');
        }

        const rolePermissions = permissions.map((permission) => {
            return this.create({
                role,
                permission,
            });
        });

        await this.em.persistAndFlush(rolePermissions);

        return rolePermissions;
    }

    async unlinkPermissionsFromRole({ role, permissionIds }: { role: Role; permissionIds: string[] }) {
        const permissions = await this.em.find(Permission, {
            id: { $in: permissionIds },
        });

        permissions.map((permission) => {
            return this.nativeDelete({
                role: role.id,
                permission: permission.id,
            });
        });
    }

    async createRolePermission({ role, permission }: { role: Role; permission: Permission }): Promise<RolePermission> {
        // Check if this entity is already linked to any organization
        const existingRolePermission = await this.findOne({
            role,
            permission,
        });
        if (existingRolePermission) {
            return existingRolePermission;
        }

        const rolePermission = new RolePermission();
        rolePermission.role = role;
        rolePermission.permission = permission;

        await this.em.persistAndFlush(rolePermission);

        return rolePermission;
    }
}
