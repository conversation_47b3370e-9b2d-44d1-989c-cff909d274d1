import { <PERSON>tity, EntityRepositoryType, ManyToOne, PrimaryKey, Property } from '@mikro-orm/core';
import { v4 as uuidv4 } from 'uuid';

import { UserRoleRepository } from '../repositories';
import { Role } from './role.entity';
import { User } from './user.entity';
/**
 * UserRole entity represents the assignment of a role to a user.
 * This maps users to their roles within specific organizations.
 * In Keycloak, this represents a user's membership in a subgroup.
 */
@Entity({ tableName: 'user_roles', repository: () => UserRoleRepository })
export class UserRole {
    [EntityRepositoryType]?: UserRoleRepository;

    @PrimaryKey({ type: 'uuid' })
    id: string = uuidv4();

    @ManyToOne(() => User)
    user!: User;

    @ManyToOne(() => Role)
    role!: Role;

    @Property({ type: 'boolean', default: false })
    ignoreAdvPerms: boolean = false;

    @Property({ type: 'datetime' })
    createdAt: Date = new Date();
}
