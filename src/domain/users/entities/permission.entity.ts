import { Collection, Entity, EntityRepositoryType, Enum, OneToMany, PrimaryKey, Property } from '@mikro-orm/core';
import { v4 as uuidv4 } from 'uuid';

import { PermissionRepository } from '../repositories/permission.repository';
import { RolePermission } from './role-permissions.entity';

// Enum for resource types
export enum ResourceType {
    QUOTE = 'quote',
    QUOTE_ITEM = 'quote_item',
    CONTRACT = 'contract',
    CONTRACT_ITEM = 'contract_item',
    ENTITY = 'entity',
    ORGANIZATION = 'organization',
    USER = 'user',
    ROLE = 'role',
    CONTACT = 'contact',
    ASSET = 'asset',
    PLATFORM = 'platform',
    USER_ORGANIZATION = 'user_organization',
    INVITED_USER = 'invited_user',
}

// Enum for common actions
export enum ActionType {
    CREATE = 'create',
    READ = 'read',
    UPDATE = 'update',
    APPROVE = 'approve',
    REJECT = 'reject',
    EXPORT = 'export',
    MANAGE = 'manage',
}

/**
 * Permission entity represents a Keycloak role mapping.
 * In Keycloak, these are the client role mappings assigned to groups.
 * For example, 'assets-view', 'contracts-edit', etc.
 */
@Entity({
    tableName: 'permissions',
    repository: () => PermissionRepository,
})
export class Permission {
    [EntityRepositoryType]?: PermissionRepository;

    @PrimaryKey({ type: 'uuid' })
    id: string = uuidv4();

    @Enum({ items: () => ResourceType, nativeEnumName: 'resource_type' })
    resourceType!: ResourceType;

    @Enum({ items: () => ActionType, nativeEnumName: 'action_type' })
    actionType!: ActionType;

    @Property()
    name: string; // Constructed as `${resourceType}:${actionType}`

    @Property({ nullable: true })
    description?: string;

    @Property({ default: false })
    disabled = false;

    @OneToMany(() => RolePermission, (rolePermission) => rolePermission.permission)
    rolePermissions = new Collection<RolePermission>(this);

    @Property({ type: 'datetime' })
    createdAt: Date = new Date();

    @Property({ type: 'datetime', onUpdate: () => new Date() })
    updatedAt: Date = new Date();
}
