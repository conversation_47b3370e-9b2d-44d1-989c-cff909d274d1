import { <PERSON><PERSON>ty, ManyToOne, PrimaryKey, Property } from '@mikro-orm/core';
import { v4 } from 'uuid';

import { Role } from './role.entity';
import { User } from './user.entity';

@Entity({ tableName: 'invitations' })
export class Invitation {
    @PrimaryKey()
    id: string = v4();

    @Property()
    userId: string;

    @Property()
    email: string;

    @Property({ type: 'json' })
    organizationIds: string[];

    @Property()
    roleIds: string[];

    @Property()
    token: string;

    @Property()
    isAccepted: boolean = false;

    @Property()
    createdAt: Date = new Date();

    @Property()
    expiresAt: Date;

    @ManyToOne(() => User, { persist: false })
    user: User;

    @ManyToOne(() => Role, { persist: false })
    role: Role;
}
