import {
    Collection,
    Entity,
    EntityRepositoryType,
    Enum,
    Index,
    OneToMany,
    PrimaryKey,
    Property,
} from '@mikro-orm/core';
import { v4 as uuidv4 } from 'uuid';

import { UserStatusEnum } from '../enums/user-status.enum';
import { UserRepository } from '../repositories/user.repository';
import { UserRole } from './user-role.entity';

@Entity({
    tableName: 'users',
    repository: () => UserRepository,
})
export class User {
    [EntityRepositoryType]?: UserRepository;

    @PrimaryKey({ type: 'uuid' })
    id: string = uuidv4();

    @Index()
    @Property({ unique: true })
    keycloakId: string;

    @Property({ unique: true })
    email: string;

    @Property({ nullable: true })
    firstName?: string;

    @Property({ nullable: true })
    lastName?: string;

    @Property({ nullable: true })
    phone?: string;

    @Property({ default: 'en' })
    locale?: string;

    @Property({ default: 'UTC' })
    timezone?: string;

    @OneToMany(() => UserRole, (userRole) => userRole.user)
    userRoles = new Collection<UserRole>(this);

    @Property({ type: 'boolean', default: false })
    isActive: boolean = false;

    @Property({ type: 'datetime' })
    inviteExpiresAt: Date = new Date();

    @Enum({ items: () => UserStatusEnum, nativeEnumName: 'user_status', default: UserStatusEnum.INACTIVE })
    status: UserStatusEnum;

    @Property({ type: 'datetime' })
    createdAt: Date = new Date();

    @Property({ type: 'datetime', onUpdate: () => new Date() })
    updatedAt: Date = new Date();

    @Property({ nullable: true, type: 'datetime' })
    lastLoginAt?: Date;

    @Property({ type: 'boolean', default: false })
    isMigrated: boolean = false;
}
