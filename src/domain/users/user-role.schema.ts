import { relations } from 'drizzle-orm';
import { boolean, foreignKey, pgTable, timestamp, uuid } from 'drizzle-orm/pg-core';

import { roles } from './role.schema';
import { users } from './user.schema';

export const userRoles = pgTable(
    'user_roles',
    {
        id: uuid().primaryKey().notNull(),
        userId: uuid('user_id').notNull(),
        roleId: uuid('role_id').notNull(),
        ignoreAdvPerms: boolean('ignore_adv_perms').default(false).notNull(),
        createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }).notNull(),
    },
    (table) => [
        foreignKey({
            columns: [table.userId],
            foreignColumns: [users.id],
            name: 'user_roles_user_id_foreign',
        }).onUpdate('cascade'),
        foreignKey({
            columns: [table.roleId],
            foreignColumns: [roles.id],
            name: 'user_roles_role_id_foreign',
        }).onUpdate('cascade'),
    ],
);

export const userRolesRelations = relations(userRoles, ({ one }) => ({
    user: one(users, {
        fields: [userRoles.userId],
        references: [users.id],
    }),
    role: one(roles, {
        fields: [userRoles.roleId],
        references: [roles.id],
    }),
}));
