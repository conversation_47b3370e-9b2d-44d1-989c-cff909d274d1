import { Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { EmailTemplateEnum } from '../common/enums';
import { EmailService } from '../common/services/email';
import { NotificationOrganizationDto } from './dto/notification/notification-organization.dto';

@Injectable()
export class NotificationService {
    private readonly logger = new Logger(NotificationService.name);

    constructor(
        private configService: ConfigService,
        @Inject(EmailService)
        private emailService: EmailService,
    ) {}

    public async sendSignUpInvitationNotification(
        email: string,
        organizations: NotificationOrganizationDto[],
        url: string,
        locale: string,
    ) {
        try {
            this.logger.log(`Sending sign up invitation to ${email}`);

            await this.emailService.send({
                toEmails: [email],
                locale,
                templateName: EmailTemplateEnum.SIGN_UP_INVITATION,
                tags: ['Invitation'],
                params: {
                    url,
                    organizations: organizations.map((org) => ({
                        name: org.name,
                        isNew: org.isNew,
                    })),
                },
            });
        } catch (error) {
            this.logger.error({ err: error, email }, `Error sending sign up invitation to ${email}`);
        }
    }

    public async sendSignInInvitationNotification(
        email: string,
        organizations: NotificationOrganizationDto[],
        url: string,
        locale: string,
    ) {
        try {
            this.logger.log(`Sending sign in invitation to ${email}`);

            await this.emailService.send({
                toEmails: [email],
                locale,
                templateName: EmailTemplateEnum.INVITATION,
                tags: ['Invitation'],
                params: {
                    url,
                    organizations: organizations.map((org) => ({
                        name: org.name,
                        isNew: org.isNew,
                    })),
                },
            });
        } catch (error) {
            this.logger.error({ err: error, email }, `Error sending sign in invitation to ${email}`);
        }
    }
}
