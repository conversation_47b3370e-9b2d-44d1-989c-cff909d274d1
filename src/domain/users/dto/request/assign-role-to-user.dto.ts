import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

export const AssignRoleToUserSchema = extendApi(
    z.object({
        roleId: z.string().describe('Role Id'),
        ignoreAdvPerms: z.boolean().default(false).describe('Ignore advanced permissions'),
    }),
    {
        title: 'AssignRoleToUserSchema',
        description: 'Data required to assign a role to a user',
    },
);

export class AssignRoleToUserDto extends createZodDto(AssignRoleToUserSchema) {
    static readonly zodSchema = AssignRoleToUserSchema;
}
