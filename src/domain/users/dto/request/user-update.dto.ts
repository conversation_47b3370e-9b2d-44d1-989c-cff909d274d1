import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { addMetaProperties } from '../../../../helpers/zod.helper';

export const UserUpdateRequestSchema = extendApi(
    z.object({
        firstName: extendApi(z.string().trim().max(255).min(1), {
            description: 'FIELDS.FIRST_NAME',
        }),
        lastName: extendApi(z.string().trim().max(255).min(1), {
            description: 'FIELDS.LAST_NAME',
        }),

        phone: extendApi(z.string().optional().nullable(), {
            description: 'FIELDS.PHONE',
        }),

        locale: extendApi(z.string().optional().nullable(), {
            description: 'FIELDS.LANGUAGE',
        }),

        timezone: extendApi(z.string().optional().nullable(), {
            description: 'FIELDS.TIMEZONE',
        }),

        email: addMetaProperties(z.string().email().optional().nullable().describe('FIELDS.EMAIL'), {
            'ui:options': {
                readOnly: true,
                helperText: 'FIELDS.EMAIL_HELPER_TEXT',
            },
        }),
    }),
);

export class UserUpdateRequestDto extends createZodDto(UserUpdateRequestSchema) {}
