import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { addMetaProperties } from '../../../../helpers/zod.helper';

export const UserChangePasswordRequestSchema = extendApi(
    z
        .object({
            currentPassword: extendApi(z.string(), {
                type: 'string',
                format: 'password',
                description: 'SECURITY.CURRENT_PASSWORD',
            }),

            newPassword: extendApi(z.string().min(8), {
                type: 'string',
                format: 'password',
                description: 'SECURITY.NEW_PASSWORD',
            }),
            confirmPassword: extendApi(z.string().min(8), {
                type: 'string',
                format: 'password',
                description: 'SECURITY.CONFIRM_PASSWORD',
            }),
        })
        .refine(
            (data) => {
                return data.newPassword === data.confirmPassword;
            },
            {
                path: ['passwordConfirm'],
                message: 'SECURITY.PASSWORDS_DO_NOT_MATCH',
            },
        ),
);

export class UserChangePasswordRequestDto extends createZodDto(UserChangePasswordRequestSchema) {}
