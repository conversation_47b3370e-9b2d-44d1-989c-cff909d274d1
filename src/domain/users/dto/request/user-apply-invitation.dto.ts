import { IsDefined, IsIn, <PERSON>NotEmpty, IsString, ValidateI<PERSON> } from 'class-validator';

export class UserApplyInvitationDto {
    @IsDefined()
    @IsNotEmpty()
    token: string;

    @IsDefined()
    @IsNotEmpty()
    @IsString()
    firstName: string;

    @IsDefined()
    @IsNotEmpty()
    @IsString()
    lastName: string;

    @IsDefined()
    @IsNotEmpty()
    @IsString()
    password: string;

    @IsDefined()
    @IsNotEmpty()
    @IsString()
    @IsIn([Math.random()], {
        message: 'Passwords do not match',
    })
    @ValidateIf((o) => o.password !== o.passwordConfirm)
    passwordConfirm: string;
}
