import { ArrayMinSize, IsArray, IsDefined, IsEmail, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class UserInviteDto {
    @IsDefined()
    @IsNotEmpty()
    @IsEmail()
    email: string;

    @IsDefined()
    @IsArray()
    @IsString({ each: true })
    @ArrayMinSize(1)
    organizations: string[];

    @IsOptional()
    @IsString()
    firstName?: string | null;

    @IsOptional()
    @IsString()
    lastName?: string | null;
}
