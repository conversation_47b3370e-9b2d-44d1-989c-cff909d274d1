import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';

import { EventNameEnum } from '../../common/enums/event-names.enum';
import { GenericEvent } from '../../common/interfaces/generic-event.interface';
import { Organization } from '../../organizations';
import { RolesService } from '../services';

@Injectable()
export class OrganizationCreatedEventListener {
    private readonly logger = new Logger('UsersOrganizationCreatedEventListener');

    constructor(private readonly rolesService: RolesService) {}

    @OnEvent(EventNameEnum.ORGANIZATION_CREATED_V1)
    async handleAllEvents(event: GenericEvent<Organization>) {
        this.logger.log(`Initiating default role creation for organization ${event.payload.id}`);

        await this.rolesService.createDefaultRoles({ organizationId: event.payload.id });

        this.logger.log(`Finished default role creation for organization ${event.payload.id}`);
    }
}
