import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

export const RoleSchema = extendApi(
    z.object({
        id: extendApi(z.string(), {
            description: 'Role ID',
        }),

        name: extend<PERSON>pi(z.string(), {
            description: 'Role Name',
        }),

        description: extendApi(z.string(), {
            description: 'Role Description',
        }),
    }),
    {
        title: 'Role',
        description: 'Role model',
    },
);
