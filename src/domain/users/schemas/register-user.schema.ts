import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { addMetaProperties } from '../../../helpers/zod.helper';

export const RegisterUserSchema = extendApi(
    z.object({
        token: extendApi(z.string(), {
            description: 'Invitation token',
            example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        }),
        firstName: extendApi(z.string().min(1), {
            description: 'FIELDS.FIRST_NAME',
            example: 'John',
        }),
        lastName: extendApi(z.string().min(1), {
            description: 'FIELDS.LAST_NAME',
            example: 'Doe',
        }),
        password: addMetaProperties(
            extendApi(z.string().min(8), {
                description: 'FIELDS.PASSWORD',
                example: 'password123',
            }),
            {
                format: 'password',
                'ui:options': {
                    helperText: 'FIELDS.PASSWORD_HINT',
                },
            },
        ),
    }),
    {
        title: 'RegisterUser',
        description: 'Data required to register a user from an invitation',
    },
);
