import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { RelatedActionSchema } from '../../common/schemas';
import { UserStatusEnum } from '../enums/user-status.enum';
import { RoleSchema } from './role.schema';

export const UserSchema = extendApi(
    z.object({
        id: extendApi(z.string(), {
            description: 'User ID',
        }),
        createdAt: extendApi(z.string().datetime({ offset: true }).nullable().optional(), {
            description: 'Created At date',
        }),
        updatedAt: extendApi(z.string().datetime({ offset: true }).nullable().optional(), {
            description: 'Updated At date',
        }),
        inviteExpiresAt: extendApi(z.string().datetime({ offset: true }).nullable().optional(), {
            description: 'Invite Expires At date',
        }),
        status: extendApi(z.string().nullable().optional(), {
            description: 'User status',
        }),
        email: extendApi(z.string().nullable(), {
            description: 'Email',
        }),
        firstName: extendApi(z.string().nullable(), {
            description: 'Last name',
        }),
        lastName: extendApi(z.string().nullable(), {
            description: 'Last name',
        }),
        isActive: extendApi(z.boolean(), {
            description: 'Is Active',
        }),
        keycloakId: extendApi(z.string().nullable().optional()),
        lastLoginAt: extendApi(z.string().datetime({ offset: true }).nullable().optional(), {
            description: 'Last login at',
        }),
        locale: extendApi(z.string().nullable().optional(), {
            description: 'Last login at',
        }),
        phone: extendApi(z.string().nullable().optional(), {
            description: 'Phone',
        }),
        roleId: extendApi(z.string().nullable().optional(), {
            description: 'Role ID',
        }),
        roleName: extendApi(z.string().nullable().optional(), {
            description: 'Role name',
        }),
        timezone: extendApi(z.string().nullable().optional(), {
            description: 'Timezone',
        }),
        role: extendApi(RoleSchema),

        ignoreAdvPerms: z.boolean().optional().describe('Ignore advanced permissions'),

        _actions: extendApi(
            z.object({
                removeInvited: extendApi(RelatedActionSchema, { description: 'Remove invited' }),
            }),
        ),
    }),
);
