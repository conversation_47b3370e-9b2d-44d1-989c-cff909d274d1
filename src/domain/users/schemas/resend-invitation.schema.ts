import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

export const ResendInvitationSchema = extendApi(
    z.object({
        email: extendApi(z.string().email(), {
            description: 'FIELDS.EMAIL_OF_THE_USER_TO_RESEND_INVITATION_TO',
            example: '<EMAIL>',
        }),
        organizationIds: extendApi(z.array(z.string().uuid()), {
            description: 'List of organization IDs to include in the invitation',
            example: ['123e4567-e89b-12d3-a456-************'],
        }),
    }),
    {
        title: 'ResendInvitation',
        description: 'Data required to resend an invitation',
    },
);
