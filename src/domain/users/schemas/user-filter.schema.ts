import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { BaseFilterSchema } from '../../common/pagination/pagination.schema';

export const UserFilterSchema = extendApi(
    BaseFilterSchema.extend({
        search: z.string().optional().describe('Search query to filter by'),
    }),
    {
        title: 'UserFilter',
        description: 'User filter parameters',
    },
);
