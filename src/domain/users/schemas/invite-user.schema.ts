import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

export const InviteUserSchema = extendApi(
    z.object({
        email: extendApi(z.string().email(), {
            description: 'FIELDS.EMAIL_OF_THE_USER_TO_INVITE',
            example: '<EMAIL>',
        }),
        organizationIds: extendApi(z.array(z.string().uuid()), {
            description: 'List of organization IDs to invite the user to',
            example: ['123e4567-e89b-12d3-a456-************'],
        }),
        roleId: extendApi(z.string().uuid(), {
            description: 'Role ID to assign to the user',
            example: '123e4567-e89b-12d3-a456-************',
        }),
    }),
    {
        title: 'InviteUser',
        description: 'Data required to invite a user to organizations',
    },
);
