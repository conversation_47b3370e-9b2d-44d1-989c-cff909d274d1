import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

export const CreateRoleSchema = extendApi(
    z.object({
        name: z.string().min(1).describe('The name of the role'),
        description: z.string().nullable().optional().describe('The description of the role'),
        organizationId: z.string().uuid().describe('The organization of the role'),
    }),
    {
        title: 'CreateRoleSchema',
        description: 'Data required to create a new role',
    },
);
