import { Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';

import * as schema from '../../../drizzle/schema';
import { KeycloakAdminClientService } from '../../auth/services/keycloak-admin-client.service';
import { OrganizationsAreRequiredException } from '../common/exceptions/organizations-are-required.exception';
import { BrevoService } from '../common/services';
import { OrganizationDto } from '../organizations/models';
import { OrganizationsService } from '../organizations/services/organizations.service';
import { NotificationOrganizationDto } from './dto/notification/notification-organization.dto';
import { UserStatusEnum } from './enums/user-status.enum';
import { InvitationAlreadyAcceptedException } from './exceptions/invitation-already-accepted.exception';
import { InvitationNotFoundException } from './exceptions/invitation-not-found.exception';
import { UnableToInviteUserException } from './exceptions/unable-to-invite-user.exception';
import { UserValidationException } from './exceptions/user-validation.exception';
import { keycloakErrorMessageToErrorMessage } from './helpers/keycloak-error-message-to-error-message';
import { IKeycloakUserValidationException } from './interfaces/keycloak-user-validation-exception.interface';
import { NotificationService } from './notification.service';
import { InvitationDrizzleRepository } from './repositories/invitation.drizzle.repository';
import { UserRepository } from './repositories/user.repository';
import { UserService } from './services/users.service';

@Injectable()
export class InvitationService {
    private readonly logger = new Logger(InvitationService.name);

    constructor(
        private configService: ConfigService,
        private notificationService: NotificationService,
        private keycloakAdminClientService: KeycloakAdminClientService,
        private invitationRepository: InvitationDrizzleRepository,
        @Inject(UserRepository) private userRepository: UserRepository,
        private userService: UserService,
        private organizationsService: OrganizationsService,
        private brevoService: BrevoService,
        private jwtService: JwtService,
    ) {}

    async inviteUser(email: string, organizationIds: string[], roleId: string) {
        const normalizedEmail = email.toLowerCase();

        if (!organizationIds || organizationIds.length === 0) {
            throw new OrganizationsAreRequiredException();
        }

        await this.validateOrganizations(organizationIds);

        const availableOrganizations = await this.getOrganizationsInfo(organizationIds, true);
        const locale = availableOrganizations[0]?.locale || 'en';

        const user = await this.getOrCreateUser(normalizedEmail, locale);

        if (user.isActive) {
            const contact = await this.brevoService.getContact(normalizedEmail);
            if (!contact) {
                this.logger.log(`Creating new contact for email ${normalizedEmail}`);
                await this.brevoService.createContact(normalizedEmail, null, null, null, 'invited', locale);
            } else {
                this.logger.log(`Updating existing contact for email ${normalizedEmail}`);
                await this.brevoService.updateContact(contact.id, null, null, null, 'invited', locale);
            }
        }

        if (!user.isActive) {
            return this.sendInvitationToNewUser(
                user,
                normalizedEmail,
                organizationIds,
                [roleId],
                availableOrganizations,
            );
        }

        return this.notifyExistingUser(user, normalizedEmail, organizationIds, [roleId], availableOrganizations);
    }

    async resendInvitation(email: string, organizationIds: string[]) {
        const normalizedEmail = email.toLowerCase();
        const invitation = await this.invitationRepository.findByEmail(email);
        if (!invitation) {
            throw new InvitationNotFoundException();
        }

        if (invitation.isAccepted) {
            throw new InvitationAlreadyAcceptedException();
        }

        await this.validateOrganizations(organizationIds);

        const expiredAt = new Date();
        expiredAt.setHours(expiredAt.getHours() + 24);

        const token = this.encodeInvitationToken({
            id: invitation.userId,
            expiredAt: expiredAt.getTime(),
        });

        const updatedOrganizationIds = [...new Set([...invitation.organizationIds, ...organizationIds])];
        await this.invitationRepository.updateByEmail(email, {
            token,
            expiresAt: expiredAt.toISOString(),
            organizationIds: updatedOrganizationIds,
        });

        const availableOrganizations = await this.getOrganizationsInfo(updatedOrganizationIds, true, true);

        const user = await this.getOrCreateUser(normalizedEmail);

        this.sendInvitationToNewUser(
            user,
            invitation.email,
            updatedOrganizationIds,
            invitation.roleIds,
            availableOrganizations,
        );
    }

    async applyInvitation(token: string, firstName: string, lastName: string, password: string) {
        const data = await this.decodeAndValidateToken(token);
        const user = await this.findAndValidateUser(data.id);
        const invitation = await this.findAndValidateInvitation(user.id);

        await this.updateKeycloakUser(user, firstName, lastName, password);
        await this.updateLocalUser(user.id, firstName, lastName);
        await this.acceptInvitation(invitation);

        const contact = await this.brevoService.getContact(user.email);
        if (!contact) {
            this.logger.log(`Creating new contact for email ${user.email}`);
            await this.brevoService.createContact(user.email, firstName, lastName, null, 'registered');
        } else {
            this.logger.log(`Updating existing contact for email ${user.email}`);
            await this.brevoService.updateContact(contact.id, firstName, lastName, null, 'registered');
        }

        return {
            email: user.email,
            organizationIds: invitation.organizationIds,
        };
    }

    private async getOrCreateUser(email: string, locale?: string): Promise<typeof schema.users.$inferSelect> {
        const normalizedEmail = email.toLowerCase();
        const user = await this.userRepository.findOne({ email: normalizedEmail });
        if (user) {
            // Convert MikroORM entity to Drizzle schema type
            return {
                id: user.id,
                keycloakId: user.keycloakId,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                phone: user.phone,
                isActive: user.isActive,
                inviteExpiresAt: user?.inviteExpiresAt?.toISOString() ?? null,
                status: user.status,
                createdAt: user.createdAt.toISOString(),
                updatedAt: user.updatedAt.toISOString(),
                lastLoginAt: user.lastLoginAt ? user.lastLoginAt.toISOString() : null,
                locale: user.locale || 'de',
                timezone: user.timezone || 'UTC',
                isMigrated: user.isMigrated || false,
            };
        }

        return this.createKeycloakAndLocalUser(normalizedEmail, locale);
    }

    private async createKeycloakAndLocalUser(
        email: string,
        locale?: string,
    ): Promise<typeof schema.users.$inferSelect> {
        const normalizedEmail = email.toLowerCase();
        const keycloakRealm = this.configService.get('KEYCLOAK_REALM');
        const adminClient = await this.keycloakAdminClientService.getClient();

        const keycloakUsers = await adminClient.users.find({
            realm: keycloakRealm,
            email: normalizedEmail,
            max: 1,
        });

        if (keycloakUsers.length > 0) {
            const keycloakUser = keycloakUsers[0];
            const isActive = this.checkKeycloakUserStatus(keycloakUser);
            const user = await this.userService.findOrCreateUser(
                keycloakUser.id,
                normalizedEmail,
                undefined,
                undefined,
            );
            if (!isActive) {
                await this.userService.deactivateUser(user.id);
                user.isActive = false;
            }
            return user;
        }

        const res = await adminClient.users.create({
            realm: keycloakRealm,
            email: normalizedEmail,
            enabled: false,
            attributes: {
                disableReason: ['invitation'],
            },
        });

        const user = await this.userService.findOrCreateUser(res.id, normalizedEmail, undefined, undefined, locale);
        await this.userService.deactivateUser(user.id);
        user.isActive = false;
        return user;
    }

    private checkKeycloakUserStatus(keycloakUser: any): boolean {
        if (keycloakUser.enabled) {
            return true;
        }

        const disableReason = keycloakUser.attributes?.disableReason?.[0] ?? null;
        if (disableReason !== 'invitation') {
            throw new UnableToInviteUserException();
        }

        return false;
    }

    private async sendInvitationToNewUser(
        user: typeof schema.users.$inferSelect,
        email: string,
        organizationIds: string[],
        roleIds: string[],
        organizations: NotificationOrganizationDto[],
    ) {
        const token = this.generateInvitationToken(user.id);
        await this.createOrUpdateInvitation(user.id, email, organizationIds, roleIds, token);

        const signUpUrl = `${this.configService.get('FRONTEND_SIGNUP_URI')}?token=${token}`;
        await this.notificationService.sendSignUpInvitationNotification(
            email,
            organizations,
            signUpUrl,
            organizations[0]?.locale,
        );

        await this.userRepository.updateUserStatus(user.id, UserStatusEnum.INVITED);

        this.logger.log(
            `Invitation sent to ${email} with ${organizationIds.length} organizations and roleIds ${roleIds}`,
        );

        return { success: true };
    }

    private async notifyExistingUser(
        user: typeof schema.users.$inferSelect,
        email: string,
        organizationIds: string[],
        roleIds: string[],
        organizations: NotificationOrganizationDto[],
    ) {
        const signInUrl = `${this.configService.get('FRONTEND_SIGNIN_URI')}?organizationId=${organizationIds[0]}`;

        await this.notificationService.sendSignInInvitationNotification(email, organizations, signInUrl, user.locale);

        await Promise.all(
            roleIds.map(async (roleId) => {
                try {
                    await this.userService.assignRoleToUser(user.id, roleId);
                } catch (error) {
                    this.logger.error(`Failed to assign role to user: ${error.message}`);
                }
            }),
        );

        this.logger.log(`Additional organization invitation sent to existing user ${email}`);
        return { success: true };
    }

    private generateInvitationToken(userId: string): string {
        const createdAt = new Date();
        const expiredAt = new Date();
        expiredAt.setHours(createdAt.getHours() + 24);

        return this.encodeInvitationToken({
            id: userId,
            expiredAt: expiredAt.getTime(),
        });
    }

    private async createOrUpdateInvitation(
        userId: string,
        email: string,
        organizationIds: string[],
        roleIds: string[],
        token: string,
    ): Promise<typeof schema.invitations.$inferSelect> {
        const normalizedEmail = email.toLowerCase();
        const expiredAt = new Date();
        expiredAt.setHours(expiredAt.getHours() + 24);

        const invitation = await this.invitationRepository.findByEmail(normalizedEmail);
        if (invitation) {
            const updatedOrganizationIds = [...new Set([...invitation.organizationIds, ...organizationIds])];

            return await this.invitationRepository.updateByEmail(email, {
                userId,
                organizationIds: updatedOrganizationIds,
                roleIds: Array.from(new Set([...invitation.roleIds, ...roleIds])),
                token,
                expiresAt: expiredAt.toISOString(),
                isAccepted: false,
            });
        }

        return await this.invitationRepository.create({
            userId,
            email,
            organizationIds,
            roleIds,
            token,
            expiresAt: expiredAt.toISOString(),
            isAccepted: false,
        } as any);
    }

    private async decodeAndValidateToken(token: string): Promise<any> {
        try {
            const data = await this.decodeInvitationToken(token);
            if (data.expiredAt <= new Date().getTime()) {
                throw new Error('Invitation expired');
            }
            return data;
        } catch (e) {
            throw new InvitationNotFoundException();
        }
    }

    private async findAndValidateUser(userId: string): Promise<typeof schema.users.$inferSelect> {
        const user = await this.userRepository.findOne({ id: userId });
        if (!user) {
            throw new Error('Invitation has been revoked');
        }

        if (user.isActive) {
            throw new Error('Invitation already used');
        }

        // Convert MikroORM entity to Drizzle schema type
        return {
            id: user.id,
            keycloakId: user.keycloakId,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            phone: user.phone,
            isActive: user.isActive,
            inviteExpiresAt: user?.inviteExpiresAt?.toISOString() ?? null,
            status: user.status,
            createdAt: user.createdAt.toISOString(),
            updatedAt: user.updatedAt.toISOString(),
            lastLoginAt: user.lastLoginAt ? user.lastLoginAt.toISOString() : null,
            locale: user.locale || 'en',
            timezone: user.timezone || 'UTC',
            isMigrated: user.isMigrated || false,
        };
    }

    private async findAndValidateInvitation(userId: string): Promise<typeof schema.invitations.$inferSelect> {
        const invitation = await this.invitationRepository.findByUserId(userId);
        if (!invitation || invitation.isAccepted) {
            throw new Error('Invitation not found or already used');
        }

        return invitation;
    }

    private async updateKeycloakUser(
        user: typeof schema.users.$inferSelect,
        firstName: string,
        lastName: string,
        password: string,
    ): Promise<void> {
        const keycloakUser = await this.keycloakAdminClientService.getUser(user.keycloakId);
        if (!keycloakUser) {
            throw new Error('User account has been revoked');
        }

        const attributes = keycloakUser.attributes ?? {};
        if (attributes['disableReason']) {
            delete attributes['disableReason'];
        }

        const adminClient = await this.keycloakAdminClientService.getClient();
        try {
            await adminClient.users.update(
                {
                    realm: this.configService.get('KEYCLOAK_REALM'),
                    id: user.keycloakId,
                },
                {
                    email: user.email,
                    enabled: true,
                    firstName,
                    lastName,
                    emailVerified: true,
                    credentials: [
                        {
                            type: 'password',
                            value: password,
                            temporary: false,
                        },
                    ],
                    attributes,
                },
            );
        } catch (e) {
            if (e?.responseData?.errorMessage) {
                this.exceptionToUserValidationException(e);
            }
            throw e;
        }
    }

    private async updateLocalUser(userId: string, firstName: string, lastName: string): Promise<void> {
        await this.userService.updateUser(userId, { firstName, lastName });
        await this.userService.activateUser(userId);
    }

    private async acceptInvitation(invitation: typeof schema.invitations.$inferSelect): Promise<void> {
        await this.invitationRepository.update(invitation.id, {
            isAccepted: true,
        });

        try {
            await Promise.all(
                invitation.roleIds?.map((roleId) => this.userService.assignRoleToUser(invitation.userId, roleId)),
            );
        } catch (error) {
            throw error;
        }
    }

    private encodeInvitationToken(data: any) {
        const appKey = this.configService.get('APP_KEY');

        const token = this.jwtService.sign(data, {
            secret: appKey,
            expiresIn: '24h',
            algorithm: 'HS256',
        });

        return token;
    }

    private async decodeInvitationToken(token: string) {
        const decoded = this.jwtService.verify(token, {
            secret: this.configService.get('APP_KEY'),
            algorithms: ['HS256'],
        });

        return decoded;
    }

    private async validateOrganizations(organizationIds: string[]): Promise<OrganizationDto[]> {
        const organizations: OrganizationDto[] = [];
        for (const organizationId of organizationIds) {
            const organization = await this.organizationsService.getOrganizationById(organizationId);
            if (!organization) {
                throw new Error(`Organization with id ${organizationId} not found`);
            }
            organizations.push(organization);
        }
        return organizations;
    }

    private exceptionToUserValidationException(exception: IKeycloakUserValidationException) {
        throw new UserValidationException(keycloakErrorMessageToErrorMessage(exception.responseData.errorMessage));
    }

    private async getOrganizationsInfo(
        organizationIds: string[],
        markAllAsNew: boolean = false,
        skipNotFoundOrganizations: boolean = false,
    ): Promise<NotificationOrganizationDto[]> {
        const organizations: NotificationOrganizationDto[] = [];

        for (const orgId of organizationIds) {
            try {
                const organization = await this.organizationsService.getOrganizationById(orgId);
                if (organization) {
                    const item = new NotificationOrganizationDto();
                    item.name = organization.name;
                    item.isNew = markAllAsNew || true; // By default all are new, or explicitly mark all as new
                    item.locale = organization.locale;
                    organizations.push(item);
                }
            } catch (error) {
                if (skipNotFoundOrganizations && error instanceof NotFoundException) {
                    continue;
                }
                throw error;
            }
        }

        return organizations;
    }
}
