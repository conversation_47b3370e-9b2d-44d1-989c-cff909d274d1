import { BadRequestException } from '@nestjs/common';
import { I18nContext } from 'nestjs-i18n';

import { I18nPath } from '../../../../i18n/generated/i18n.generated';

export class UsageOfDisablingReasonProhibitedException extends BadRequestException {
    constructor(reason: string) {
        super(
            I18nContext.current().t<I18nPath>('errors.USER.USAGE_OF_DISABLING_REASON_PROHIBITED', {
                args: {
                    reason,
                },
            }),
        );
    }
}
