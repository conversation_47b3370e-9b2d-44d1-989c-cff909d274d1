import { BadRequestException } from '@nestjs/common';
import { I18nContext } from 'nestjs-i18n';

import { I18nPath } from '../../../../i18n/generated/i18n.generated';

export class InvitationForUserNotFoundException extends BadRequestException {
    constructor(userId: string) {
        super(
            I18nContext.current().t<I18nPath>('errors.INVITATION.INVITATION_FOR_USER_NOT_FOUND', {
                args: {
                    userId,
                },
            }),
        );
    }
}
