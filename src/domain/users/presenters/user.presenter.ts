import { Injectable } from '@nestjs/common';
import { InferSelectModel } from 'drizzle-orm';

import * as schema from '../../../../drizzle/schema';
import { HttpMethodsEnum } from '../../common/enums';
import { Context } from '../../common/interfaces';
import { ActionType, ResourceType } from '../entities';
import { UserModel } from '../models/user.model';

type UserDrizzle = Partial<InferSelectModel<typeof schema.users>> & {
    role?: Pick<InferSelectModel<typeof schema.roles>, 'id' | 'name'>;
    roleId?: string;
    roleName?: string;
    ignoreAdvPerms?: boolean;
};
@Injectable()
export class UserPresenter {
    toFullModel(context: Context, entity: UserDrizzle): UserModel {
        const model = new UserModel();
        model.id = entity.id;
        model.createdAt = this.adaptDate(entity.createdAt);
        model.updatedAt = this.adaptDate(entity.updatedAt);
        model.email = entity.email;
        model.firstName = entity.firstName;
        model.lastName = entity.lastName;
        model.isActive = entity.isActive;
        model.keycloakId = entity.keycloakId;
        model.lastLoginAt = this.adaptDate(entity.lastLoginAt);
        model.inviteExpiresAt = this.adaptDate(entity.inviteExpiresAt);
        model.status = entity.status;
        model.locale = entity.locale;
        model.phone = entity.phone;
        model.roleId = entity.roleId;
        model.roleName = entity.roleName;
        model.role = entity.role;
        model.timezone = entity.timezone;
        model.ignoreAdvPerms = entity.ignoreAdvPerms ?? false;

        model._actions = {};

        const permissions = context.permissions;

        const removeInvitedAvailable =
            !!permissions.find(
                (permission) =>
                    (permission.actionType === ActionType.UPDATE &&
                        permission.resourceType === ResourceType.INVITED_USER) ||
                    (permission.actionType === ActionType.MANAGE &&
                        permission.resourceType === ResourceType.INVITED_USER) ||
                    (permission.actionType === ActionType.MANAGE && permission.resourceType === ResourceType.PLATFORM),
            ) && !entity.isActive;

        model._actions.removeInvited = {
            method: HttpMethodsEnum.POST,
            uri: '/users/invited/remove',
            disabled: !removeInvitedAvailable,
        };
        return model;
    }

    private adaptDate(date: string | Date | null | undefined): string | null {
        if (!date) {
            return null;
        }

        if (date instanceof Date) {
            return date.toISOString();
        }

        const dateObj = new Date(date);
        return dateObj.toISOString();
    }
}
