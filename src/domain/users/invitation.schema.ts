import { boolean, jsonb, pgTable, timestamp, varchar } from 'drizzle-orm/pg-core';

export const invitations = pgTable('invitations', {
    id: varchar({ length: 255 }).primaryKey().notNull(),
    userId: varchar('user_id', { length: 255 }).notNull(),
    email: varchar({ length: 255 }).notNull(),
    organizationIds: jsonb('organization_ids').$type<string[]>().notNull(),
    token: varchar({ length: 255 }).notNull(),
    isAccepted: boolean('is_accepted').default(false).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }).notNull(),
    expiresAt: timestamp('expires_at', { withTimezone: true, mode: 'string' }).notNull(),
    roleIds: jsonb('role_ids').$type<string[]>().notNull(),
});
