import { KeycloakErrorMessageEnum } from '../enums/keycloak-error-message.enum';
import { UserValidationErrorMessageEnum } from '../enums/user-validation-error-message.enum';

export const keycloakErrorMessageToErrorMessage = (keycloakErrorMessage: KeycloakErrorMessageEnum | string): string => {
    switch (keycloakErrorMessage) {
        case KeycloakErrorMessageEnum.PERSON_NAME_INVALID_CHARACTER:
            return UserValidationErrorMessageEnum.NAME_CONTAINS_INVALID_CHARACTERS;
        default:
            return keycloakErrorMessage;
    }
};
