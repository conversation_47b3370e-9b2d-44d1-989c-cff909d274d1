import { BadRequestException, Body, Controller, Get, Logger, <PERSON>m, Post, Req, Res } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ApiOperation } from '@nestjs/swagger';
import { validate } from 'class-validator';
import { Response } from 'express';

import { DisableAuthenticatedGuard } from '../../../auth/authenticated.guard';
import { RequestContext } from '../../../auth/context.decorator';
import { buildOpenIdClient } from '../../../auth/oidc.strategy';
import { KeycloakAdminClientService } from '../../../auth/services/keycloak-admin-client.service';
import { DisablePermissionsGuard, RequireResourceAction } from '../../../rbac/permissions.decorator';
import { BaseController } from '../../common/controllers/base.controller';
import { Context } from '../../common/interfaces';
import { RequestInterface } from '../../common/interfaces/request.interface';
import { OrganizationsService } from '../../organizations';
import {
    AssignRoleToUserDto,
    InviteUserDto,
    RegisterUserDto,
    ResendInvitationDto,
    UserChangePasswordRequestDto,
    UserDisableDto,
    UserUnlinkDto,
    UserUpdateRequestDto,
    UserUpdateResponseDto,
} from '../dto';
import { InvitedUserRemoveDto } from '../dto/request/invited-user-remove.dto';
import { UserEnableDto } from '../dto/request/user-enable.dto';
import { ActionType, ResourceType } from '../entities/permission.entity';
import { InvalidCurrentPasswordException } from '../exceptions/invalid-current-password.exception';
import { UsageOfDisablingReasonProhibitedException } from '../exceptions/usage-of-disabling-reason-prohibited.exception';
import { UserDisabledException } from '../exceptions/user-disabled.exception';
import { UserIdIsNotProvidedException } from '../exceptions/user-id-is-not-provided.exception';
import { UserNotFoundException } from '../exceptions/user-not-found.exception';
import { UserUnknownException } from '../exceptions/user-unknown.exception';
import { InvitationService } from '../invitation.service';
import { UserService } from '../services';

@Controller('users')
export class UsersController extends BaseController {
    private readonly logger = new Logger(UsersController.name);

    constructor(
        private userService: UserService,
        private invitationService: InvitationService,
        private configService: ConfigService,
        private keycloakAdminClientService: KeycloakAdminClientService,
        private readonly organizationsService: OrganizationsService,
    ) {
        super();
    }

    @RequireResourceAction({ resource: ResourceType.PLATFORM, action: ActionType.MANAGE })
    @Get('/')
    async getUsers(@Res() res: Response) {
        const users = await this.userService.getUsers();

        return this.sendOk(res, users);
    }

    @DisablePermissionsGuard()
    @Get('me/permissions')
    async getUserPermissions(@Req() req: RequestInterface, @RequestContext() context: Context) {
        return await this.userService.getUserRolePermissions(context.user.id, context.organizationId);
    }

    @DisablePermissionsGuard()
    @Get('me/roles')
    async getUserRoles(@Req() req: RequestInterface, @RequestContext() context: Context) {
        return await this.userService.getUserRoles(context.user.id, context.organizationId);
    }

    @RequireResourceAction({ resource: ResourceType.USER, action: ActionType.CREATE })
    @Post('invite')
    async inviteUser(@Body() inviteUserDto: InviteUserDto, @Res() res: Response) {
        try {
            await this.invitationService.inviteUser(
                inviteUserDto.email,
                inviteUserDto.organizationIds,
                inviteUserDto.roleId,
            );

            await this.organizationsService.updateAdvancedPermissions();

            return this.sendCreated(res, { success: true });
        } catch (e) {
            throw new BadRequestException(e.message);
        }
    }

    @RequireResourceAction({ resource: ResourceType.USER, action: ActionType.CREATE })
    @Post('invite/resend')
    async resendInvitation(@Body() resendInvitationDto: ResendInvitationDto, @Res() res: Response) {
        try {
            await this.invitationService.resendInvitation(
                resendInvitationDto.email,
                resendInvitationDto.organizationIds,
            );
            return this.sendOk(res, { success: true });
        } catch (e) {
            throw new BadRequestException(e.message);
        }
    }

    @RequireResourceAction({ resource: ResourceType.USER, action: ActionType.UPDATE })
    @Post('disable')
    async disableUser(@Res() res: Response, @Body() body: UserDisableDto) {
        await this.validateBody(body);

        const userId = body.userId;
        if (!userId) {
            throw new UserIdIsNotProvidedException();
        }

        const reason = body.reason || null;
        if (reason === 'invitation') {
            throw new UsageOfDisablingReasonProhibitedException(reason);
        }

        const user = await this.userService.getUserById(userId);
        const keycloakUser = await this.keycloakAdminClientService.getUser(user.keycloakId);
        if (!keycloakUser) {
            throw new UserNotFoundException();
        }

        await this.userService.deactivateUser(userId);

        // Saving reason requires passing attributes, so we need to pull them from the user
        let attributes = keycloakUser.attributes ?? null;
        if (!attributes && reason) {
            attributes = {};
        }
        if (reason) {
            attributes['disabledReason'] = reason;
        } else {
            // Delete previously set disabledReason attribute
            if (attributes && attributes['disabledReason']) {
                delete attributes['disabledReason'];
            }
        }

        // If we are passing attributes, we need to pass them together with email
        await this.keycloakAdminClientService.updateUser(user.keycloakId, {
            email: keycloakUser.email,
            enabled: false,
            ...(attributes !== null && { attributes: attributes }),
        });

        return this.sendOk(res, { message: 'User disabled successfully' });
    }

    @RequireResourceAction({ resource: ResourceType.USER, action: ActionType.UPDATE })
    @Post('enable')
    async enableUser(@Res() res: Response, @Body() body: UserEnableDto) {
        await this.validateBody(body);

        const userId = body.userId;
        if (!userId) {
            throw new BadRequestException('User id is not provided');
        }

        const user = await this.userService.getUserById(userId);
        const keycloakUser = await this.keycloakAdminClientService.getUser(user.keycloakId);
        if (!keycloakUser) {
            throw new BadRequestException('User not found');
        }

        await this.userService.activateUser(userId);

        // Saving reason requires passing attributes, so we need to pull them from the user
        let attributes = keycloakUser.attributes ?? null;
        if (!attributes) {
            attributes = {};
        }

        if ('disabledReason' in attributes) {
            delete attributes['disabledReason'];
        }

        // If we are passing attributes, we need to pass them together with email
        await this.keycloakAdminClientService.updateUser(user.keycloakId, {
            email: keycloakUser.email,
            enabled: true,
            ...(attributes !== null && { attributes: attributes }),
        });

        return this.sendOk(res, { message: 'User enabled successfully' });
    }

    @RequireResourceAction({ resource: ResourceType.USER, action: ActionType.UPDATE })
    @Post('unlink')
    async unlinkFromOrg(@Res() res: Response, @Body() body: UserUnlinkDto) {
        await this.validateBody(body);
        await this.userService.unlinkUserFromRole(body.userId, body.roleId);
        return this.sendOk(res, { message: 'User unlinked successfully' });
    }

    @RequireResourceAction({ resource: ResourceType.INVITED_USER, action: ActionType.UPDATE })
    @ApiOperation({
        summary: 'Remove user from organization',
    })
    @Post('invited/remove')
    async removeInvitedUser(@Res() res: Response, @Body() body: InvitedUserRemoveDto) {
        await this.userService.removeInvitedUser(body.userId, body.organizationId);
        return this.sendOk(res, { message: 'User removed successfully' });
    }

    @DisablePermissionsGuard()
    @Get('me')
    async getUserMe(@Req() req: RequestInterface, @Res() res: Response) {
        const keycloakUserId = req.user.sub ?? null;
        if (!keycloakUserId) {
            throw new UserUnknownException();
        }

        const user = await this.userService.getUserByKeycloakId(keycloakUserId);
        if (!user) {
            throw new UserNotFoundException();
        }

        return this.getUserById(res, user.id);
    }

    @RequireResourceAction({ resource: ResourceType.USER, action: ActionType.READ })
    @Get(':userId')
    // TODO: check if the user belongs to the administer organization
    async getUserById(@Res() res: Response, @Param('userId') userId: string) {
        const user = await this.userService.getUserById(userId);
        if (!user) {
            throw new UserNotFoundException();
        }

        const response = {
            id: user.id,
            isActive: user.isActive,
            status: user.status,
            email: user.email,
            firstName: user.firstName ?? null,
            lastName: user.lastName ?? null,
            phone: user.phone ?? null,
            locale: user.locale ?? 'en',
            timezone: user.timezone ?? 'UTC',
        } as UserUpdateResponseDto;

        return this.sendOk(res, response);
    }

    @DisablePermissionsGuard()
    @Post('me')
    async updateUserMe(@Res() res: Response, @Body() body: UserUpdateRequestDto, @RequestContext() context: Context) {
        return this.updateUser(res, context.user.id, body);
    }

    @DisablePermissionsGuard()
    @Post('me/password')
    async changePassword(
        @Res() res: Response,
        @Body() body: UserChangePasswordRequestDto,
        @RequestContext() context: Context,
    ) {
        try {
            await this.keycloakAdminClientService.signIn({
                email: context.user.email,
                password: body.currentPassword,
            });
        } catch {
            throw new InvalidCurrentPasswordException();
        }

        await this.keycloakAdminClientService.changePassword({
            keycloakUserId: context.user.keycloakId,
            newPassword: body.newPassword,
            confirmPassword: body.confirmPassword,
        });

        const data = await this.keycloakAdminClientService.signIn({
            email: context.user.email,
            password: body.newPassword,
        });

        return this.sendOk(res, data);
    }

    @DisableAuthenticatedGuard()
    @DisablePermissionsGuard()
    @Post('register')
    async registerUser(@Body() registerUserDto: RegisterUserDto, @Res() res: Response) {
        try {
            const result = await this.invitationService.applyInvitation(
                registerUserDto.token,
                registerUserDto.firstName,
                registerUserDto.lastName,
                registerUserDto.password,
            );

            try {
                const client = await buildOpenIdClient();
                const tokenSet = await client.grant({
                    grant_type: 'password',
                    username: result.email,
                    password: registerUserDto.password,
                    client_id: this.configService.get('KEYCLOAK_CLIENT_ID'),
                    client_secret: this.configService.get('KEYCLOAK_CLIENT_SECRET'),
                    scope: 'openid profile email offline_access',
                });

                const data = {
                    success: true,
                    email: result.email,
                    access_token: tokenSet.access_token,
                    refresh_token: tokenSet.refresh_token,
                    refresh_expires_in: 1800,
                    id_token: tokenSet.id_token,
                    expires_in: tokenSet.expires_in,
                    organizationIds: result.organizationIds,
                };

                return this.sendOk(res, data);
            } catch (tokenError) {
                this.logError('Auto-login failed', tokenError);
                return this.sendOk(res, {
                    success: true,
                    email: result.email,
                    autoLogin: false,
                });
            }
        } catch (e) {
            throw new BadRequestException(e.message);
        }
    }

    @RequireResourceAction({ resource: ResourceType.USER, action: ActionType.UPDATE })
    @Post(':userId')
    // TODO: check if the user belongs to the administer organization
    async updateUser(@Res() res: Response, @Param('userId') userId: string, @Body() body: UserUpdateRequestDto) {
        const user = await this.userService.getUserById(userId);
        if (!user) {
            throw new UserNotFoundException();
        }

        if (!user.isActive) {
            throw new UserDisabledException();
        }

        const updatedUser = await this.userService.updateUser(userId, body);

        const response = {
            id: updatedUser.id,
            isActive: updatedUser.isActive,
            email: updatedUser.email,
            firstName: updatedUser.firstName ?? null,
            lastName: updatedUser.lastName ?? null,
            phone: updatedUser.phone ?? null,
        } as UserUpdateResponseDto;

        return this.sendOk(res, response);
    }

    // TODO: This need to be available only to administrative user
    @RequireResourceAction({ resource: ResourceType.USER, action: ActionType.UPDATE })
    @Post(':userId/roles')
    async assignRoleToUser(@Res() res: Response, @Param('userId') userId: string, @Body() body: AssignRoleToUserDto) {
        const userRole = await this.userService.assignRoleToUser(userId, body.roleId, body.ignoreAdvPerms);
        return this.sendCreated(res, {
            message: 'Role assigned successfully',
            userRole,
        });
    }

    private async validateBody(body: any) {
        if (!body || Object.keys(body).length === 0) {
            throw new BadRequestException('Request body is empty');
        }

        const errors = await validate(body);
        if (errors.length > 0) {
            throw new BadRequestException(errors);
        }
    }

    private logError(message: string, error: any) {
        console.error(`${message}: ${error.message || error}`);
    }
}
