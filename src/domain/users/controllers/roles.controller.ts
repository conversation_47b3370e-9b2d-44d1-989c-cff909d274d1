import {
    Body,
    Controller,
    Delete,
    Get,
    HttpStatus,
    Logger,
    NotFoundException,
    Param,
    Patch,
    Post,
    Put,
    Res,
} from '@nestjs/common';
import { ApiBadRequestResponse, ApiOkResponse } from '@nestjs/swagger';
import { Response } from 'express';

import { RequestContext } from '../../../auth/context.decorator';
import { DisablePermissionsGuard, RequireResourceAction } from '../../../rbac/permissions.decorator';
import { BaseController } from '../../common/controllers/base.controller';
import { Context } from '../../common/interfaces';
import { ErrorModel } from '../../common/models/error.model';
import { CreateRoleDto, UpdateRoleDto } from '../dto';
import { ActionType, ResourceType } from '../entities/permission.entity';
import { NotAllowedToUpdateRoleException } from '../exceptions/not-allowed-to-update-role.exception';
import { RoleIsAssignedToUsersException } from '../exceptions/role-is-assigned-to-users.exception';
import { RoleNotFoundException } from '../exceptions/role-not-found.exception';
import { PermissionsModel, RoleListModel, RoleModel } from '../models';
import { RolePermissionsService, RolesService, UserRoleService, UserService } from '../services';

@Controller('roles')
export class RolesController extends BaseController {
    private readonly logger = new Logger(RolesController.name);

    constructor(
        private readonly userService: UserService,
        private readonly rolesService: RolesService,
        private readonly rolePermissionService: RolePermissionsService,
        private readonly userRoleService: UserRoleService,
    ) {
        super();
    }

    @DisablePermissionsGuard()
    @RequireResourceAction({ resource: ResourceType.ROLE, action: ActionType.UPDATE })
    @Get('organizations/:organizationId')
    @ApiOkResponse({
        description: 'Roles list',
        type: RoleListModel,
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async getRoles(@Res() res: Response, @Param('organizationId') organizationId: string) {
        const model = await this.rolesService.getAllRoles(organizationId);

        return this.sendOk(res, model.getData());
    }

    @DisablePermissionsGuard()
    @RequireResourceAction({ resource: ResourceType.ROLE, action: ActionType.CREATE })
    @Post()
    @ApiOkResponse({
        description: 'Create role',
        type: RoleModel,
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async createRole(@Res() res: Response, @RequestContext() context: Context, @Body() body: CreateRoleDto) {
        const role = await this.rolesService.createRole(context, body);
        await this.rolePermissionService.linkPermissionsToRole(context, {
            roleId: role.id,
            permissionIds: body.permissionIds,
        });

        return this.sendCreated(res, role);
    }

    @DisablePermissionsGuard()
    @RequireResourceAction({ resource: ResourceType.ROLE, action: ActionType.UPDATE })
    @Patch(':roleId')
    @ApiOkResponse({
        description: 'Update role',
        type: RoleModel,
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async updateRole(
        @Res() res: Response,
        @RequestContext() context: Context,
        @Param('roleId') roleId: string,
        @Body() body: UpdateRoleDto,
    ) {
        let role = await this.rolesService.getById(context, roleId);
        if (!role) {
            throw new RoleNotFoundException();
        }

        if (role.organization.id !== context.organizationId) {
            throw new NotAllowedToUpdateRoleException();
        }

        role = await this.rolesService.updateRole(context, roleId, body);
        if (!body.permissionIds) {
            return this.sendCreated(res, role);
        }

        const assignedPermissions = await this.rolePermissionService.getPermissionsByRoleId(context, { roleId });
        const newPermissions = body.permissionIds.filter((permissionId) => {
            return !assignedPermissions.find((permission) => permission.permission.id === permissionId);
        });
        const removedPermissions = assignedPermissions.filter((permission) => {
            return !body.permissionIds.find((permissionId) => permissionId === permission.permission.id);
        });

        if (newPermissions.length) {
            await this.rolePermissionService.linkPermissionsToRole(context, {
                roleId: role.id,
                permissionIds: newPermissions,
            });
        }
        if (removedPermissions.length) {
            await this.rolePermissionService.unlinkPermissionsFromRole(context, {
                roleId: role.id,
                permissionIds: removedPermissions.map((permission) => permission.permission.id),
            });
        }

        return this.sendCreated(res, role);
    }

    @DisablePermissionsGuard()
    @RequireResourceAction({ resource: ResourceType.ROLE, action: ActionType.UPDATE })
    @Delete(':roleId')
    @ApiOkResponse({
        description: 'Delete role',
        type: RoleModel,
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async deleteRole(@Res() res: Response, @RequestContext() context: Context, @Param('roleId') roleId: string) {
        const role = await this.rolesService.getById(context, roleId);
        if (!role) {
            throw new RoleNotFoundException();
        }

        if (role.organization.id !== context.organizationId) {
            throw new NotAllowedToUpdateRoleException();
        }

        const roleUsageCount = await this.userRoleService.countRoleUsage(context, { roleId });
        if (roleUsageCount > 0) {
            throw new RoleIsAssignedToUsersException();
        }

        const assignedPermissions = await this.rolePermissionService.getPermissionsByRoleId(context, { roleId });
        await this.rolePermissionService.unlinkPermissionsFromRole(context, {
            roleId: role.id,
            permissionIds: assignedPermissions.map((permission) => permission.permission.id),
        });

        await this.rolesService.deleteRole(context, roleId);

        return this.sendOk(res, role);
    }

    @DisablePermissionsGuard()
    @RequireResourceAction({ resource: ResourceType.ROLE, action: ActionType.UPDATE })
    @Put(':roleId/permissions')
    @ApiOkResponse({
        description: 'Permissions updated',
        type: PermissionsModel,
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async assignPermissionsToRole(
        @Res() res: Response,
        @Param('roleId') roleId: string,
        @Body() body: PermissionsModel,
    ) {
        try {
            const permissions = PermissionsModel.create(body);

            const role = await this.userService.getRoleById(roleId);
            const rolePermissions = await this.userService.updateRolePermissions(role, permissions);
            res.send(rolePermissions);
        } catch (e) {
            this.logger.error({ err: e }, 'Error assigning role');
            throw e;
        }
    }

    @DisablePermissionsGuard()
    @RequireResourceAction({ resource: ResourceType.ROLE, action: ActionType.READ })
    @Get(':roleId/permissions')
    @ApiOkResponse({
        description: 'Permissions',
        type: PermissionsModel,
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async getPermissionsByRole(@Res() res: Response, @Param('roleId') roleId: string) {
        try {
            const role = await this.userService.getRoleById(roleId);
            const rolePermissions = await this.userService.getRolePermissions(role);
            res.send(rolePermissions);
        } catch (e) {
            this.logger.error({ err: e }, 'Error getting role permissions');
            if (e instanceof NotFoundException) {
                return res.status(HttpStatus.NOT_FOUND).send({ message: e.message });
            }
            res.status(HttpStatus.BAD_REQUEST).send({ message: 'Error getting role permissions' });
        }
    }
}
