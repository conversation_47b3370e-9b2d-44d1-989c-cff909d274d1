import { PaginationMetaModel } from '../../common/pagination/pagination.model';
import { RoleListSchema } from '../schemas/role-list.schema';
import { RoleModel } from './role.model';

export class RoleListModel {
    zodSchema = RoleListSchema;

    private data: RoleModel[] = [];
    private meta: PaginationMetaModel = new PaginationMetaModel();

    public setData(data: RoleModel[], meta: PaginationMetaModel) {
        this.data = data;
        this.meta = meta;
    }

    public getData() {
        return this.zodSchema.parse({
            data: this.data,
            meta: this.meta,
        });
    }
}
