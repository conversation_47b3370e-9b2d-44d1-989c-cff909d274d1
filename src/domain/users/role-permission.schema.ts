import { relations } from 'drizzle-orm';
import { foreignKey, pgTable, timestamp, uuid } from 'drizzle-orm/pg-core';

import { permissions } from './permission.schema';
import { roles } from './role.schema';

export const rolePermissions = pgTable(
    'role_permissions',
    {
        id: uuid().primaryKey().notNull(),
        roleId: uuid('role_id').notNull(),
        permissionId: uuid('permission_id').notNull(),
        createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }).notNull(),
    },
    (table) => [
        foreignKey({
            columns: [table.roleId],
            foreignColumns: [roles.id],
            name: 'role_permissions_role_id_foreign',
        }).onUpdate('cascade'),
        foreignKey({
            columns: [table.permissionId],
            foreignColumns: [permissions.id],
            name: 'role_permissions_permission_id_foreign',
        }).onUpdate('cascade'),
    ],
);

export const rolePermissionsRelations = relations(rolePermissions, ({ one }) => ({
    role: one(roles, {
        fields: [rolePermissions.roleId],
        references: [roles.id],
    }),
    permission: one(permissions, {
        fields: [rolePermissions.permissionId],
        references: [permissions.id],
    }),
}));
