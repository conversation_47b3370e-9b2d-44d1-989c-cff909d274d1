import { InjectRepository } from '@mikro-orm/nestjs';
import { Injectable } from '@nestjs/common';

import { Context } from '../../common/interfaces';
import { RolePermission } from '../entities/role-permissions.entity';
import { RoleNotFoundException } from '../exceptions/role-not-found.exception';
import { RolePermissionRepository } from '../repositories';
import { RolesService } from './roles.service';

@Injectable()
export class RolePermissionsService {
    constructor(
        @InjectRepository(RolePermission)
        private readonly rolePermissionRepository: RolePermissionRepository,
        private readonly rolesService: RolesService,
    ) {}

    async linkPermissionsToRole(
        context: Context,
        { roleId, permissionIds }: { roleId: string; permissionIds: string[] },
    ) {
        const role = await this.rolesService.getById(context, roleId);
        if (!role) {
            throw new RoleNotFoundException();
        }

        const entries = await this.rolePermissionRepository.linkPermissionsToRole({
            role,
            permissionIds,
        });

        return entries;
    }

    async unlinkPermissionsFromRole(
        context: Context,
        { roleId, permissionIds }: { roleId: string; permissionIds: string[] },
    ) {
        const role = await this.rolesService.getById(context, roleId);
        if (!role) {
            throw new RoleNotFoundException();
        }

        const entries = await this.rolePermissionRepository.unlinkPermissionsFromRole({
            role,
            permissionIds,
        });

        return entries;
    }

    async getPermissionsByRoleId(context: Context, { roleId }: { roleId: string }) {
        return this.rolePermissionRepository.findAll({
            where: {
                role: roleId,
            },
        });
    }
}
