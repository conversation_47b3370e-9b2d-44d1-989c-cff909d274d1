import { InjectRepository } from '@mikro-orm/nestjs';
import { Inject, Injectable } from '@nestjs/common';

import { Context } from '../../common/interfaces';
import { Organization } from '../../organizations';
import { OrganizationRepository } from '../../organizations/repositories';
import { CreateRoleDto, UpdateRoleDto } from '../dto';
import { ActionType, Permission, ResourceType } from '../entities/permission.entity';
import { RolePermission } from '../entities/role-permissions.entity';
import { RoleListModel } from '../models';
import { PermissionRepository, RolePermissionRepository, RoleRepository } from '../repositories';
import { RoleDrizzleRepository } from '../repositories/role.drizzle.repository';

@Injectable()
export class RolesService {
    constructor(
        private readonly roleRepository: RoleRepository,
        @InjectRepository(Permission)
        private readonly permissionRepository: PermissionRepository,
        @InjectRepository(RolePermission)
        private readonly rolePermissionRepository: RolePermissionRepository,
        @InjectRepository(Organization)
        private readonly organizationRepository: OrganizationRepository,
        @Inject(RoleDrizzleRepository)
        private readonly roleDrizzleRepository: RoleDrizzleRepository,
    ) {}

    async createDefaultRoles({ organizationId }: { organizationId: string }) {
        const ownerPermissionNames = [
            'entity:read',
            'asset:read',
            'contact:read',
            'quote:manage',
            'quote_item:read',
            'quote_item:export',
            'contract:read',
            'contract_item:read',
            'contract_item:export',
            'user:manage',
            'organization:read',
            'user_organization:read',
        ];

        const approverPermissionNames = [
            'asset:read',
            'entity:read',
            'contact:read',
            'quote:manage',
            'quote_item:read',
            'quote_item:export',
            'contract:read',
            'contract_item:read',
            'contract_item:export',
        ];

        const requesterPermissionNames = [
            'asset:read',
            'entity:read',
            'contact:read',
            'quote:create',
            'quote:read',
            'quote:update',
            'quote:reject',
            'quote_item:read',
            'quote_item:export',
            'contract:read',
            'contract_item:read',
            'contract_item:export',
        ];

        const viewerPermissionNames = [
            'asset:read',
            'entity:read',
            'contact:read',
            'quote:read',
            'quote_item:read',
            'contract:read',
            'contract_item:read',
        ];

        const supportOperatorPermissionNames = [
            'asset:read',
            'entity:read',
            'contact:read',
            'quote:read',
            'quote_item:read',
            'contract:read',
            'contract_item:read',
            'user:manage',
            'organization:read',
            'user_organization:read',
        ];

        const [
            ownerPermissions,
            approverPermissions,
            requesterPermissions,
            viewerPermissions,
            supportOperatorPermissions,
        ] = await Promise.all([
            this.permissionRepository.findAll({
                where: {
                    name: {
                        $in: ownerPermissionNames,
                    },
                },
            }),
            this.permissionRepository.findAll({
                where: {
                    name: {
                        $in: approverPermissionNames,
                    },
                },
            }),
            this.permissionRepository.findAll({
                where: {
                    name: {
                        $in: requesterPermissionNames,
                    },
                },
            }),
            this.permissionRepository.findAll({
                where: {
                    name: {
                        $in: viewerPermissionNames,
                    },
                },
            }),
            this.permissionRepository.findAll({
                where: {
                    name: {
                        $in: supportOperatorPermissionNames,
                    },
                },
            }),
        ]);

        const organization = await this.organizationRepository.findOneOrFail({
            id: organizationId,
        });

        const [ownerRole, approverRole, requesterRole, viewerRole, supportOperatorRole] = await Promise.all([
            this.roleRepository.createRole({
                organization,
                name: 'Owner',
                description: 'The owner role enables the reseller to self manage user accounts for his organisation.',
            }),
            this.roleRepository.createRole({
                organization,
                name: 'Approver',
                description:
                    'This is the role for the users in the reseller organisation that are allowed to make a purchase.',
            }),
            this.roleRepository.createRole({
                organization,
                name: 'Requester',
                description:
                    'This is role is used for users that need quote management permission but do not have purchase auth.',
            }),
            this.roleRepository.createRole({
                organization,
                name: 'Viewer',
                description: 'This is the default role that only has viewing permission.',
            }),
            this.roleRepository.createRole({
                organization,
                name: 'Support Operator',
                description: 'This is the default role for Tesedi AM / support only',
            }),
        ]);

        await Promise.all([
            ...ownerPermissions.map((permission) => {
                this.rolePermissionRepository.createRolePermission({
                    permission,
                    role: ownerRole,
                });
            }),
            ...approverPermissions.map((permission) => {
                this.rolePermissionRepository.createRolePermission({
                    permission,
                    role: approverRole,
                });
            }),
            ...requesterPermissions.map((permission) => {
                this.rolePermissionRepository.createRolePermission({
                    permission,
                    role: requesterRole,
                });
            }),
            ...viewerPermissions.map((permission) => {
                this.rolePermissionRepository.createRolePermission({
                    permission,
                    role: viewerRole,
                });
            }),
            ...supportOperatorPermissions.map((permission) => {
                this.rolePermissionRepository.createRolePermission({
                    permission,
                    role: supportOperatorRole,
                });
            }),
        ]);
    }

    async getById(context: Context, id: string) {
        return this.roleRepository.findOneOrFail({
            id,
        });
    }

    async createRole(context: Context, { name, description }: CreateRoleDto) {
        return this.roleDrizzleRepository.createRole(name, description, context.organizationId);
    }

    async updateRole(context: Context, id: string, { name, description }: UpdateRoleDto) {
        return this.roleRepository.update({
            id,
            name,
            description,
        });
    }

    async deleteRole(context: Context, id: string) {
        await this.roleRepository.nativeDelete({ id });
    }

    async getAllRoles(organizationId: string) {
        const paginatedResult = await this.roleRepository.findPaginated(
            {
                organization: organizationId,
                visible: true,
            },
            {
                limit: 25,
            },
        );

        if (!paginatedResult) {
            return new RoleListModel();
        }

        const model = new RoleListModel();
        model.setData(paginatedResult.data, paginatedResult.meta);

        return model;
    }
}
