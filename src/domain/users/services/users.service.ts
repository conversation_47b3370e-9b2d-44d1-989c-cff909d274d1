import { Inject, Injectable, Logger } from '@nestjs/common';
import { and, eq, inArray } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { v4 as uuidv4 } from 'uuid';

import * as schema from '../../../../drizzle/schema';
import { DRIZZLE_PROVIDER } from '../../../drizzle/drizzle.provider';
import { BrevoService } from '../../common/services/email/brevo.service';
import { UserUpdateRequestDto } from '../dto/request/user-update.dto';
import { ActionType, ResourceType } from '../entities/permission.entity';
import { UserStatusEnum } from '../enums/user-status.enum';
import { InvitationForUserNotFoundException } from '../exceptions/invitation-for-user-not-found.exception';
import { RoleNotFoundException } from '../exceptions/role-not-found.exception';
import { UserNotFoundException } from '../exceptions/user-not-found.exception';
import { InvitationDrizzleRepository } from '../repositories/invitation.drizzle.repository';
import { PermissionDrizzleRepository } from '../repositories/permission.drizzle.repository';
import { RoleDrizzleRepository } from '../repositories/role.drizzle.repository';
import { UserDrizzleRepository } from '../repositories/user.drizzle.repository';

@Injectable()
export class UserService {
    private readonly logger = new Logger(UserService.name);

    constructor(
        @Inject(UserDrizzleRepository)
        private readonly userDrizzleRepository: UserDrizzleRepository,
        @Inject(PermissionDrizzleRepository)
        private readonly permissionDrizzleRepository: PermissionDrizzleRepository,
        @Inject(RoleDrizzleRepository)
        private readonly roleDrizzleRepository: RoleDrizzleRepository,
        @Inject(DRIZZLE_PROVIDER)
        private readonly db: NodePgDatabase<typeof schema>,
        @Inject(InvitationDrizzleRepository)
        private readonly invitationRepository: InvitationDrizzleRepository,
        private readonly brevoService: BrevoService,
    ) {}

    async getUserById(userId: string): Promise<typeof schema.users.$inferSelect> {
        return await this.userDrizzleRepository.getUserById(userId);
    }

    async getUserByEmail(email: string): Promise<typeof schema.users.$inferSelect> {
        return await this.userDrizzleRepository.getUserByEmail(email);
    }

    async getUsers(): Promise<(typeof schema.users.$inferSelect)[]> {
        return await this.db.select().from(schema.users);
    }

    async findOrCreateUser(
        keycloakId: string,
        email: string,
        firstName?: string,
        lastName?: string,
        locale?: string,
    ): Promise<typeof schema.users.$inferSelect> {
        const normalizedEmail = email.toLowerCase();
        const existingUser = await this.db
            .select()
            .from(schema.users)
            .where(eq(schema.users.keycloakId, keycloakId))
            .limit(1);

        const now = new Date().toISOString();

        if (existingUser.length === 0) {
            const [newUser] = await this.db
                .insert(schema.users)
                .values({
                    id: uuidv4(),
                    keycloakId,
                    email: normalizedEmail,
                    firstName,
                    lastName,
                    isActive: false,
                    status: UserStatusEnum.INACTIVE,
                    locale,
                    createdAt: now,
                    updatedAt: now,
                })
                .returning();

            return newUser;
        } else {
            const [updatedUser] = await this.db
                .update(schema.users)
                .set({
                    email: normalizedEmail,
                    firstName,
                    lastName,
                    updatedAt: now,
                })
                .where(eq(schema.users.keycloakId, keycloakId))
                .returning();

            return updatedUser;
        }
    }

    async updateLastLogin(userId: string): Promise<void> {
        const user = await this.db.select().from(schema.users).where(eq(schema.users.id, userId)).limit(1);
        if (user.length > 0) {
            await this.db
                .update(schema.users)
                .set({
                    lastLoginAt: new Date().toISOString(),
                })
                .where(eq(schema.users.id, userId));
        }
    }

    async unlinkUserFromRole(userId: string, roleId: string): Promise<void> {
        const user = await this.db.select().from(schema.users).where(eq(schema.users.id, userId)).limit(1);
        const role = await this.db.select().from(schema.roles).where(eq(schema.roles.id, roleId)).limit(1);

        if (user.length === 0) {
            throw new UserNotFoundException();
        }

        if (role.length === 0) {
            throw new RoleNotFoundException();
        }

        const userRole = await this.db
            .select()
            .from(schema.userRoles)
            .where(and(eq(schema.userRoles.userId, userId), eq(schema.userRoles.roleId, roleId)))
            .limit(1);

        if (userRole.length > 0) {
            await this.db.delete(schema.userRoles).where(eq(schema.userRoles.id, userRole[0].id));
        }
    }

    async assignRoleToUser(
        userId: string,
        roleId: string,
        ignoreAdvPerms: boolean = false,
    ): Promise<typeof schema.userRoles.$inferSelect> {
        const user = await this.db.select().from(schema.users).where(eq(schema.users.id, userId)).limit(1);
        const role = await this.db.select().from(schema.roles).where(eq(schema.roles.id, roleId)).limit(1);

        if (user.length === 0) {
            throw new UserNotFoundException();
        }

        if (role.length === 0) {
            throw new RoleNotFoundException();
        }

        // Check for existing assignment
        const existingAssignment = await this.db
            .select()
            .from(schema.userRoles)
            .where(
                and(
                    eq(schema.userRoles.userId, userId),
                    eq(schema.userRoles.roleId, roleId),
                    eq(schema.userRoles.ignoreAdvPerms, ignoreAdvPerms),
                ),
            )
            .limit(1);

        if (existingAssignment.length > 0) {
            return existingAssignment[0];
        }

        // Get organization ID from the role
        const organizationId = role[0].organizationId;

        // Find all roles from the same organization
        const organizationRoles = await this.db
            .select()
            .from(schema.roles)
            .where(eq(schema.roles.organizationId, organizationId));

        // Get all role IDs from the organization
        const orgRoleIds = organizationRoles.map((r) => r.id);

        // Find previous user roles in the same organization
        const previousRoles = await this.db
            .select()
            .from(schema.userRoles)
            .where(and(eq(schema.userRoles.userId, userId), inArray(schema.userRoles.roleId, orgRoleIds)));

        // Delete previous roles
        for (const previousRole of previousRoles) {
            await this.db.delete(schema.userRoles).where(eq(schema.userRoles.id, previousRole.id));
        }

        // Create new user role
        const now = new Date().toISOString();
        const [userRole] = await this.db
            .insert(schema.userRoles)
            .values({
                id: uuidv4(),
                userId,
                roleId,
                ignoreAdvPerms,
                createdAt: now,
            })
            .returning();

        return userRole;
    }

    async getUserByKeycloakId(keycloakId: string): Promise<typeof schema.users.$inferSelect> {
        return this.userDrizzleRepository.getUserByKeycloakId(keycloakId);
    }

    async getUserEntityByKeycloakId(keycloakId: string): Promise<typeof schema.users.$inferSelect> {
        return this.userDrizzleRepository.getUserByKeycloakId(keycloakId);
    }

    async getUserRolePermissions(
        userId: string,
        organizationId?: string,
    ): Promise<Record<ResourceType, Record<ActionType, boolean>>> {
        const permissions = await this.permissionDrizzleRepository.getPermissionsByUserAndOrganization(
            userId,
            organizationId,
        );

        const data = {} as Record<ResourceType, Record<ActionType, boolean>>;

        for (const resource of Object.values(ResourceType)) {
            data[resource] = {} as Record<ActionType, boolean>;
            for (const action of Object.values(ActionType)) {
                data[resource][action] = false;
            }
        }

        for (const permission of permissions) {
            data[permission.permissions.resourceType][permission.permissions.actionType] = true;
        }

        return data;
    }

    async getRolePermissions(
        role: typeof schema.roles.$inferSelect,
    ): Promise<Record<ResourceType, Record<ActionType, boolean>>> {
        const permissionsResult = await this.db
            .select({
                resourceType: schema.permissions.resourceType,
                actionType: schema.permissions.actionType,
            })
            .from(schema.permissions)
            .innerJoin(schema.rolePermissions, eq(schema.permissions.id, schema.rolePermissions.permissionId))
            .where(eq(schema.rolePermissions.roleId, role.id));

        const data = {} as Record<ResourceType, Record<ActionType, boolean>>;

        for (const resource of Object.values(ResourceType)) {
            data[resource] = {} as Record<ActionType, boolean>;
            for (const action of Object.values(ActionType)) {
                data[resource][action] = false;
            }
        }

        for (const permission of permissionsResult) {
            data[permission.resourceType as ResourceType][permission.actionType as ActionType] = true;
        }

        return data;
    }

    async getUserRoles(userId: string, organizationId?: string) {
        return await this.roleDrizzleRepository.getUserRoles(userId, organizationId);
    }

    async deactivateUser(userId: string): Promise<void> {
        const user = await this.db.select().from(schema.users).where(eq(schema.users.id, userId)).limit(1);
        if (user.length === 0) {
            throw new UserNotFoundException();
        }

        await this.db
            .update(schema.users)
            .set({
                isActive: false,
                status: UserStatusEnum.INACTIVE,
                updatedAt: new Date().toISOString(),
            })
            .where(eq(schema.users.id, userId));
    }

    async activateUser(userId: string): Promise<void> {
        const user = await this.db.select().from(schema.users).where(eq(schema.users.id, userId)).limit(1);
        if (user.length === 0) {
            throw new UserNotFoundException();
        }

        await this.db
            .update(schema.users)
            .set({
                isActive: true,
                status: UserStatusEnum.ACTIVE,
                updatedAt: new Date().toISOString(),
            })
            .where(eq(schema.users.id, userId));
    }

    async hasRole(userId: string, roleId: string): Promise<boolean> {
        const userRoles = await this.db
            .select()
            .from(schema.userRoles)
            .where(and(eq(schema.userRoles.userId, userId), eq(schema.userRoles.roleId, roleId)))
            .limit(1);

        return userRoles.length > 0;
    }

    async updateUser(userId: string, userUpdateDto: UserUpdateRequestDto): Promise<typeof schema.users.$inferSelect> {
        const user = await this.db.select().from(schema.users).where(eq(schema.users.id, userId)).limit(1);
        if (user.length === 0) {
            throw new UserNotFoundException();
        }
        const existingUser = user[0];

        let normalizedEmail = existingUser.email.toLowerCase();

        // Normalize email if it's being updated
        const updateData = { ...userUpdateDto };
        if (updateData.email) {
            normalizedEmail = updateData.email.toLowerCase();
            updateData.email = normalizedEmail;
        }

        const [updatedUser] = await this.db
            .update(schema.users)
            .set({
                ...updateData,
                updatedAt: new Date().toISOString(),
            })
            .where(eq(schema.users.id, userId))
            .returning();

        if (existingUser.isActive) {
            const contact = await this.brevoService.getContact(normalizedEmail);
            const firstName = updatedUser?.firstName ?? existingUser?.firstName;
            const lastName = updatedUser?.lastName ?? existingUser?.lastName;
            const phone = updatedUser?.phone ?? existingUser?.phone;
            const locale = updatedUser?.locale ?? existingUser?.locale;

            if (!contact) {
                this.logger.log(`Creating new contact for email ${normalizedEmail}`);
                await this.brevoService.createContact(normalizedEmail, firstName, lastName, phone, 'invited', locale);
            } else {
                this.logger.log(`Updating existing contact for email ${normalizedEmail}`);
                await this.brevoService.updateContact(contact.id, firstName, lastName, phone, 'invited', locale);
            }
        }

        return updatedUser;
    }

    async markUserMigrationComplete(userId: string): Promise<void> {
        await this.db
            .update(schema.users)
            .set({
                isMigrated: false,
                updatedAt: new Date().toISOString(),
            })
            .where(eq(schema.users.id, userId));
    }

    async getRoleById(roleId: string): Promise<typeof schema.roles.$inferSelect> {
        const role = await this.roleDrizzleRepository.findRoleById(roleId);
        if (!role) {
            throw new RoleNotFoundException();
        }
        return role;
    }

    async updateRolePermissions(
        roleData: typeof schema.roles.$inferSelect,
        permissions: Partial<Record<ResourceType, Partial<Record<ActionType, boolean>>>>,
    ): Promise<Record<ResourceType, Record<ActionType, boolean>>> {
        for (const resource of Object.values(ResourceType)) {
            for (const action of Object.values(ActionType)) {
                const hasPermission = permissions[resource]?.[action] ?? false;

                const permission = await this.permissionDrizzleRepository.getPermissionByResourceAndAction(
                    resource as ResourceType,
                    action as ActionType,
                );
                if (!permission) {
                    continue;
                }

                const rolePermissions = await this.db
                    .select()
                    .from(schema.rolePermissions)
                    .where(
                        and(
                            eq(schema.rolePermissions.roleId, roleData.id),
                            eq(schema.rolePermissions.permissionId, permission.id),
                        ),
                    )
                    .limit(1);

                const rolePermission = rolePermissions.length > 0 ? rolePermissions[0] : null;

                if (!hasPermission && rolePermission) {
                    await this.db
                        .delete(schema.rolePermissions)
                        .where(eq(schema.rolePermissions.id, rolePermission.id));
                } else if (hasPermission && !rolePermission) {
                    await this.db.insert(schema.rolePermissions).values({
                        id: uuidv4(),
                        roleId: roleData.id,
                        permissionId: permission.id,
                        createdAt: new Date().toISOString(),
                    });
                }
            }
        }

        const permissionsResult = await this.db
            .select({
                resourceType: schema.permissions.resourceType,
                actionType: schema.permissions.actionType,
            })
            .from(schema.permissions)
            .innerJoin(schema.rolePermissions, eq(schema.permissions.id, schema.rolePermissions.permissionId))
            .where(eq(schema.rolePermissions.roleId, roleData.id));

        const data = {} as Record<ResourceType, Record<ActionType, boolean>>;

        for (const resource of Object.values(ResourceType)) {
            data[resource] = {} as Record<ActionType, boolean>;
            for (const action of Object.values(ActionType)) {
                data[resource][action] = false;
            }
        }

        for (const permission of permissionsResult) {
            data[permission.resourceType as ResourceType][permission.actionType as ActionType] = true;
        }

        return data;
    }

    async removeInvitedUser(userId: string, organizationId: string): Promise<void> {
        const invitation = await this.invitationRepository.findByUserIdAndOrganizationId(userId, organizationId);

        if (!invitation) {
            throw new InvitationForUserNotFoundException(userId);
        }

        if (invitation.organizationIds?.length > 0) {
            await this.invitationRepository.updateById(invitation.id, {
                expiresAt: new Date().toISOString(),
                organizationIds: invitation.organizationIds?.filter((orgId) => orgId !== organizationId),
            });
        }
    }

    // These methods are no longer needed with Drizzle ORM
    // Each operation is executed immediately
    async persistAndFlush(_entity: any): Promise<void> {
        // No-op - Drizzle doesn't have a concept of entity persistence
        // Each insert/update is executed immediately
    }

    async flushChanges(): Promise<void> {
        // No-op - Drizzle doesn't have a concept of flushing changes
        // Each operation is executed immediately
    }
}
