import { relations } from 'drizzle-orm';
import { boolean, index, pgTable, timestamp, unique, uuid, varchar } from 'drizzle-orm/pg-core';

import { userRoles } from './user-role.schema';

export const users = pgTable(
    'users',
    {
        id: uuid().primaryKey().notNull(),
        keycloakId: varchar('keycloak_id', { length: 255 }).notNull(),
        email: varchar({ length: 255 }).notNull(),
        firstName: varchar('first_name', { length: 255 }),
        lastName: varchar('last_name', { length: 255 }),
        phone: varchar({ length: 255 }),
        isActive: boolean('is_active').default(false).notNull(),
        inviteExpiresAt: timestamp('invite_expires_at', { withTimezone: true, mode: 'string' }),
        status: varchar('status', { length: 255 }),
        createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }).notNull(),
        updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }).notNull(),
        lastLoginAt: timestamp('last_login_at', { withTimezone: true, mode: 'string' }),
        locale: varchar({ length: 255 }).default('en').notNull(),
        timezone: varchar({ length: 255 }).default('UTC').notNull(),
        isMigrated: boolean('is_migrated').default(false).notNull(),
    },
    (table) => [
        index().using('btree', table.keycloakId.asc().nullsLast().op('text_ops')),
        unique('users_keycloak_id_unique').on(table.keycloakId),
        unique('users_email_unique').on(table.email),
    ],
);

export const usersRelations = relations(users, ({ many }) => ({
    userRoles: many(userRoles),
}));
