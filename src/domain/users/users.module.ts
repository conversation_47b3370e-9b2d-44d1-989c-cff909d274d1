import { MikroOrmModule } from '@mikro-orm/nestjs';
import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { KeycloakAdminClientService } from '../../auth/services/keycloak-admin-client.service';
import { RbacModule } from '../../rbac/rbac.module';
import { CommonModule } from '../common/common.module';
import { OrganizationsModule } from '../organizations';
import { RolesController, UsersController } from './controllers';
import { Invitation } from './entities/invitation.entity';
import { Permission } from './entities/permission.entity';
import { Role } from './entities/role.entity';
import { RolePermission } from './entities/role-permissions.entity';
import { User } from './entities/user.entity';
import { UserRole } from './entities/user-role.entity';
import { InvitationService } from './invitation.service';
import { OrganizationCreatedEventListener } from './listeners';
import { NotificationService } from './notification.service';
import { UserPresenter } from './presenters/user.presenter';
import {
    InvitationRepository,
    PermissionRepository,
    RolePermissionRepository,
    RoleRepository,
    UserEmailRepository,
    UserRepository,
    UserRoleRepository,
} from './repositories';
import { InvitationDrizzleRepository } from './repositories/invitation.drizzle.repository';
import { PermissionDrizzleRepository } from './repositories/permission.drizzle.repository';
import { RoleDrizzleRepository } from './repositories/role.drizzle.repository';
import { UserDrizzleRepository } from './repositories/user.drizzle.repository';
import { PermissionsService, RolePermissionsService, RolesService, UserRoleService, UserService } from './services';

@Module({
    imports: [
        ConfigModule,
        forwardRef(() => RbacModule),
        MikroOrmModule.forFeature([User, UserRole, Permission, Role, RolePermission, Invitation]),
        forwardRef(() => OrganizationsModule),
        forwardRef(() => CommonModule),
    ],
    controllers: [UsersController, RolesController],
    providers: [
        UserRepository,
        UserRoleRepository,
        PermissionRepository,
        RoleRepository,
        RolePermissionRepository,
        InvitationRepository,
        UserService,
        RolesService,
        KeycloakAdminClientService,
        UserEmailRepository,
        OrganizationCreatedEventListener,
        InvitationService,
        NotificationService,
        PermissionsService,
        RolePermissionsService,
        UserRoleService,
        RoleDrizzleRepository,
        UserDrizzleRepository,
        PermissionDrizzleRepository,
        UserPresenter,
        InvitationDrizzleRepository,
    ],
    exports: [
        UserRepository,
        PermissionRepository,
        RoleRepository,
        RolePermissionRepository,
        InvitationRepository,
        MikroOrmModule.forFeature([User, UserRole, Permission, Role, RolePermission, Invitation]),
        UserService,
        RolesService,
        InvitationService,
        PermissionsService,
        RolePermissionsService,
        UserRoleService,
        RoleDrizzleRepository,
        UserDrizzleRepository,
        PermissionDrizzleRepository,
        UserPresenter,
        InvitationDrizzleRepository,
    ],
})
export class UsersModule {}
