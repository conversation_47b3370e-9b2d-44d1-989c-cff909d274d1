import { relations } from 'drizzle-orm';
import { boolean, pgEnum, pgTable, timestamp, uuid, varchar } from 'drizzle-orm/pg-core';

import { rolePermissions } from './role-permission.schema';

export const actionType = pgEnum('action_type', ['create', 'read', 'update', 'approve', 'reject', 'manage', 'export']);

export const resourceType = pgEnum('resource_type', [
    'quote',
    'quote_item',
    'contract',
    'contract_item',
    'entity',
    'organization',
    'user',
    'role',
    'contact',
    'asset',
    'platform',
    'user_organization',
    'invited_user',
]);

export const permissions = pgTable('permissions', {
    id: uuid().primaryKey().notNull(),
    resourceType: resourceType('resource_type').notNull(),
    actionType: actionType('action_type').notNull(),
    name: varchar({ length: 255 }).notNull(),
    description: varchar({ length: 255 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }).notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }).notNull(),
    disabled: boolean().default(false).notNull(),
});

export const permissionsRelations = relations(permissions, ({ many }) => ({
    rolePermissions: many(rolePermissions),
}));
