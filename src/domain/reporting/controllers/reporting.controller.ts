import { <PERSON>, Get, Logger, <PERSON><PERSON> } from '@nestjs/common';
import { ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';

import { RequestContext } from '../../../auth/context.decorator';
import { BaseController } from '../../common/controllers/base.controller';
import { Context } from '../../common/interfaces';
import { ReportingKpiDto } from '../dtos';
import { ReportingService } from '../services';

@ApiTags('Reporting')
@Controller('reporting')
export class ReportingController extends BaseController {
    private readonly logger = new Logger(ReportingController.name);

    constructor(private readonly reportingService: ReportingService) {
        super();
    }

    @Get('kpis')
    @ApiResponse({
        status: 200,
        type: ReportingKpiDto,
    })
    async getKpis(@Res() res: Response, @RequestContext() context: Context) {
        const data = await this.reportingService.getKpis(context);

        return this.sendOk(res, data);
    }
}
