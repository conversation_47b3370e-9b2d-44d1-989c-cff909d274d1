import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { ContractsModule } from '../contracts/contracts.module';
import { QuotesModule } from '../quotes/quotes.module';
import { ReportingController } from './controllers';
import { ReportingDrizzleRepository } from './repositories/reporting.drizzle.repository';
import { ReportingService } from './services';

@Module({
    imports: [ConfigModule, ContractsModule, QuotesModule],
    controllers: [ReportingController],
    providers: [ReportingService, ReportingDrizzleRepository],
    exports: [ReportingService],
})
export class ReportingModule {}
