import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

export const ReportingKpiSchema = extendApi(
    z.object({
        contracts: z.object({
            total: z.number().default(0),
            status: z.object({
                Active: z.number().default(0),
                Renewed: z.number().default(0),
                Terminated: z.number().default(0),
                Expired: z.number().default(0),
            }),
            activity: z.object({
                Active: z.number().default(0),
                Inactive: z.number().default(0),
            }),
            expiration: z.object({
                active: z.number().default(0),
                expiring: z.number().default(0),
                expired: z.number().default(0),
            }),
        }),
        customers: z.object({
            total: z.number().default(0),
        }),
        quotes: z.object({
            total: z.number().default(0),
            status: z.object({
                'Change Requested': z.number().default(0),
                Lost: z.number().default(0),
                Open: z.number().default(0),
                Ordered: z.number().default(0),
            }),
        }),
    }),
    {
        title: 'ReportingKpiSchema',
        description: 'ReportingKpi model',
    },
);

export class ReportingKpiDto extends createZodDto(ReportingKpiSchema) {}
