import { Injectable } from '@nestjs/common';
import { Inject } from '@nestjs/common';
import { and, count, inArray, ne, sql } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

import * as schema from '../../../../drizzle/schema';
import { DRIZZLE_PROVIDER } from '../../../drizzle/drizzle.provider';
import { ContractUiStatusEnum } from '../../contracts/enums';
import { QuoteUiStatusEnum } from '../../quotes/enums';

@Injectable()
export class ReportingDrizzleRepository {
    constructor(@Inject(DRIZZLE_PROVIDER) private readonly db: NodePgDatabase<typeof schema>) {}

    private getStatusCounts<T extends { status: string; count: unknown }>(
        results: T[],
    ): { counts: Record<string, number>; total: number } {
        const counts = results.reduce((acc: Record<string, number>, row) => {
            acc[row.status] = Number(row.count);
            return acc;
        }, {});

        const total = Object.values(counts).reduce((sum, count) => sum + count, 0);

        return { counts, total };
    }

    async getContractMetrics(resellerIds: string[], endUserIds: string[] | null) {
        const baseConditions = [
            inArray(schema.contracts.resellerId, resellerIds),
            ...(endUserIds !== null ? [inArray(schema.contracts.endUserId, endUserIds)] : []),
            ne(schema.contracts.uiStatus, ContractUiStatusEnum.INVALID),
        ];

        const statusResult = await this.db
            .select({
                status: schema.contracts.uiStatus,
                count: count(),
            })
            .from(schema.contracts)
            .where(and(...baseConditions))
            .groupBy(schema.contracts.uiStatus);

        const { counts: statusCounts, total: totalContracts } = this.getStatusCounts(statusResult);

        const contractStatusSummary = this.db.$with('contract_status_summary').as(
            this.db
                .select({
                    contractId: schema.contracts.id,
                    contract_category: sql<string>`
                        CASE
                            WHEN ${schema.contracts.uiStatus} IN ('Active', 'Renewed') THEN 'Active'
                            ELSE 'Inactive'
                        END
                    `.as('contract_category'),
                })
                .from(schema.contracts)
                .where(and(...baseConditions)),
        );

        const activityResult = await this.db
            .with(contractStatusSummary)
            .select({
                status: contractStatusSummary.contract_category,
                count: count(),
            })
            .from(contractStatusSummary)
            .groupBy(contractStatusSummary.contract_category);

        const { counts: activityCounts } = this.getStatusCounts(activityResult);

        const activeConditions = [...baseConditions, sql`${schema.contracts.uiStatus} IN ('Active', 'Renewed')`];

        const contractExpirationStatus = this.db.$with('contract_expiration_status').as(
            this.db
                .select({
                    status: sql<string>`
                        CASE
                            WHEN ${schema.contracts.endDate}::date > CURRENT_DATE + INTERVAL '3 months' THEN 'active'
                            WHEN ${schema.contracts.endDate}::date BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL '3 months' THEN 'expiring'
                            ELSE 'expired'
                        END
                    `.as('status'),
                })
                .from(schema.contracts)
                .where(and(...activeConditions)),
        );

        const expirationResult = await this.db
            .with(contractExpirationStatus)
            .select({
                status: contractExpirationStatus.status,
                count: count(),
            })
            .from(contractExpirationStatus)
            .groupBy(contractExpirationStatus.status)
            .orderBy(sql`count DESC`);

        const { counts: expirationCounts } = this.getStatusCounts(expirationResult);

        return {
            total: totalContracts,
            status: statusCounts,
            activity: activityCounts,
            expiration: expirationCounts,
        };
    }

    async getQuoteMetrics(resellerIds: string[], endUserIds: string[] | null) {
        const baseConditions = [
            inArray(schema.quotes.resellerId, resellerIds),
            ...(endUserIds !== null ? [inArray(schema.quotes.endUserId, endUserIds)] : []),
            ne(schema.quotes.uiStatus, QuoteUiStatusEnum.INVALID),
        ];

        const statusResult = await this.db
            .select({
                status: schema.quotes.uiStatus,
                count: count(),
            })
            .from(schema.quotes)
            .where(and(...baseConditions))
            .groupBy(schema.quotes.uiStatus);

        const { counts: statusCounts, total: totalQuotes } = this.getStatusCounts(statusResult);

        return {
            total: totalQuotes,
            status: statusCounts,
        };
    }
}
