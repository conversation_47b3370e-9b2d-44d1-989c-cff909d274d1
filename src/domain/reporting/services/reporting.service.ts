import { Inject, Injectable, Logger } from '@nestjs/common';

import { Context } from '../../common/interfaces';
import { ContractDrizzleRepository } from '../../contracts/repositories/contract.drizzle.repository';
import { QuoteDrizzleRepository } from '../../quotes/repositories/quote.drizzle.repository';
import { ReportingKpiDto } from '../dtos';
import { ReportingDrizzleRepository } from '../repositories/reporting.drizzle.repository';

@Injectable()
export class ReportingService {
    private readonly logger = new Logger(ReportingService.name);

    constructor(
        @Inject(ContractDrizzleRepository)
        private readonly contractDrizzleRepository: ContractDrizzleRepository,
        @Inject(QuoteDrizzleRepository)
        private readonly quoteDrizzleRepository: QuoteDrizzleRepository,
        @Inject(ReportingDrizzleRepository)
        private readonly reportingDrizzleRepository: ReportingDrizzleRepository,
    ) {}

    async getKpis(context: Context): Promise<ReportingKpiDto> {
        const res: any = {
            contracts: {
                total: 0,
                status: {
                    Active: 0,
                    Renewed: 0,
                    Terminated: 0,
                    Expired: 0,
                },
                activity: {
                    Active: 0,
                    Inactive: 0,
                },
                expiration: {
                    active: 0,
                    expiring: 0,
                    expired: 0,
                },
            },
            quotes: {
                total: 0,
                status: {
                    'Change Requested': 0,
                    Lost: 0,
                    Open: 0,
                    Ordered: 0,
                },
            },
            customers: {
                total: 0,
            },
        };

        const [contractMetrics, quoteMetrics, totalCustomers] = await Promise.all([
            this.reportingDrizzleRepository.getContractMetrics(context.resellerIds, context.endUserIds),
            this.reportingDrizzleRepository.getQuoteMetrics(context.resellerIds, context.endUserIds),
            this.getTotalCustomers(context.resellerIds, context.endUserIds),
        ]);

        res.contracts = {
            total: contractMetrics.total,
            status: contractMetrics.status,
            activity: contractMetrics.activity,
            expiration: contractMetrics.expiration,
        };
        res.quotes = {
            total: quoteMetrics.total,
            status: quoteMetrics.status,
        };
        res.customers = {
            total: totalCustomers,
        };

        return res;
    }

    protected async getTotalCustomers(resellerIds: string[], endUserIds: string[] | null) {
        const [contractEndUserIds, quoteEndUserIds] = await Promise.all([
            this.contractDrizzleRepository.findAllEndUserIdsByResellerId(resellerIds, endUserIds),
            this.quoteDrizzleRepository.findAllEndUserIdsByResellerId(resellerIds, endUserIds),
        ]);

        const allEndUserIds = [...contractEndUserIds, ...quoteEndUserIds];
        const uniqueEndUserIds = [...new Set(allEndUserIds)].filter((id): id is string => !!id);

        return uniqueEndUserIds.length;
    }
}
