import { Inject, Injectable } from '@nestjs/common';
import { and, desc, eq, ilike, inArray, InferSelectModel, ne, or, sql, SQLWrapper } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { alias, PgSelect, SelectedFields } from 'drizzle-orm/pg-core';

import * as schema from '../../../../drizzle/schema';
import { DRIZZLE_PROVIDER } from '../../../drizzle/drizzle.provider';
import { PaginatedResponseModel, PaginationParamsModel } from '../../common/pagination/pagination.model';
import { BaseDrizzleRepository } from '../../common/repositories/base.drizzle.repository';
import { AssetsQuotesListSelect } from '../consts/assets-quotes-list.select';
import { QuoteGlobalSearchFilterDto } from '../dtos/quote-global-search-filter.dto';
import { Quote } from '../entities/quote.entity';
import { QuoteUiStatusEnum } from '../enums/quote-ui-status.enum';
import { QuoteFilterModel } from '../models';

type QuoteDrizzle = Partial<InferSelectModel<typeof schema.quotes>>;

@Injectable()
export class QuoteDrizzleRepository extends BaseDrizzleRepository<Quote> {
    constructor(
        @Inject(DRIZZLE_PROVIDER)
        db: NodePgDatabase<typeof schema>,
    ) {
        super(db, schema.quotes);
    }

    async checkIfQuoteExistsByQuoteId(
        resellerIds: string[],
        endUserIds: string[] | null,
        quoteId: string,
    ): Promise<boolean> {
        const res = await this.findQuoteNumberByQuoteId(resellerIds, endUserIds, quoteId);
        return res !== null;
    }

    async findQuoteNumberByQuoteId(
        resellerIds: string[],
        endUserIds: string[] | null,
        quoteId: string,
    ): Promise<string | null> {
        const result = await this.db
            .select({ quoteNo: schema.quotes.quoteNo })
            .from(schema.quotes)
            .where(
                and(
                    ...[
                        inArray(schema.quotes.resellerId, resellerIds),
                        ...(endUserIds !== null ? [inArray(schema.quotes.endUserId, endUserIds)] : []),
                        eq(schema.quotes.id, quoteId),
                    ],
                ),
            )
            .limit(1);

        return result.length > 0 ? result[0].quoteNo : null;
    }

    async findQuoteByResellerIdAndQuoteId(
        resellerIds: string[],
        endUserIds: string[] | null,
        id: string,
        withEndUser: boolean = false,
    ): Promise<QuoteDrizzle | null> {
        const query = this.db
            .select({
                id: schema.quotes.id,
                quoteNo: schema.quotes.quoteNo,
                status: schema.quotes.status,
                uiStatus: schema.quotes.uiStatus,
                expiryDate: schema.quotes.expiryDate,
                startDate: schema.quotes.startDate,
                endDate: schema.quotes.endDate,
                groupId: schema.quotes.groupId,
                endCustomerTotalPrice: schema.quotes.endCustomerTotalPrice,
                resellerTotalPrice: schema.quotes.resellerTotalPrice,
                currency: schema.quotes.currency,
                endUserId: schema.quotes.endUserId,
                resellerId: schema.quotes.resellerId,
                distributorId: schema.quotes.distributorId,
                vendorId: schema.quotes.vendorId,

                ...(withEndUser
                    ? {
                          endUser: {
                              id: schema.entities.id,
                              name: schema.entities.name,
                          },
                      }
                    : {}),
            })
            .from(schema.quotes)
            .where(
                and(
                    ...[
                        inArray(schema.quotes.resellerId, resellerIds),
                        ...(endUserIds !== null ? [inArray(schema.quotes.endUserId, endUserIds)] : []),
                        eq(schema.quotes.id, id),
                    ],
                ),
            )
            .limit(1);

        if (withEndUser) {
            query.leftJoin(schema.entities, eq(schema.entities.id, schema.quotes.endUserId));
        }

        const item = await query;

        return item.length > 0 ? item[0] : null;
    }

    async globalSearch(resellerIds: string[], endUserIds: string[] | null, searchPhrase?: string) {
        const whereConditions: SQLWrapper[] = [
            ...(searchPhrase
                ? [
                      or(
                          eq(sql`UPPER(${schema.quotes.quoteNo})`, searchPhrase),
                          eq(sql`UPPER(${schema.quotes.groupId})`, searchPhrase),
                          eq(sql`UPPER(${schema.quoteItems.serialNo})`, searchPhrase),
                      ),
                  ]
                : []),
            inArray(schema.quotes.resellerId, resellerIds),
            ...(endUserIds !== null ? [inArray(schema.quotes.endUserId, endUserIds)] : []),
            ne(schema.quotes.uiStatus, QuoteUiStatusEnum.INVALID),
        ];

        const query = this.db
            .select({
                id: schema.quotes.id,
                quoteNo: schema.quotes.quoteNo,
                groupId: schema.quotes.groupId,
                endUser: {
                    id: schema.entities.id,
                    name: schema.entities.name,
                },
                uiStatus: schema.quotes.uiStatus,
                resellerTotalPrice: schema.quotes.resellerTotalPrice,
                startDate: schema.quotes.startDate,
                endDate: schema.quotes.endDate,
                expiresIn: sql`${schema.quotes.endDate}::date - now()`,
                currency: schema.quotes.currency,
            })
            .from(schema.quotes)
            .innerJoin(schema.entities, eq(schema.entities.id, schema.quotes.endUserId))
            .leftJoin(schema.quoteItems, eq(schema.quoteItems.quoteId, schema.quotes.id))
            .limit(5)
            .groupBy(schema.quotes.id, schema.entities.id)
            .orderBy(desc(schema.quotes.endDate))
            .where(and(...whereConditions));

        const items = await query;

        return items;
    }

    async findQuotesByResellerIds(
        resellerIds: string[],
        endUserIds: string[] | null,
        paginationParams?: PaginationParamsModel,
        filter?: QuoteFilterModel | QuoteGlobalSearchFilterDto,
        fields?: SelectedFields,
        queryModifier?: (query: PgSelect) => void,
    ): Promise<PaginatedResponseModel<Quote>> {
        const entityConditions = [
            inArray(schema.quotes.resellerId, resellerIds),
            ...(endUserIds !== null ? [inArray(schema.quotes.endUserId, endUserIds)] : []),
        ];
        return this.findQuotesWithFilter(paginationParams, filter, entityConditions, fields, queryModifier);
    }

    async findAllEndUserIdsByResellerId(resellerIds: string[], endUserIds: string[] | null): Promise<string[]> {
        const query = this.db
            .select({
                endUserId: schema.quotes.endUserId,
            })
            .from(schema.quotes)
            .where(
                and(
                    inArray(schema.quotes.resellerId, resellerIds),
                    ...(endUserIds !== null ? [inArray(schema.quotes.endUserId, endUserIds)] : []),
                    ne(schema.quotes.uiStatus, QuoteUiStatusEnum.INVALID),
                ),
            )
            .groupBy(schema.quotes.endUserId);

        const result = await query;
        return result.map((record) => record.endUserId).filter((id): id is string => !!id);
    }

    async findQuotesByResellerAndEndUserId(
        resellerIds: string[],
        endUserIds: string[] | null,
        endUserId: string,
        paginationParams: PaginationParamsModel,
        filter?: QuoteFilterModel,
        fields?: SelectedFields,
        queryModifier?: (query: PgSelect) => void,
    ): Promise<PaginatedResponseModel<Quote>> {
        const entityConditions = [
            inArray(schema.quotes.resellerId, resellerIds),
            ...(endUserIds !== null ? [inArray(schema.quotes.endUserId, endUserIds)] : []),
            eq(schema.quotes.endUserId, endUserId),
        ];
        return this.findQuotesWithFilter(paginationParams, filter, entityConditions, fields, queryModifier);
    }

    protected buildSearchConditions(searchTerm: string): SQLWrapper[] {
        if (!searchTerm) return [];

        return [
            or(
                ilike(schema.quotes.quoteNo, `%${searchTerm}%`),
                ilike(schema.quotes.groupId, `%${searchTerm}%`),
                ilike(schema.entities.name, `%${searchTerm}%`),
            ),
        ];
    }

    protected buildStatusConditions(status: string | string[] | undefined): SQLWrapper[] {
        if (!status) {
            return [];
        }

        const statusArray = Array.isArray(status) ? status : [status];

        if (statusArray.length === 0) {
            return [];
        }

        const statusConditions = statusArray.map((s) => eq(schema.quotes.uiStatus, s as any));
        return [or(...statusConditions)];
    }

    protected buildTypeConditions(type?: string): SQLWrapper[] {
        const conditions: SQLWrapper[] = [];

        if (type) {
            conditions.push(eq(schema.quotes.quoteType, type));
        }

        return conditions;
    }

    protected mapToEntity(record: any): Quote {
        return {
            ...record,
            startDate: record.startDate ? new Date(record.startDate) : null,
            endDate: record.endDate ? new Date(record.endDate) : null,
            createdAt: record.createdAt ? new Date(record.createdAt) : null,
            updatedAt: record.updatedAt ? new Date(record.updatedAt) : null,
            resellerTotalPrice: record.resellerTotalPrice ? Number(record.resellerTotalPrice) : null,
            endCustomerTotalPrice: record.endCustomerTotalPrice ? Number(record.endCustomerTotalPrice) : null,
        } as Quote;
    }

    async findQuotesWithFilter(
        paginationParams?: PaginationParamsModel,
        filter?: QuoteFilterModel | QuoteGlobalSearchFilterDto,
        additionalConditions: SQLWrapper[] = [],
        fields?: SelectedFields,
        queryModifier?: (query: PgSelect) => void,
    ): Promise<PaginatedResponseModel<Quote>> {
        const whereConditions: SQLWrapper[] = [
            ne(schema.quotes.uiStatus, QuoteUiStatusEnum.INVALID),
            ...additionalConditions,
        ];

        if (filter?.search) {
            whereConditions.push(...this.buildSearchConditions(filter.search));
        }

        // Global search conditions
        if (filter && 'globalSearchPhrase' in filter && filter.globalSearchPhrase) {
            whereConditions.push(
                or(
                    eq(schema.quotes.quoteNo, filter.globalSearchPhrase),
                    eq(schema.quotes.groupId, filter.globalSearchPhrase),
                ),
            );
        }

        whereConditions.push(...this.buildStatusConditions(filter?.status));

        if (filter?.type) {
            whereConditions.push(...this.buildTypeConditions(filter.type));
        }

        if (!filter) {
            filter = { sortBy: 'quoteNo', sortOrder: 'asc' } as QuoteFilterModel;
        } else if (!filter.sortBy) {
            filter.sortBy = 'quoteNo';
            filter.sortOrder = 'asc';
        } else if (filter.sortBy && !filter.sortOrder) {
            filter.sortOrder = 'asc';
        }

        if (filter.sortBy === 'endUser') {
            filter.sortBy = 'endUserName';
        }

        if (filter.sortBy === 'vendor') {
            filter.sortBy = 'vendorName';
        }

        const result = await this.findPaginated(whereConditions, paginationParams, filter, fields, queryModifier);

        const mappedData = result.data.map((record) => this.mapToEntity(record));
        return new PaginatedResponseModel<Quote>(mappedData, result.meta);
    }

    async findQuotesByContract(
        resellerIds: string[],
        endUserIds: string[] | null,
        contractId: string,
        paginationParams: PaginationParamsModel,
        filter?: QuoteFilterModel,
    ) {
        const vendorSchema = alias(schema.entities, 'vendor');

        const fields = {
            id: schema.quotes.id,
            quoteNo: schema.quotes.quoteNo,
            uiStatus: schema.quotes.uiStatus,
            endDate: schema.quotes.endDate,
            expiryDate: schema.quotes.expiryDate,
            groupId: schema.quotes.groupId,
            resellerTotalPrice: schema.quotes.resellerTotalPrice,
            distributorTotalPrice: schema.quotes.distributorTotalPrice,
            endCustomerTotalPrice: schema.quotes.endCustomerTotalPrice,
            startDate: schema.quotes.startDate,
            endUserName: schema.entities.name,
            endUser: {
                id: schema.entities.id,
                name: schema.entities.name,
            },
            vendor: {
                id: vendorSchema.id,
                name: vendorSchema.name,
            },
            currency: schema.quotes.currency,
        };

        const modifier = (query: PgSelect) => {
            query
                .leftJoin(schema.quoteItems, eq(schema.quotes.id, schema.quoteItems.quoteId))
                .leftJoin(
                    schema.contractItems,
                    sql`(${schema.quoteItems.sourceContractItemId} =
                            ${schema.contractItems.id}
                            OR
                            ${schema.quoteItems.resultingContractItemId}
                            =
                            ${schema.contractItems.id})`,
                )
                .leftJoin(schema.entities, eq(schema.entities.id, schema.quotes.endUserId))
                .innerJoin(
                    vendorSchema,
                    and(
                        eq(vendorSchema.id, schema.quotes.vendorId),
                        filter?.vendorNames?.length ? inArray(vendorSchema.name, filter.vendorNames) : undefined,
                    ),
                )
                .groupBy(schema.quotes.id, schema.entities.id, vendorSchema.id);
        };

        const where: SQLWrapper[] = [
            ne(schema.quotes.uiStatus, QuoteUiStatusEnum.INVALID),
            inArray(schema.quotes.resellerId, resellerIds),
            ...(endUserIds !== null ? [inArray(schema.quotes.endUserId, endUserIds)] : []),
            eq(schema.contractItems.contractId, contractId),
        ];

        if (filter?.type) {
            where.push(eq(schema.quotes.quoteType, filter.type));
        }

        if (filter?.status?.length) {
            where.push(inArray(schema.quotes.uiStatus, filter.status));
        }

        if (filter?.search) {
            where.push(...this.buildSearchConditions(filter.search));
        }

        if (filter && filter.sortBy === 'endUser') {
            filter.sortBy = 'endUserName';
        }

        return await this.findPaginated(where, paginationParams, filter, fields, modifier);
    }

    async findQuotesByResellerAndSerialNumber(
        resellerIds: string[],
        endUserIds: string[] | null,
        serialNo: string,
        paginationParams: PaginationParamsModel,
        filter?: QuoteFilterModel,
    ): Promise<PaginatedResponseModel<Quote>> {
        if (filter && filter.sortBy === 'endUser') {
            filter.sortBy = 'endUserName';
        }

        const vendorSchema = alias(schema.entities, 'vendor');

        return this.findQuotesWithFilter(
            paginationParams,
            filter,
            [
                inArray(schema.quotes.resellerId, resellerIds),
                ...(endUserIds !== null ? [inArray(schema.quotes.endUserId, endUserIds)] : []),
                eq(schema.quoteItems.serialNo, serialNo),
            ],
            {
                ...AssetsQuotesListSelect,
                vendor: {
                    id: vendorSchema.id,
                    name: vendorSchema.name,
                },
            },
            (query) => {
                query.innerJoin(schema.quoteItems, eq(schema.quoteItems.quoteId, schema.quotes.id));
                query.innerJoin(schema.entities, eq(schema.entities.id, schema.quotes.endUserId));
                query.innerJoin(
                    vendorSchema,
                    and(
                        eq(vendorSchema.id, schema.quotes.vendorId),
                        filter?.vendorNames?.length ? inArray(vendorSchema.name, filter.vendorNames) : undefined,
                    ),
                );
                query.groupBy(schema.quotes.id, vendorSchema.id);
            },
        );
    }
}
