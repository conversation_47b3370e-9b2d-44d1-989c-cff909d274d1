import { SqlEntityManager } from '@mikro-orm/postgresql';
import { Inject, Injectable } from '@nestjs/common';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

import * as schema from '../../../../drizzle/schema';
import { DRIZZLE_PROVIDER } from '../../../drizzle/drizzle.provider';
import { BaseRepository } from '../../common/repositories/base.repository';
import { Quote } from '../entities';
import { QuoteStatusEnum, QuoteUiStatusEnum } from '../enums';

@Injectable()
export class QuoteRepository extends BaseRepository<Quote> {
    constructor(
        em: SqlEntityManager,
        @Inject(DRIZZLE_PROVIDER)
        db: NodePgDatabase<typeof schema>,
    ) {
        super(em, Quote.name, db);
    }

    async updateUiStatus(quote: Quote, status: QuoteUiStatusEnum): Promise<void> {
        await this.nativeUpdate({ id: quote.id }, { uiStatus: status });
    }

    async updateStatus(quote: Quote, status: QuoteStatusEnum): Promise<void> {
        await this.nativeUpdate({ id: quote.id }, { status: status });
    }
}
