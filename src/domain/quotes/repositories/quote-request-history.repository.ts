import { SqlEntityManager } from '@mikro-orm/postgresql';
import { Inject, Injectable } from '@nestjs/common';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

import * as schema from '../../../../drizzle/schema';
import { DRIZZLE_PROVIDER } from '../../../drizzle/drizzle.provider';
import { BaseRepository } from '../../common/repositories/base.repository';
import { QuoteRequestHistory } from '../entities';

@Injectable()
export class QuoteRequestHistoryRepository extends BaseRepository<QuoteRequestHistory> {
    constructor(
        em: SqlEntityManager,
        @Inject(DRIZZLE_PROVIDER)
        db: NodePgDatabase<typeof schema>,
    ) {
        super(em, QuoteRequestHistory.name, db);
    }

    async findRequestsByQuoteId(quoteId: string): Promise<QuoteRequestHistory[]> {
        return await this.find({
            quote: {
                id: quoteId,
            },
        });
    }

    async findLatestRequestByQuoteId(quoteId: string): Promise<QuoteRequestHistory> {
        return await this.findOne(
            {
                quote: {
                    id: quoteId,
                },
            },
            {
                orderBy: {
                    createdAt: 'desc',
                },
            },
        );
    }
}
