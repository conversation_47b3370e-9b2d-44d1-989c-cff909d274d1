import { Inject, Injectable } from '@nestjs/common';
import { and, asc, desc, eq, gt, ilike, inArray, isNotNull, or, sql, SQLWrapper } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

import * as schema from '../../../../drizzle/schema';
import { DRIZZLE_PROVIDER } from '../../../drizzle/drizzle.provider';
import {
    PaginatedResponseModel,
    PaginationMetaModel,
    PaginationParamsModel,
} from '../../common/pagination/pagination.model';
import { BaseDrizzleRepository } from '../../common/repositories/base.drizzle.repository';
import { AssetType } from '../../common/types';
import { QuoteItem } from '../entities/quote-item.entity';
import { QuoteItemFilterModel } from '../models/quote-item-filter.model';

@Injectable()
export class QuoteItemViewDrizzleRepository extends BaseDrizzleRepository<QuoteItem> {
    constructor(
        @Inject(DRIZZLE_PROVIDER)
        db: NodePgDatabase<typeof schema>,
    ) {
        super(db, schema.vQuoteItems);
    }

    async findBySerialNumber({ serialNo }: { serialNo: string }) {
        const conditions: SQLWrapper[] = [eq(schema.vQuoteItems.serialNo, serialNo)];

        const items = await this.db
            .select()
            .from(this.table)
            .where(and(...conditions));

        return items;
    }

    async findAssetsByQuoteId(
        resellerIds: string[],
        endUserIds: string[] | null,
        quoteId: string,
        paginationParams: PaginationParamsModel,
        filter?: QuoteItemFilterModel,
    ): Promise<PaginatedResponseModel<AssetType>> {
        const sortBy = this.buildSortBy(filter?.sortBy);
        const sortOrder = filter?.sortOrder || 'asc';

        const whereConditions: SQLWrapper[] = [
            inArray(schema.vQuoteItems.resellerId, resellerIds),
            ...(endUserIds !== null ? [inArray(schema.vQuoteItems.endUserId, endUserIds)] : []),
            eq(schema.vQuoteItems.quoteId, quoteId),
        ];

        if (filter?.search) {
            whereConditions.push(...this.buildSearchConditions(filter.search));
        }

        if (filter?.hasEndCustomerPrice !== undefined) {
            whereConditions.push(
                and(isNotNull(schema.vQuoteItems.endCustomerPrice), gt(schema.vQuoteItems.endCustomerPrice, '0')),
            );
        }

        if (filter.hasEndOfServiceLifeDate === true) {
            whereConditions.push(isNotNull(schema.vQuoteItems.supportLifeEndDate));
        }

        const DEFAULT_PAGINATION_LIMIT = parseInt(process.env.PAGINATION_DEFAULT_LIMIT, 10) || 50;
        const MAX_PAGINATION_LIMIT = parseInt(process.env.PAGINATION_MAX_LIMIT, 10) || 100;

        const limit = paginationParams?.limit || DEFAULT_PAGINATION_LIMIT;
        const effectiveLimit = Math.min(limit, MAX_PAGINATION_LIMIT);
        const queryLimit = effectiveLimit + 1;

        let offset = 0;
        if (paginationParams?.cursor) {
            offset = parseInt(paginationParams.cursor, 10);
        }

        const query = this.db
            .select({
                name: schema.vQuoteItems.productName,
                serial_number: schema.vQuoteItems.serialNo,
                coverage_status: schema.vQuoteItems.coverageStatus,
                service_group_sku: schema.vQuoteItems.serviceGroupSku,
                service_group_label: schema.vQuoteItems.serviceGroupLabel,
                product_sku: schema.vQuoteItems.productSku,
                currency: schema.vQuoteItems.currency,
                quantity: schema.vQuoteItems.quantity,
                items_count: sql<number>`count(*)`,
                reseller_price_final_sum: sql<number>`sum(${schema.vContractItems.resellerPriceFinal})::float`,
                end_customer_price_final_sum: sql<number>`sum(${schema.vQuoteItems.endCustomerPriceFinal})::float`,
                items: sql<any[]>`JSON_AGG(to_json(${schema.vQuoteItems}))`,
                vendor: schema.vQuoteItems.vendorId,
                start_date: schema.vQuoteItems.startDate,
                end_date: schema.vQuoteItems.endDate,
                support_life_end_date: schema.vQuoteItems.supportLifeEndDate,
            })
            .from(schema.vQuoteItems)
            .where(and(...whereConditions))
            .groupBy(
                schema.vQuoteItems.productName,
                schema.vQuoteItems.serialNo,
                schema.vQuoteItems.productSku,
                schema.vQuoteItems.currency,
                schema.vQuoteItems.quantity,
                schema.vQuoteItems.serviceGroupSku,
                schema.vQuoteItems.serviceGroupLabel,
                schema.vQuoteItems.coverageStatus,
                schema.vQuoteItems.vendorId,
                schema.vQuoteItems.startDate,
                schema.vQuoteItems.endDate,
                schema.vQuoteItems.supportLifeEndDate,
            )
            .limit(queryLimit)
            .offset(offset);

        if (sortOrder.toLowerCase() === 'desc') {
            switch (sortBy) {
                case 'name':
                case 'product_name':
                    query.orderBy(desc(schema.vQuoteItems.productName));
                    break;
                case 'serial_number':
                case 'serial_no':
                    query.orderBy(desc(schema.vQuoteItems.serialNo));
                    break;
                case 'product_sku':
                    query.orderBy(desc(schema.vQuoteItems.productSku));
                    break;
                case 'service_group_sku':
                    query.orderBy(desc(schema.vQuoteItems.serviceGroupSku));
                    break;
                case 'reseller_price_final_sum':
                case 'reseller_price':
                    query.orderBy(desc(sql`sum(${schema.vQuoteItems.resellerPriceFinal})::float`));
                    break;
                case 'end_customer_price_final_sum':
                case 'end_customer_price':
                    query.orderBy(desc(sql`sum(${schema.vQuoteItems.endCustomerPriceFinal})::float`));
                    break;
                case 'quantity':
                    query.orderBy(desc(schema.vQuoteItems.quantity));
                    break;
                case 'startDate':
                    query.orderBy(desc(schema.vQuoteItems.startDate));
                    break;
                case 'endDate':
                    query.orderBy(desc(schema.vQuoteItems.endDate));
                    break;
                case 'supportLifeEndDate':
                    query.orderBy(desc(schema.vQuoteItems.supportLifeEndDate));
                    break;
                default:
                    // For custom columns or computed values, use SQL expressions
                    query.orderBy(desc(sql`${sortBy}`));
                    break;
            }
        } else {
            switch (sortBy) {
                case 'name':
                case 'product_name':
                    query.orderBy(asc(schema.vQuoteItems.productName));
                    break;
                case 'serial_number':
                case 'serial_no':
                    query.orderBy(asc(schema.vQuoteItems.serialNo));
                    break;
                case 'product_sku':
                    query.orderBy(asc(schema.vQuoteItems.productSku));
                    break;
                case 'service_group_sku':
                    query.orderBy(asc(schema.vQuoteItems.serviceGroupSku));
                    break;
                case 'reseller_price_final_sum':
                case 'reseller_price':
                    query.orderBy(asc(sql`sum(${schema.vQuoteItems.resellerPriceFinal})::float`));
                    break;
                case 'end_customer_price_final_sum':
                case 'end_customer_price':
                    query.orderBy(asc(sql`sum(${schema.vQuoteItems.endCustomerPriceFinal})::float`));
                    break;
                case 'quantity':
                    query.orderBy(asc(schema.vQuoteItems.quantity));
                    break;
                case 'startDate':
                    query.orderBy(asc(schema.vQuoteItems.startDate));
                    break;
                case 'endDate':
                    query.orderBy(asc(schema.vQuoteItems.endDate));
                    break;
                case 'supportLifeEndDate':
                    query.orderBy(asc(schema.vQuoteItems.supportLifeEndDate));
                    break;
                default:
                    // For custom columns or computed values, use SQL expressions
                    query.orderBy(asc(sql`${sortBy}`));
                    break;
            }
        }

        // Execute query
        const assets = await query;

        // Handle pagination result
        const hasNextPage = assets.length > effectiveLimit;
        const data = hasNextPage ? assets.slice(0, effectiveLimit) : assets;

        const nextCursor = hasNextPage
            ? paginationParams?.cursor
                ? (parseInt(paginationParams.cursor, 10) + effectiveLimit).toString()
                : effectiveLimit.toString()
            : null;

        const items = data.map((record) => this.mapToEntity(record));

        return new PaginatedResponseModel<AssetType>(items, new PaginationMetaModel(hasNextPage, nextCursor));
    }

    protected mapToEntity(record: any): AssetType {
        return {
            ...record,
            quantity: record.quantity ? Number(record.quantity) : null,
            items_count: record.items_count ? Number(record.items_count) : null,
            reseller_price_final_sum: record.reseller_price_final_sum ? Number(record.reseller_price_final_sum) : null,
            end_customer_price_final_sum: record.end_customer_price_final_sum
                ? Number(record.end_customer_price_final_sum)
                : null,
        } as AssetType;
    }

    protected buildSortBy(sortBy: QuoteItemFilterModel['sortBy']): string {
        if (!sortBy) {
            return 'name';
        }

        switch (sortBy) {
            case 'serialNo':
            case 'serialNumber':
                return 'serial_number';
            case 'name':
            case 'productName':
                return 'name';
            case 'endCustomerPrice':
            case 'endCustomerPriceFinalSum':
                return 'end_customer_price_final_sum';
            case 'productSku':
                return 'product_sku';
            case 'resellerPrice':
            case 'resellerPriceFinalSum':
                return 'reseller_price_final_sum';
            case 'serviceGroupSku':
                return 'service_group_sku';
            default:
                return sortBy;
        }
    }

    protected buildSearchConditions(searchTerm: string): SQLWrapper[] {
        if (!searchTerm) return [];

        return [
            or(
                ilike(schema.vQuoteItems.productName, `%${searchTerm}%`),
                ilike(schema.vQuoteItems.serialNo, `%${searchTerm}%`),
                ilike(schema.vQuoteItems.productSku, `%${searchTerm}%`),
            ),
        ];
    }
}
