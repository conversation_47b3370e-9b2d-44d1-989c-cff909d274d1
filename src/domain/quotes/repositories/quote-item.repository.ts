import { FilterQuery } from '@mikro-orm/core';
import { SqlEntityManager } from '@mikro-orm/postgresql';
import { Inject, Injectable } from '@nestjs/common';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

import * as schema from '../../../../drizzle/schema';
import { DRIZZLE_PROVIDER } from '../../../drizzle/drizzle.provider';
import {
    PaginatedResponseModel,
    PaginationMetaModel,
    PaginationParamsModel,
} from '../../common/pagination/pagination.model';
import { BaseRepository } from '../../common/repositories/base.repository';
import { AssetType } from '../../common/types';
import { QuoteItem } from '../entities';
import { QuoteItemFilterModel } from '../models';

@Injectable()
export class QuoteItemRepository extends BaseRepository<QuoteItem> {
    constructor(
        em: SqlEntityManager,
        @Inject(DRIZZLE_PROVIDER)
        db: NodePgDatabase<typeof schema>,
    ) {
        super(em, QuoteItem.name, db);
    }

    async findQuoteItemsWithFilter(
        paginationParams: PaginationParamsModel,
        filter?: QuoteItemFilterModel,
        additionalWhere: FilterQuery<QuoteItem> = {},
    ): Promise<PaginatedResponseModel<QuoteItem>> {
        const where: FilterQuery<QuoteItem> = Object.assign({}, additionalWhere);

        if (filter?.search) {
            where['$or'] = [
                { serialNo: { $ilike: `%${filter.search}%` } },
                { productName: { $ilike: `%${filter.search}%` } },
                { productSku: { $ilike: `%${filter.search}%` } },
            ];
        }

        if (filter?.hasEndCustomerPrice !== undefined) {
            where['endCustomerPrice'] = filter.hasEndCustomerPrice ? { $ne: null } : null;
        }

        // Set default sort to serialNo if not specified in filter
        if (!filter) {
            filter = { sortBy: 'serialNo', sortOrder: 'asc' } as QuoteItemFilterModel;
        } else if (!filter.sortBy) {
            filter.sortBy = 'serialNo';
            filter.sortOrder = 'asc';
        }

        return this.findPaginated(where, paginationParams, filter);
    }

    async findBySerialNumber({ serialNo }: { serialNo: string }) {
        const query = `SELECT * FROM v_quote_items where serial_no = :serialNo`;
        const params = {
            serialNo,
        };

        const result = await this.getKnex().raw(query, params);
        return result.rows;
    }

    async findAssetsByQuoteId(
        quoteId: string,
        paginationParams?: PaginationParamsModel,
        filter?: QuoteItemFilterModel,
    ): Promise<PaginatedResponseModel<AssetType> | AssetType[]> {
        const conditions: string[] = [`ci.quote_id = :quoteId`];
        const params: any = { quoteId };

        if (filter?.search) {
            conditions.push(`(
                product_name ilike :search
                or serial_no ilike :search
                or product_sku ilike :search
            )`);
            params.search = `%${filter.search}%`;
        }

        if (filter.hasEndCustomerPrice === true) {
            conditions.push('(ci.end_customer_price_final is not null AND ci.end_customer_price_final != 0)');
        }
        if (filter.hasEndCustomerPrice === false) {
            conditions.push(`(ci.end_customer_price_final is null OR ci.end_customer_price_final = 0)`);
        }

        const countQuery = `
            select count(distinct ci.product_sku || '-' || ci.serial_no) as total_count
            from v_quote_items ci
            WHERE ${conditions.join(' AND ')}
        `;

        const countResult = await this.getKnex().raw(countQuery, params);
        const totalCount = parseInt(countResult.rows[0].total_count, 10);

        let sortBy;
        if (!filter?.sortBy) {
            sortBy = 'name';
        } else {
            switch (filter?.sortBy) {
                case 'serialNo':
                case 'serialNumber':
                    sortBy = 'serial_no';
                    break;
                case 'serviceGroupSku':
                    sortBy = 'service_group_sku';
                    break;
                case 'resellerPrice':
                case 'resellerPriceFinalSum':
                    sortBy = 'reseller_price_final_sum';
                    break;
                case 'endCustomerPriceFinalSum':
                case 'endCustomerPrice':
                    sortBy = 'end_customer_price_final_sum';
                    break;
                case 'productName':
                    sortBy = 'product_name';
                    break;
                case 'productSku':
                    sortBy = 'product_sku';
                    break;
                default:
                    sortBy = filter.sortBy;
                    break;
            }
        }
        const sortOrder = filter?.sortOrder || 'asc';
        const DEFAULT_PAGINATION_LIMIT = parseInt(process.env.PAGINATION_DEFAULT_LIMIT, 10) || 50;
        const limit = paginationParams?.limit || DEFAULT_PAGINATION_LIMIT;
        const offset = paginationParams?.cursor ? parseInt(paginationParams.cursor, 10) : 0;

        const dataQuery = `
            select product_name          as name,
                   vendor_id             as vendor_id,
                   serial_no             as serial_number,
                   coverage_status       as coverage_status,
                   service_group_sku     as service_group_sku,
                   service_group_label   as service_group_label,
                   product_sku           as product_sku,
                   currency              as currency,
                   quantity              as quantity,
                   count(*)              as items_count,
                   sum(reseller_price_final)::float     as reseller_price_final_sum,
                   sum(distributor_price_final)::float  as distributor_price_final_sum,
                   sum(end_customer_price_final)::float as end_customer_price_final_sum,
                   JSON_AGG(to_json(ci)) as items
            from v_quote_items ci
            WHERE ${conditions.join(' AND ')}
            group by name, vendor_id, serial_number, product_sku,
                     service_group_sku, service_group_label, coverage_status, currency, quantity
            order by ${sortBy} ${sortOrder}
            limit :limit offset :offset
        `;

        params.limit = limit;
        params.offset = offset;

        const result = await this.getKnex().raw(dataQuery, params);
        const assets = result.rows as AssetType[];

        const hasNextPage = offset + assets.length < totalCount;
        const nextCursor = hasNextPage ? (offset + limit).toString() : null;

        return new PaginatedResponseModel<AssetType>(assets, new PaginationMetaModel(hasNextPage, nextCursor));
    }

    async findAssetsByQuoteIds(quoteIds: string[], filter?: QuoteItemFilterModel): Promise<AssetType[]> {
        if (quoteIds.length === 0) {
            return [];
        }

        const params: any = { quoteIds };

        let query = `
            select product_name                  as name,
                    serial_no                     as serial_number,
                    coverage_status               as coverage_status,
                    service_group_sku             as service_group_sku,
                    product_sku                   as product_sku,
                    currency                      as currency,
                    quantity                      as quantity,
                    count(*)                      as items_count,
                    sum(reseller_price_final)     as reseller_price_final_sum,
                    sum(distributor_price_final)  as distributor_price_final_sum,
                    sum(end_customer_price_final) as end_customer_price_final_sum,
                    JSON_AGG(to_json(ci))         as items
            from v_quote_items ci
            where ci.quote_id = ANY (:quoteIds)
        `;

        if (filter?.search) {
            query += ` and (
                product_name ilike :search
                or serial_no ilike :search
                or product_sku ilike :search
            )`;
            params.search = `%${filter.search}%`;
        }

        query += `
        group by name, serial_number, product_sku, service_group_sku, coverage_status, currency, quantity
                order by name, serial_number
        `;

        const result = await this.getKnex().raw(query, params);
        return result.rows as AssetType[];
    }

    async findAssetsByEntityId(entityId: string, filter?: QuoteItemFilterModel): Promise<AssetType[]> {
        const params: any = { entityId };

        let query = `
            select product_name                  as name,
                    serial_no                     as serial_number,
                    coverage_status               as coverage_status,
                    service_group_sku             as service_group_sku,
                    product_sku                   as product_sku,
                    currency                      as currency,
                    quantity                      as quantity,
                    count(*)                      as items_count,
                    sum(reseller_price_final)     as reseller_price_final_sum,
                    sum(distributor_price_final)  as distributor_price_final_sum,
                    sum(end_customer_price_final) as end_customer_price_final_sum,
                    JSON_AGG(to_json(ci))         as items
            from v_quote_items ci
            where (ci.distributor_id = :entityId or ci.vendor_id = :entityId or ci.end_user_id = :entityId or ci.reseller_id = :entityId)

        `;

        if (filter?.search) {
            query += ` and (
                product_name ilike :search
                or serial_no ilike :search
                or product_sku ilike :search
            )`;
            params.search = `%${filter.search}%`;
        }

        query += `
        group by name, serial_number, product_sku, service_group_sku, coverage_status, currency, quantity
                order by name, serial_number
        `;

        const result = await this.getKnex().raw(query, params);
        return result.rows as AssetType[];
    }
}
