import { index, integer, jsonb, numeric, pgEnum, pgTable, text, timestamp } from 'drizzle-orm/pg-core';

import { ContractItemDataField } from '../contracts/contract-item.schema';
import { DataSourceEnum } from '../contracts/enums/data-source.enum';

const dataSource = pgEnum('data_source', DataSourceEnum);

export type QuoteItemDataField = {
    [x: string]: any;

    QuoteItemId?: number;
    ItemNo?: number;
    Sku?: string; // Service Level SKU
    Description?: string; // Service Level Description
    Quantity?: number; // Units quantity
    SerialNo?: string;
    CoveredSku?: string; // Product SKU
    VendorEntityId?: number;
    SellRate?: number;
    MSRP?: number;

    TotalBuyPrice?: number;
    TotalValue?: number;
    RetailPrice?: number; // [DEPRECATED] End Customer Price (already multiplied by quantity) - USE TesediEndCustomerPrice INSTEAD
    // BuyPrice?: number; // [DEPRECATED] Distributor Price (already multiplied by quantity) - USE TesediDistributorPrice INSTEAD // Hidden because of AH-101
    SellPrice?: number; // [DEPRECATED] Reseller Price (already multiplied by quantity) - USE TesediResellerPrice INSTEAD
    TesediResellerPrice?: number;
    TesediDistributorPrice?: number;
    TesediEndCustomerPrice?: number;
    TesediResellerPricePerUnit?: string;
    TesediDistributorPricePerUnit?: string;
    TesediEndCustomerPricePerUnit?: string;

    UnitCOGS?: number;
    TotalCOGS?: number;
    BuyRate?: number;
    MDFAmount?: number;
    UpliftAmount?: number | null;
    EnduserSiteId?: number;
    AttachToContractItemId?: number;
    SourceContractItemId?: number;
    CoveredProductId?: number; // Product ID
    ServiceLevel?: string | null; // Service Group Description
    SAR?: string; // Support Account Reference ID
    SAID?: string; // Service Agreement ID
    DocId?: string;
    DocType?: string;
    EquipNo?: string;
    SvcLvlSku?: string; // Service Group SKU
    SvcLvlDesc?: string;
    MonthlyListPrice?: string;
    MonthlyDisc?: string;
    MonthlyNetPrice?: string;
    MonthlyListPriceEC?: string;
    EnvID?: string;
    SalesOrg?: string;
    PSPId?: string;
    PSPName?: string;
    GroupID?: string; // Covered SKU Description
    MyDisc?: string;
    SndDisc?: string;
    PpDisc?: string;
    HwContact?: string;
    SwContact?: string;
    SysContact?: string;
    SupportLifeEndDate?: string;
};

export const quoteItems = pgTable(
    'quote_items',
    {
        id: text().primaryKey().notNull(),
        itemNo: integer('item_no'),
        quoteId: text('quote_id'),
        vendorId: text('vendor_id'),
        serialNo: text('serial_no'),
        productSku: text('product_sku'),
        data: jsonb().$type<QuoteItemDataField>(),
        sourceContractItemId: text('source_contract_item_id'),
        resultingContractItemId: text('resulting_contract_item_id'),
        serviceLevelSku: text('service_level_sku'),
        serviceGroupSku: text('service_group_sku'),
        serviceGroupLabel: text('service_group_label'),
        resellerPrice: numeric('reseller_price', { precision: 20, scale: 4 }),
        distributorPrice: numeric('distributor_price', { precision: 20, scale: 4 }),
        endCustomerPrice: numeric('end_customer_price', { precision: 20, scale: 4 }),
        quantity: numeric({ precision: 10, scale: 0 }),

        createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
        updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }).defaultNow().notNull(),

        source: dataSource('source').default(DataSourceEnum.iAsset).notNull(),
    },
    (table) => [
        index('idx_quote_items_ecp').using('btree', table.endCustomerPrice.asc().nullsLast().op('numeric_ops')),
        index('idx_quote_items_p_sku').using('btree', table.productSku.asc().nullsLast().op('text_ops')),
        index('idx_quote_items_q_id').using('btree', table.quoteId.asc().nullsLast().op('text_ops')),
        index('idx_quote_items_rci').using('btree', table.resultingContractItemId.asc().nullsLast().op('text_ops')),
        index('idx_quote_items_sci').using('btree', table.sourceContractItemId.asc().nullsLast().op('text_ops')),
        index('idx_quote_items_sno').using('btree', table.serialNo.asc().nullsLast().op('text_ops')),
        index('idx_quote_items_v_p_sku').using(
            'btree',
            table.vendorId.asc().nullsLast().op('text_ops'),
            table.productSku.asc().nullsLast().op('text_ops'),
        ),
        index('idx_quote_items_v_sl_sku').using(
            'btree',
            table.vendorId.asc().nullsLast().op('text_ops'),
            table.serviceLevelSku.asc().nullsLast().op('text_ops'),
        ),
    ],
);
