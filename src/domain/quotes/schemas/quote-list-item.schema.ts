import { extendApi } from '@anatine/zod-openapi';

import { QuoteSchema } from './quote.schema';

export const QuoteListItemSchema = extendApi(
    QuoteSchema.pick({
        id: true,
        quoteNo: true,
        status: true,
        uiStatus: true,
        expiryDate: true,
        groupId: true,
        resellerTotalPrice: true,
        // distributorTotalPrice: true, // Hidden because of AH-101
        endCustomerTotalPrice: true,
        currency: true,
        endUser: true,
        startDate: true,
        endDate: true,
        vendor: true,
    }).setKey(
        '_links',
        QuoteSchema.shape._links.pick({
            self: true,
        }),
    ),
    {
        title: 'QuoteListItem',
        description: 'Quote list item model',
    },
);
