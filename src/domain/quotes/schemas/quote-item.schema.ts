import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { RelatedActionSchema } from '../../common/schemas/related-action.schema';
import { RelatedCompanyLinkSchema } from '../../common/schemas/related-company-link.schema';
import { RelatedItemLinkSchema } from '../../common/schemas/related-item-link.schema';

export const QuoteItemSchema = extendApi(
    z.object({
        id: extendApi(z.string(), {
            description: 'Quote item ID',
        }),

        itemNo: z.union([z.string(), z.number()]).nullable().optional().describe('Item No'),

        quoteId: extendApi(z.string().nullable().optional(), {
            description: 'Quote ID',
        }),

        vendorId: extendApi(z.string().nullable().optional(), {
            description: 'Vendor',
        }),

        serialNo: extendApi(z.string().nullable().optional(), {
            description: 'Serial Number',
        }),

        productSku: extendApi(z.string().nullable().optional(), {
            description: 'Product SKU',
        }),

        serviceLevelSku: extendApi(z.string().nullable().optional(), {
            description: 'Service Level SKU',
        }),

        serviceGroupSku: extendApi(z.string().nullable().optional(), {
            description: 'Service Group Sku',
        }),

        serviceGroupLabel: extendApi(z.string().nullable().optional(), {
            description: 'Service Group Label',
        }),

        serviceName: extendApi(z.string().nullable().optional(), {
            description: 'Service Name',
        }),

        resellerPrice: z.number().nullable().optional().describe('Reseller Price (per unit)'),
        // distributorPrice: z.number().nullable().optional().describe('Distributor Price (per unit)'), // Hidden because of AH-101
        endCustomerPrice: z.number().nullable().optional().describe('End Customer Price (per unit)'),

        // *************************************************************************************************************
        // ** Links section
        // *************************************************************************************************************

        _links: extendApi(
            z.object({
                self: extendApi(RelatedItemLinkSchema.omit({ count: true }), { description: 'Self' }),

                vendor: extendApi(RelatedCompanyLinkSchema, {
                    description: 'Vendor',
                }),
            }),
            {
                description: 'Links',
            },
        ),

        // *************************************************************************************************************
        // ** Actions section
        // *************************************************************************************************************
        _actions: extendApi(
            z.object({
                edit: extendApi(RelatedActionSchema, {
                    description: 'Update',
                }),
            }),
            {
                description: 'Actions',
            },
        ),
    }),
    {
        title: 'Quote item',
        description: 'Quote item model',
    },
);
