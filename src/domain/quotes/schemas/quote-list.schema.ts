import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { createPaginatedResponseSchema } from '../../common/pagination/pagination.schema';
import { QuoteListItemSchema } from './quote-list-item.schema';

export const QuoteListItemsSchema = extendApi(z.array(QuoteListItemSchema), {
    title: 'QuoteList',
    description: 'Quote list model',
});

export const QuoteListSchema = createPaginatedResponseSchema(QuoteListItemSchema);
