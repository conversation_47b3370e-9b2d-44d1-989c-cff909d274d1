import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { BaseFilterSchema } from '../../common/pagination/pagination.schema';
import { QuoteTypeEnum, QuoteUiStatusEnum } from '../enums';

const filteredUiStatusValues = Object.keys(QuoteUiStatusEnum)
    .map((key) => QuoteUiStatusEnum[key as keyof typeof QuoteUiStatusEnum])
    .filter((value) => value !== QuoteUiStatusEnum.INVALID.valueOf());
const quoteStatusValues = filteredUiStatusValues as [QuoteUiStatusEnum, ...QuoteUiStatusEnum[]];
const quoteTypeValues = Object.values(QuoteTypeEnum);

const QUOTE_SORTABLE_FIELDS = [
    'id',
    'quoteNo',
    'groupId',
    'status',
    'uiStatus',
    'quoteType',
    'startDate',
    'endDate',
    'expiryDate',
    'createdAt',
    'updatedAt',
    'resellerTotalPrice',
    'endCustomerTotalPrice',
    'endUser',
    'vendor',
];

export const QuoteFilterSchema = extendApi(
    BaseFilterSchema.extend({
        search: z.string().optional().describe('Search query to filter by'),
        status: extendApi(z.array(z.enum(quoteStatusValues)).optional(), {
            description: 'QUOTES.STATUS',
            type: 'array',
            items: {
                type: 'string',
                enum: quoteStatusValues,
            },
        }),

        type: extendApi(z.nativeEnum(QuoteTypeEnum).optional(), {
            description: 'QUOTES.TYPE',
            type: 'string',
            enum: quoteTypeValues,
        }),
        vendorNames: extendApi(z.array(z.string()).nullable().optional(), {
            description: 'List of vendors quote related to',
        }),
        sortBy: extendApi(
            z
                .union([
                    z.literal('id'),
                    z.literal('quoteNo'),
                    z.literal('groupId'),
                    z.literal('status'),
                    z.literal('uiStatus'),
                    z.literal('quoteType'),
                    z.literal('startDate'),
                    z.literal('endDate'),
                    z.literal('expiryDate'),
                    z.literal('createdAt'),
                    z.literal('updatedAt'),
                    z.literal('resellerTotalPrice'),
                    z.literal('endCustomerTotalPrice'),
                    z.literal('endUser'),
                    z.literal('endUserName'),
                    z.literal('vendor'),
                    z.literal('vendorName'),
                ])
                .optional(),
            {
                description: 'Field to sort by',
                type: 'string',
                enum: QUOTE_SORTABLE_FIELDS,
                default: 'quoteNo',
            },
        ),
    }),
    {
        title: 'QuoteFilter',
        description: 'Filter parameters for quotes',
    },
);
