import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { ServiceRoleSchema } from '../../common/dtos';
import { QuoteActionEnum } from '../enums';

export const QuoteHistoryRecordCreateSchema = extendApi(
    z.object({
        quoteId: extendApi(z.string().optional().nullable(), {
            description: 'Quote ID',
        }),

        action: extendApi(z.nativeEnum(QuoteActionEnum), {
            description: 'Quote action',
        }),

        request: extendApi(z.any(), {
            description: 'Quote request data',
        }),

        triggeredBy: ServiceRoleSchema,
    }),
    {
        title: 'QuoteHistoryRecordCreateSchema',
        description: 'Quote History Record model',
    },
);
