import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { CurrencyEnum } from '../../common/enums';
import { QuoteItemSchema } from './quote-item.schema';

export const QuoteItemApiSchema = extendApi(
    QuoteItemSchema.pick({
        id: true,
        itemNo: true,
        quoteId: true,
        vendorId: true,
        serviceLevelSku: true,
        serviceName: true,
        serviceGroupSku: true,
        productSku: true,
        resellerPrice: true,
        // distributorPrice: true, // Hidden because of AH-101
        endCustomerPrice: true,
        _links: true,
        _actions: true,
    })
        .setKey(
            'name',
            extendApi(z.string().nullable().optional(), {
                description: 'Product Name',
            }),
        )
        .setKey('serialNumber', QuoteItemSchema.shape.serialNo)
        .setKey(
            'coverageStatus',
            extendApi(z.string().nullable().optional(), {
                description: 'Coverage Status',
            }),
        )
        .setKey(
            'currency',
            extendApi(z.nativeEnum(CurrencyEnum).nullable().optional(), {
                description: 'Currency code',
            }),
        )
        .set<PERSON>ey(
            'itemsCount',
            extendApi(z.number().nullable().optional(), {
                description: 'Items Count',
            }),
        ),
    {
        title: 'QuoteItem',
        description: 'Quote item model',
    },
);
