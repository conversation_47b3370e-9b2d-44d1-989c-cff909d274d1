import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { CurrencyEnum } from '../../common/enums/currency.enum';
import { RelatedActionSchema } from '../../common/schemas/related-action.schema';
import { RelatedItemLinkSchema } from '../../common/schemas/related-item-link.schema';
import { QuoteUiStatusEnum } from '../enums';

export const QuoteSchema = extendApi(
    z.object({
        id: extendApi(z.string(), {
            description: 'Quote ID',
        }),

        quoteNo: extendApi(z.string().nullable().optional(), {
            description: 'Quote Number',
        }),

        quoteType: extendApi(z.string().nullable().optional(), {
            description: 'Quote Type',
        }),

        status: extendApi(z.string().nullable().optional(), {
            description: 'Quote IAsset Status',
        }),

        uiStatus: z.nativeEnum(QuoteUiStatusEnum).nullable().optional().describe('Quote status'),

        groupId: extendApi(z.string().nullable().optional(), {
            description: 'Group ID',
        }),

        startDate: extendApi(z.string().datetime({ offset: true }).nullable().optional(), {
            description: 'Start Date',
        }),

        endDate: extendApi(z.string().datetime({ offset: true }).nullable().optional(), {
            description: 'End Date',
        }),

        expiryDate: z.string().datetime({ offset: true }).nullable().optional().describe('Expiry Date'),

        currency: extendApi(z.nativeEnum(CurrencyEnum).nullable().optional(), {
            description: 'Currency code',
        }),

        resellerTotalPrice: z.number().nullable().optional().describe('Reseller Total Price'),
        // distributorTotalPrice: z.number().nullable().optional().describe('Distributor Total Price'), // Hidden because of AH-101
        endCustomerTotalPrice: z.number().nullable().optional().describe('End Customer Total Price'),

        vendorId: extendApi(z.string().nullable().optional(), {
            description: 'Vendor ID',
        }),

        distributorId: extendApi(z.string().nullable().optional(), {
            description: 'Distributor ID',
        }),

        resellerId: extendApi(z.string().nullable().optional(), {
            description: 'Reseller ID',
        }),

        endUserId: extendApi(z.string().nullable().optional(), {
            description: 'End User ID',
        }),

        endUser: extendApi(
            z
                .object({
                    id: extendApi(z.string().optional().nullable()),
                    name: extendApi(z.string().optional().nullable()),
                })
                .nullable()
                .optional(),
            {
                description: 'End User',
            },
        ),
        vendor: extendApi(
            z
                .object({
                    id: extendApi(z.string().optional().nullable()),
                    name: extendApi(z.string().optional().nullable()),
                })
                .nullable()
                .optional(),
            {
                description: 'Vendor',
            },
        ),
        // *************************************************************************************************************
        // ** Links section
        // *************************************************************************************************************

        _links: extendApi(
            z.object({
                self: extendApi(RelatedItemLinkSchema.omit({ count: true }), { description: 'Self' }),

                assets: extendApi(RelatedItemLinkSchema, {
                    description: 'Assets',
                }),
                pdf: extendApi(RelatedItemLinkSchema, {
                    description: 'PDF',
                }),
                contracts: extendApi(RelatedItemLinkSchema, {
                    description: 'Contracts',
                }),
                contacts: extendApi(RelatedItemLinkSchema, {
                    description: 'Contacts',
                }),
                requests: extendApi(RelatedItemLinkSchema, {
                    description: 'Requests',
                }),
                parties: extendApi(RelatedItemLinkSchema, {
                    description: 'Parties',
                }),
            }),
            {
                description: 'Links',
            },
        ),

        // *************************************************************************************************************
        // ** Actions section
        // *************************************************************************************************************
        _actions: extendApi(
            z.object({
                requestChange: extendApi(RelatedActionSchema, {
                    description: 'Request Change',
                }),
                approve: extendApi(RelatedActionSchema, { description: 'Approve' }),
                decline: extendApi(RelatedActionSchema, { description: 'Decline' }),
            }),
            {
                description: 'Actions',
            },
        ),
    }),
    {
        title: 'Quote',
        description: 'Quote model',
    },
);
