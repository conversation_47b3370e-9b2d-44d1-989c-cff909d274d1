import { MikroOrmModule } from '@mikro-orm/nestjs';
import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { RbacModule } from '../../rbac/rbac.module';
import { CommonModule } from '../common/common.module';
import { ContactsModule } from '../contacts/contacts.module';
import { Contact } from '../contacts/entities/contact.entity';
import { ContractsModule } from '../contracts/contracts.module';
import { EntitiesModule } from '../entities/entities.module';
import { EntityEntity } from '../entities/entities/entity.entity';
import { FilesModule } from '../files';
import { IAssetModule } from '../iasset/iasset.module';
import { OrganizationsModule } from '../organizations';
import { Product } from '../products/entities/product.entity';
import { ProductsModule } from '../products/products.module';
import { UsersModule } from '../users/users.module';
import { QuotesController } from './controllers';
import { FileQuoteRequestHistory, Quote, QuoteItem, QuoteRequestHistory } from './entities';
import { QuoteUpdatedEventListener } from './listeners';
import { QuoteItemPresenter, QuotePresenter } from './presenters';
import { QuoteRequestHistoryPresenter } from './presenters/quote-request-history.presenter';
import {
    FileQuoteRequestHistoryRepository,
    QuoteItemRepository,
    QuoteItemViewDrizzleRepository,
    QuoteRepository,
    QuoteRequestHistoryRepository,
} from './repositories';
import { QuoteDrizzleRepository } from './repositories/quote.drizzle.repository';
import { EmailService, QuoteItemsService, QuoteRequestHistoryService, QuotesService } from './services';

@Module({
    imports: [
        ConfigModule,
        MikroOrmModule.forFeature([
            Quote,
            QuoteItem,
            Product,
            Contact,
            EntityEntity,
            QuoteRequestHistory,
            FileQuoteRequestHistory,
        ]),
        forwardRef(() => RbacModule),
        forwardRef(() => IAssetModule),
        forwardRef(() => ContactsModule),
        forwardRef(() => CommonModule),
        forwardRef(() => UsersModule),
        forwardRef(() => ProductsModule),
        forwardRef(() => FilesModule),
        forwardRef(() => ContractsModule),
        forwardRef(() => EntitiesModule),
        forwardRef(() => OrganizationsModule),
    ],
    controllers: [QuotesController],
    providers: [
        QuoteRepository,
        QuoteDrizzleRepository,
        QuoteItemRepository,
        QuoteRequestHistoryRepository,
        FileQuoteRequestHistoryRepository,
        EmailService,
        QuotesService,
        QuoteItemsService,
        QuoteRequestHistoryService,
        QuoteItemPresenter,
        QuotePresenter,
        QuoteUpdatedEventListener,
        QuoteRequestHistoryPresenter,
        QuoteItemViewDrizzleRepository,
    ],
    exports: [
        QuoteRepository,
        QuoteDrizzleRepository,
        QuoteItemRepository,
        QuotesService,
        QuoteItemsService,
        QuoteRequestHistoryService,
        QuotePresenter,
        MikroOrmModule.forFeature([
            Quote,
            QuoteItem,
            Product,
            Contact,
            EntityEntity,
            QuoteRequestHistory,
            FileQuoteRequestHistory,
        ]),
    ],
})
export class QuotesModule {}
