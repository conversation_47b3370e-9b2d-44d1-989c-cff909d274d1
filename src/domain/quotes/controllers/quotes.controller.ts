import {
    BadRequestException,
    Body,
    Controller,
    Get,
    Logger,
    Param,
    Post,
    Query,
    Req,
    Res,
    UploadedFile,
    UploadedFiles,
    UseInterceptors,
} from '@nestjs/common';
import { FileFieldsInterceptor, FileInterceptor } from '@nestjs/platform-express';
import { ApiBadRequestResponse, ApiNotFoundResponse, ApiOkResponse, ApiOperation } from '@nestjs/swagger';
import { Response } from 'express';

import { RequestContext } from '../../../auth/context.decorator';
import { RequireResourceAction } from '../../../rbac/permissions.decorator';
import { BaseController } from '../../common/controllers/base.controller';
import { CursorShouldBeANumericStringException } from '../../common/exceptions/cursor-should-be-a-numeric-string.exception';
import { Context, RequestInterface } from '../../common/interfaces';
import { AssetListModel } from '../../common/models/asset-list.model';
import { ErrorModel } from '../../common/models/error.model';
import { ExportFormatModel } from '../../common/models/export-format.model';
import { Pagination } from '../../common/pagination/pagination.decorator';
import { PaginationParamsModel } from '../../common/pagination/pagination.model';
import { ExportFormat } from '../../common/services';
import { ContactListModel } from '../../contacts/models/contact-list.model';
import { ContractsService } from '../../contracts/contracts.service';
import { ContractFilterModel } from '../../contracts/models/contract-filter.model';
import { ContractListModel } from '../../contracts/models/contract-list.model';
import { EntitiesService } from '../../entities/entities.service';
import { EntityListModel } from '../../entities/models/entity-list.model';
import { ActionType, ResourceType } from '../../users/entities/permission.entity';
import { UserService } from '../../users/services/users.service';
import { QuoteRequestHistory } from '../entities';
import {
    ApproveQuoteFileModel,
    ApproveQuoteModel,
    DeclineQuoteModel,
    QuoteFilterModel,
    QuoteIdParamModel,
    QuoteItemFilterModel,
    QuoteListModel,
    QuoteModel,
} from '../models';
import { RequestQuoteFilesModel, RequestQuoteModel } from '../models/request-quote.model';
import { RequestQuoteChangeModel } from '../models/request-quote-change.model';
import { QuoteItemsService, QuoteRequestHistoryService, QuotesService } from '../services';

@Controller('quotes')
export class QuotesController extends BaseController {
    private readonly logger = new Logger(QuotesController.name);

    constructor(
        private readonly userService: UserService,
        private readonly quotesService: QuotesService,
        private readonly quoteRequestHistoryService: QuoteRequestHistoryService,
        private readonly contractsService: ContractsService,
        private readonly quoteItemsService: QuoteItemsService,
        private readonly entitiesService: EntitiesService,
    ) {
        super();
    }

    @RequireResourceAction({ resource: ResourceType.QUOTE, action: ActionType.READ })
    @Get('/')
    @ApiOkResponse({
        description: 'Quotes list',
        type: QuoteListModel,
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    @ApiNotFoundResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async getQuotes(
        @Pagination() paginationParams: PaginationParamsModel,
        @Res() res: Response,
        @Query() query: QuoteFilterModel,
        @RequestContext() context: Context,
    ) {
        const model = await this.quotesService.getQuotesByResellerIds(
            context.resellerIds,
            context.endUserIds,
            paginationParams,
            query,
        );
        return this.sendOk(res, model.getData());
    }

    @RequireResourceAction({ resource: ResourceType.QUOTE, action: ActionType.READ })
    @Get(':quoteId')
    @ApiOkResponse({
        description: 'The quote object',
        type: QuoteModel,
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async getQuoteById(@Res() res: Response, @Param() params: QuoteIdParamModel, @RequestContext() context: Context) {
        const model = await this.quotesService.getQuoteModelById(context, params.quoteId);
        return this.sendOk(res, model);
    }

    @RequireResourceAction({ resource: ResourceType.QUOTE, action: ActionType.READ })
    @Get(':quoteId/pdf')
    @ApiOkResponse({
        description: 'PDF file',
        content: {
            'application/pdf': {
                schema: {
                    type: 'string',
                    format: 'binary',
                },
            },
        },
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async getQuotePdf(@Res() res: Response, @Param() params: QuoteIdParamModel, @RequestContext() context: Context) {
        const iAssetFile = await this.quotesService.getQuotePdf(
            context.resellerIds,
            context.endUserIds,
            params.quoteId,
        );
        const stream = iAssetFile.getStream();
        return this.sendStream(res, stream, 'application/pdf', iAssetFile.getName());
    }

    @RequireResourceAction({ resource: ResourceType.QUOTE_ITEM, action: ActionType.READ })
    @Get(':quoteId/assets')
    @ApiOperation({
        summary: 'Get assets by quote ID',
    })
    @ApiOkResponse({
        description: 'Success',
        type: AssetListModel,
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async getAssetsByQuoteId(
        @Res() res: Response,
        @Param() params: QuoteIdParamModel,
        @Pagination() paginationParams: PaginationParamsModel,
        @Query() filter: QuoteItemFilterModel,
        @RequestContext() context: Context,
    ) {
        if (paginationParams?.cursor && isNaN(Number.parseInt(paginationParams.cursor))) {
            throw new CursorShouldBeANumericStringException();
        }
        const model = await this.quoteItemsService.getAssetsByQuoteId(
            context.resellerIds,
            context.endUserIds,
            params.quoteId,
            paginationParams,
            filter,
        );
        return this.sendOkWithPagination(res, model.getData(), model.getMeta(), Boolean(paginationParams?.limit));
    }

    @RequireResourceAction({ resource: ResourceType.CONTACT, action: ActionType.READ })
    @Get(':quoteId/contacts')
    @ApiOkResponse({
        description: 'The contacts list',
        type: ContactListModel,
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async getContactsByQuoteId(@Res() res: Response, @Param() params: QuoteIdParamModel) {
        const model = await this.quotesService.getContactsByQuoteId(params.quoteId);
        return this.sendOk(res, model.getData());
    }

    @RequireResourceAction(
        { resource: ResourceType.QUOTE, action: ActionType.READ },
        { resource: ResourceType.CONTRACT, action: ActionType.READ },
    )
    @Get(':quoteId/contracts')
    @ApiOkResponse({
        description: 'Contracts list',
        type: ContractListModel,
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async getContracts(
        @Pagination() paginationParams: PaginationParamsModel,
        @Res() res: Response,
        @Param() params: QuoteIdParamModel,
        @Query() query: ContractFilterModel,
        @RequestContext() context: Context,
    ) {
        await this.quotesService.checkIfQuoteExistsByQuoteId(context.resellerIds, context.endUserIds, params.quoteId);

        const model = await this.contractsService.getContractsByQuoteId(
            context.resellerIds,
            context.endUserIds,
            params.quoteId,
            paginationParams,
            query,
        );
        return this.sendOk(res, model.getData());
    }

    @RequireResourceAction({ resource: ResourceType.QUOTE_ITEM, action: ActionType.READ })
    @Get(':quoteId/assets/export')
    @ApiOperation({ summary: 'Export assets by quote ID as CSV or XLSX' })
    @ApiOkResponse({
        description: 'Assets exported successfully',
        content: {
            'text/csv': {
                schema: {
                    type: 'string',
                    format: 'binary',
                },
            },
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': {
                schema: {
                    type: 'string',
                    format: 'binary',
                },
            },
        },
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async exportAssetsByQuoteId(
        @Res() res: Response,
        @Param() params: QuoteIdParamModel,
        @Query() filter: QuoteItemFilterModel,
        @Query() exportFormat: ExportFormatModel,
        @RequestContext() context: Context,
    ) {
        const format = exportFormat.format as ExportFormat;
        const result = await this.quoteItemsService.exportAssetsByQuoteId(
            context.resellerIds,
            context.endUserIds,
            params.quoteId,
            format,
            filter,
        );
        const contentType =
            format === ExportFormat.CSV
                ? 'text/csv'
                : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        return this.sendFile(res, result.data, result.filename, contentType);
    }

    @RequireResourceAction({ resource: ResourceType.QUOTE, action: ActionType.APPROVE })
    @Post(':quoteId/approve')
    @ApiOkResponse()
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    @UseInterceptors(FileInterceptor('attachPo'))
    async approveByQuoteId(
        @Res() res: Response,
        @Param() params: QuoteIdParamModel,
        @Body() body: ApproveQuoteModel,
        @UploadedFile() attachPo: ApproveQuoteFileModel,
        @RequestContext() context: Context,
    ) {
        body.attachPo = attachPo;
        await this.quotesService.approveQuoteByQuoteId(context, params.quoteId, body);
        return this.sendOk(res, null);
    }

    @RequireResourceAction({ resource: ResourceType.QUOTE, action: ActionType.REJECT })
    @Post(':quoteId/decline')
    @ApiOkResponse()
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async declineByQuoteId(
        @Req() req: RequestInterface,
        @Res() res: Response,
        @Param() params: QuoteIdParamModel,
        @Body() body: DeclineQuoteModel,
    ) {
        const keycloakUserId = req.user.sub;
        const user = await this.userService.getUserEntityByKeycloakId(keycloakUserId);
        await this.quotesService.declineQuoteByQuoteId(user, params.quoteId, body);
        return this.sendOk(res, null);
    }

    @RequireResourceAction({ resource: ResourceType.QUOTE, action: ActionType.CREATE })
    @Post()
    @ApiOkResponse()
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    @UseInterceptors(FileFieldsInterceptor([{ name: 'files', maxCount: 10 }]))
    async requestQuote(
        @RequestContext() context: Context,
        @Res() res: Response,
        @Body() body: RequestQuoteModel,
        @UploadedFiles() files: RequestQuoteFilesModel,
    ) {
        body.files = files?.files;

        if (!body.assets?.length && !body.files?.length) {
            throw new BadRequestException('At least one asset or file is required');
        }

        await this.quotesService.requestQuote(context, body);
        return this.sendOk(res, null);
    }

    @RequireResourceAction({ resource: ResourceType.QUOTE, action: ActionType.UPDATE })
    @Post(':quoteId/change-request')
    @ApiOkResponse()
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async requestQuoteChange(
        @RequestContext() context: Context,
        @Res() res: Response,
        @Param() params: QuoteIdParamModel,
        @Body() body: RequestQuoteChangeModel,
    ) {
        await this.quotesService.requestQuoteChange(context.user, params.quoteId, body);
        return this.sendOk(res, null);
    }

    @RequireResourceAction({ resource: ResourceType.QUOTE_ITEM, action: ActionType.READ })
    @Get(':quoteId/requests')
    @ApiOperation({
        summary: 'Get requests by quote ID',
    })
    @ApiOkResponse({
        description: 'Success',
        type: QuoteRequestHistory,
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async getRequestsByQuoteId(
        @Res() res: Response,
        @Param() params: QuoteIdParamModel,
        @RequestContext() context: Context,
    ) {
        await this.quotesService.checkIfQuoteExistsByQuoteId(context.resellerIds, context.endUserIds, params.quoteId);

        const model = await this.quoteRequestHistoryService.getRequestHistory(params.quoteId);

        return this.sendOk(res, model);
    }

    @RequireResourceAction({ resource: ResourceType.ENTITY, action: ActionType.READ })
    @Get(':quoteId/entities')
    @ApiOkResponse({
        description: 'Entity list',
        type: EntityListModel,
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async getEntities(@Res() res: Response, @Param() params: QuoteIdParamModel, @RequestContext() context: Context) {
        const model = await this.entitiesService.findByResellerIdAndQuoteId(
            context.resellerIds,
            context.endUserIds,
            params.quoteId,
        );

        return this.sendOkWithPagination(res, model.getData().data, model.getData().meta, true);
    }
}
