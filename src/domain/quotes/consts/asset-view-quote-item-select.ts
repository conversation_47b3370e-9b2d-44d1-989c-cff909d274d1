import { sql } from 'drizzle-orm';

import * as schema from '../../../../drizzle/schema';

export const AssetViewQuoteItemSelect = {
    id: schema.vQuoteItems.id,
    vendorId: schema.vQuoteItems.vendorId,
    productSku: schema.vQuoteItems.productSku,
    serialNo: schema.vQuoteItems.serialNo,
    contractNo: sql`NULL::text as contract_no`,
    contractStatus: sql`NULL::text as contract_status`,
    quoteNo: schema.vQuoteItems.quoteNo,
    quoteStatus: schema.vQuoteItems.quoteStatus,
    startDate: schema.vQuoteItems.startDate,
    endDate: schema.vQuoteItems.endDate,
    resellerId: schema.vQuoteItems.resellerId,
    endUserId: schema.vQuoteItems.endUserId,
    distributorId: schema.vQuoteItems.distributorId,
    contractId: sql`NULL::text as contract_id`,
    quoteId: schema.vQuoteItems.quoteId,
    productName: schema.vQuoteItems.productName,
    resellerPriceFinal: schema.vQuoteItems.resellerPriceFinal,
    distributorPriceFinal: schema.vQuoteItems.distributorPriceFinal,
    endCustomerPriceFinal: schema.vQuoteItems.endCustomerPriceFinal,
    quantity: schema.vQuoteItems.quantity,
    currency: schema.vQuoteItems.currency,
    coverageStatus: schema.vQuoteItems.coverageStatus,
    serviceGroupSku: schema.vQuoteItems.serviceGroupSku,
    serviceGroupLabel: schema.vQuoteItems.serviceGroupLabel,
    serviceLevelSku: schema.vQuoteItems.serviceLevelSku,
    serviceName: schema.vQuoteItems.serviceName,
    support_life_end_date: schema.vQuoteItems.supportLifeEndDate,
};
