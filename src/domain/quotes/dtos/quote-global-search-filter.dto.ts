import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { QuoteFilterSchema } from '../schemas';

const QuoteGlobalSearchFilterSchema = extendApi(
    QuoteFilterSchema.extend({
        globalSearchPhrase: z.string().optional().describe('Search by global search phrase'),
    }),
    {
        title: 'QuoteGlobalSearchFilter',
        description: 'Filter global search parameters for quotes',
    },
);

export class QuoteGlobalSearchFilterDto extends createZodDto(QuoteGlobalSearchFilterSchema) {}
