import { index, jsonb, numeric, pgEnum, pgTable, text, timestamp } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm/relations';

import { CurrencyEnum } from '../common/enums';
import { DataSourceEnum } from '../contracts/enums/data-source.enum';
import { entities } from '../entities/entity.schema';
import { QuoteUiStatusEnum } from './enums';

export const quoteCurrency = pgEnum('quote_ui_status', CurrencyEnum);
export const quoteUiStatus = pgEnum('quote_ui_status', QuoteUiStatusEnum);

const dataSource = pgEnum('data_source', DataSourceEnum);

export type QuoteDataField = {
    [x: string]: any;

    QuoteId?: number;
    QuoteNo?: string;
    QuoteVersionNo?: number;
    RenewalContactId?: string;
    CustomerContactId?: number;
    EndUserContactId?: number;
    SalesContactId?: number;
    CustomerShippingAddressId?: number;
    BillingAddressId?: number;
    EnduserEntityId?: number;
    SupplierContactId?: number;
    QuoteAmount?: number; // [DEPRECATED] Reseller Total Price - USE TesediResellerTotalPrice INSTEAD
    BuyAmount?: number; // [DEPRECATED] Distributor Total Price - USE TesediDistributorTotalPrice INSTEAD
    TesediResellerTotalPrice?: number;
    TesediDistributorTotalPrice?: number;
    TesediEndCustomerTotalPrice?: number;
    GroupID?: string;
    BillingCycleCode?: string;
    FirstInvoiceDate?: string;
    SellDiscountPercentage?: string;
    SellIncumbentPercentage?: string;
    CustomerNotes?: string;
};

export const quotes = pgTable(
    'quotes',
    {
        id: text().primaryKey().notNull(),
        quoteNo: text('quote_no'),
        quoteType: text('quote_type'),
        status: text(),
        groupId: text('group_id'),
        startDate: text('start_date'),
        endDate: text('end_date'),
        expiryDate: text('expiry_date'),
        currency: text(),
        vendorId: text('vendor_id'),
        distributorId: text('distributor_id'),
        resellerId: text('reseller_id'),
        endUserId: text('end_user_id'),
        data: jsonb().$type<QuoteDataField>(),
        uiStatus: quoteUiStatus('ui_status'),
        resellerTotalPrice: numeric('reseller_total_price', { precision: 20, scale: 4 }),
        distributorTotalPrice: numeric('distributor_total_price', { precision: 20, scale: 4 }),
        endCustomerTotalPrice: numeric('end_customer_total_price', { precision: 20, scale: 4 }),

        createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
        updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }).defaultNow().notNull(),

        source: dataSource('source').default(DataSourceEnum.iAsset).notNull(),
    },
    (table) => [
        index('idx_quotes_quote_no').on(table.quoteNo),
        index('idx_quotes_group_id').on(table.groupId),
        index('idx_quotes_r_id').using('btree', table.resellerId.asc().nullsLast().op('text_ops')),
        index().using('btree', table.distributorId.asc().nullsLast().op('text_ops')),
        index().using('btree', table.endUserId.asc().nullsLast().op('text_ops')),
        index().using('btree', table.vendorId.asc().nullsLast().op('text_ops')),
    ],
);

export const quotesRelations = relations(quotes, ({ one }) => ({
    endUser: one(entities, {
        fields: [quotes.endUserId],
        references: [entities.id],
    }),
}));
