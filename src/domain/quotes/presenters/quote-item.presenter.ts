import { Injectable } from '@nestjs/common';

import { CurrencyEnum } from '../../common/enums';
import { HttpMethodsEnum } from '../../common/enums/http-methods.enum';
import { AssetModel } from '../../common/models/asset.model';
import { AssetType, QuoteItemType } from '../../common/types';
import { QuoteItemApiModel } from '../models';

@Injectable()
export class QuoteItemPresenter {
    toAssetModel(entity: AssetType): AssetModel {
        const model = new AssetModel();
        model.name = entity.name;
        model.vendorId = entity.vendor_id;
        model.serialNumber = entity.serial_number;
        model.coverageStatus = entity.coverage_status;
        model.serviceGroupSku = entity.service_group_sku;
        model.serviceGroupLabel = entity.service_group_label;
        model.serviceLevelSku = entity.service_sku;
        model.productSku = entity.product_sku;
        model.currency = entity.currency;
        model.quantity = +entity.quantity;
        model.itemsCount = entity.items_count ? parseInt(entity.items_count) : null;
        model.resellerPriceFinalSum = entity.reseller_price_final_sum;
        // model.distributorPriceFinalSum = entity.distributor_price_final_sum; // Hidden because of AH-101
        model.endCustomerPriceFinalSum = entity.end_customer_price_final_sum;
        model.startDate = this.adaptDate(entity.start_date);
        model.endDate = this.adaptDate(entity.end_date);
        model.supportLifeEndDate = this.adaptDate(entity.support_life_end_date);

        if (entity.items) {
            model.items = entity.items.map((item) => {
                return this.toQuoteItemApiModel(item as QuoteItemType, entity.currency);
            });
        }

        const vendorId = entity.items[0].vendor_id;
        const serialNo = entity.serial_number;
        const id = entity.items[0].id;
        const assetId = serialNo?.length > 0 ? serialNo : id;

        model._links = {};
        model._links.self = {
            uri: `/assets/${assetId}`,
        };
        model._links.vendor = {
            name: 'N/A',
            uri: `/entities/${vendorId}`,
        };

        return model;
    }

    toQuoteItemApiModel(entity: QuoteItemType, currency: CurrencyEnum): QuoteItemApiModel {
        const model = new QuoteItemApiModel();
        model.id = entity.id;
        model.itemNo = entity.item_no;
        model.quoteId = entity.quote_id;
        model.vendorId = entity.vendor_id;
        model.serialNumber = entity.serial_no;
        model.productSku = entity.product_sku;
        model.serviceLevelSku = entity.service_level_sku;
        model.serviceGroupSku = entity.service_group_sku;
        model.serviceName = entity.service_name;
        model.name = entity.data?.Description || null;
        model.coverageStatus = entity.coverage_status;
        model.resellerPrice = entity.reseller_price;
        model.currency = currency;
        // model.distributorPrice = entity.distributor_price; // Hidden because of AH-101
        model.endCustomerPrice = entity.end_customer_price;

        // Links and actions
        model._links = {
            self: { uri: `/quotes/${entity.quote_id}/assets` },
            vendor: { name: 'N/A', uri: `/entities/${entity.vendor_id}` },
        };

        model._actions = {
            edit: {
                method: HttpMethodsEnum.PATCH,
                uri: `/quotes/${entity.quote_id}/assets`,
            },
        };

        return model;
    }

    private adaptDate(date: string | Date | null | undefined): string | null {
        if (!date) {
            return null;
        }

        if (date instanceof Date) {
            return date.toISOString();
        }

        const dateObj = new Date(date);
        return dateObj.toISOString();
    }
}
