import { Injectable } from '@nestjs/common';
import { InferSelectModel } from 'drizzle-orm';

import * as schema from '../../../../drizzle/schema';
import { CurrencyEnum } from '../../common/enums';
import { HttpMethodsEnum } from '../../common/enums/http-methods.enum';
import { Context } from '../../common/interfaces';
import { ActionType, ResourceType } from '../../users/entities/permission.entity';
import { Quote } from '../entities';
import { QuoteUiStatusEnum } from '../enums';
import { QuoteListItemModel, QuoteModel } from '../models';

type QuoteDrizzle = Partial<InferSelectModel<typeof schema.quotes>> & {
    endUser?: Pick<InferSelectModel<typeof schema.entities>, 'id' | 'name'>;
    vendor?: Pick<InferSelectModel<typeof schema.entities>, 'id' | 'name'>;
};

@Injectable()
export class QuotePresenter {
    toFullModel(context: Context, entity: Quote | QuoteDrizzle): QuoteModel {
        const model = new QuoteModel();
        model.id = entity.id;
        model.quoteNo = entity.quoteNo;
        model.status = entity.status;
        model.uiStatus = entity.uiStatus;
        model.expiryDate = this.adaptDate(entity.expiryDate);
        model.startDate = this.adaptDate(entity.startDate);
        model.endDate = this.adaptDate(entity.endDate);
        model.groupId = entity.groupId;
        // model.distributorTotalPrice = entity.distributorTotalPrice; // Hidden because of AH-101
        model.endCustomerTotalPrice = Number(entity.endCustomerTotalPrice);
        model.resellerTotalPrice = Number(entity.resellerTotalPrice);
        model.currency = CurrencyEnum[entity.currency as keyof typeof CurrencyEnum];
        model.endUserId = entity.endUserId;
        model.resellerId = entity.resellerId;
        model.distributorId = entity.distributorId;
        model.vendorId = entity.vendorId;
        model.endUser = entity?.endUser;

        // Links
        model._links = {};

        model._links.self = {
            uri: `/quotes/${entity.id}`,
        };

        model._links.assets = {
            count: 0,
            uri: `/quotes/${entity.id}/assets`,
        };

        model._links.pdf = {
            count: 0,
            uri: `/quotes/${entity.id}/pdfs`,
        };

        model._links.contracts = {
            count: 0,
            uri: `/quotes/${entity.id}/contracts`,
        };

        model._links.contacts = {
            count: 0,
            uri: `/quotes/${entity.id}/contacts`,
        };

        model._links.requests = {
            count: 0,
            uri: `/quotes/${entity.id}/requests`,
        };

        model._links.parties = {
            count: 0,
            uri: `/quotes/${entity.id}/entities`,
        };

        // Actions
        model._actions = {};

        const isValidApproveDeclineStatus = ![
            QuoteUiStatusEnum.CHANGE_REQUESTED,
            QuoteUiStatusEnum.LOST,
            QuoteUiStatusEnum.ORDERED,
        ].includes(entity.uiStatus);

        const permissions = context.permissions;

        const requestChangeAvailable =
            !!permissions.find(
                (permission) =>
                    (permission.actionType === ActionType.UPDATE && permission.resourceType === ResourceType.QUOTE) ||
                    (permission.actionType === ActionType.MANAGE && permission.resourceType === ResourceType.QUOTE) ||
                    (permission.actionType === ActionType.MANAGE && permission.resourceType === ResourceType.PLATFORM),
            ) && entity.uiStatus === QuoteUiStatusEnum.OPEN;

        const declineAvailable =
            !!permissions.find(
                (permission) =>
                    (permission.actionType === ActionType.REJECT && permission.resourceType === ResourceType.QUOTE) ||
                    (permission.actionType === ActionType.MANAGE && permission.resourceType === ResourceType.QUOTE) ||
                    (permission.actionType === ActionType.MANAGE && permission.resourceType === ResourceType.PLATFORM),
            ) && isValidApproveDeclineStatus;

        const approveAvailable =
            !!permissions.find(
                (permission) =>
                    (permission.actionType === ActionType.APPROVE && permission.resourceType === ResourceType.QUOTE) ||
                    (permission.actionType === ActionType.MANAGE && permission.resourceType === ResourceType.QUOTE) ||
                    (permission.actionType === ActionType.MANAGE && permission.resourceType === ResourceType.PLATFORM),
            ) && isValidApproveDeclineStatus;

        model._actions.requestChange = {
            method: HttpMethodsEnum.POST,
            uri: `/quotes/${entity.id}/change-request`,
            disabled: !requestChangeAvailable,
        };

        model._actions.approve = {
            method: HttpMethodsEnum.POST,
            uri: `/quotes/${entity.id}/approve`,
            disabled: !approveAvailable,
        };

        model._actions.decline = {
            method: HttpMethodsEnum.POST,
            uri: `/quotes/${entity.id}/decline`,
            disabled: !declineAvailable,
        };

        return model;
    }

    toListItemModel(entity: Quote | QuoteDrizzle): QuoteListItemModel {
        const model = new QuoteListItemModel();
        model.id = entity.id;
        model.quoteNo = entity.quoteNo;
        model.status = entity.status;
        model.uiStatus = entity.uiStatus;
        model.expiryDate = this.adaptDate(entity.expiryDate);
        model.groupId = entity.groupId;
        // model.distributorTotalPrice = entity.distributorTotalPrice; // Hidden because of AH-101
        model.resellerTotalPrice = Number(entity.resellerTotalPrice);
        model.endCustomerTotalPrice = Number(entity.endCustomerTotalPrice);
        model.currency = CurrencyEnum[entity.currency as keyof typeof CurrencyEnum];
        model.endUser = entity.endUser;
        model.startDate = this.adaptDate(entity.startDate);
        model.endDate = this.adaptDate(entity.endDate);
        model.vendor = entity?.vendor;

        // Links
        model._links = {};

        model._links.self = {
            uri: `/quotes/${entity.id}`,
        };

        return model;
    }

    private adaptDate(date: string | Date | null | undefined): string | null {
        if (!date) {
            return null;
        }

        if (date instanceof Date) {
            return date.toISOString();
        }

        const dateObj = new Date(date);
        return dateObj.toISOString();
    }
}
