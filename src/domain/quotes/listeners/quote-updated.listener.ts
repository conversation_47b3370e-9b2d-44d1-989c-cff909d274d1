import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';

import { EventNameEnum } from '../../common/enums/event-names.enum';
import { GenericDbEvent } from '../../common/interfaces/generic-db-event.interface';
import { QuoteUiStatusEnum } from '../enums';
import { QuotesService } from '../services';

@Injectable()
export class QuoteUpdatedEventListener {
    private readonly logger = new Logger(QuoteUpdatedEventListener.name);

    constructor(private readonly quoteService: QuotesService) {}

    @OnEvent(EventNameEnum.DB_WATCHER_QUOTE_UPDATED)
    async handleAllEvents(event: GenericDbEvent) {
        this.logger.log(`Received event:`, JSON.stringify(event, null, 2));

        const oldUiStatus = event.changes.changed_fields?.ui_status?.old;
        const newUiStatus = event.changes.changed_fields?.ui_status?.new;

        this.logger.log(`Evaluating isUpdated: old ui_status=${oldUiStatus}, new ui_status=${newUiStatus}`);

        const isUpdated = oldUiStatus === QuoteUiStatusEnum.CHANGE_REQUESTED && newUiStatus === QuoteUiStatusEnum.OPEN;
        this.logger.log(`isUpdated result: ${isUpdated}`);

        this.logger.log(`Evaluating isNew: old ui_status=${oldUiStatus}, new ui_status=${newUiStatus}`);

        const isNew = oldUiStatus !== newUiStatus && newUiStatus === QuoteUiStatusEnum.OPEN;
        this.logger.log(`isNew result: ${isNew}`);

        if (isUpdated) {
            try {
                this.logger.log(`Handling event update for quote ID: ${event.entityId}`);
                await this.quoteService.quoteUpdatedEvent(event.entityId);
                this.logger.log(`Event update for quote ID: ${event.entityId} handled successfully`);
                return;
            } catch (error) {
                this.logger.error(
                    { err: error, quoteId: event.entityId },
                    `Error handling event update for quote ID: ${event.entityId}`,
                );
            }
        }

        if (isNew) {
            try {
                this.logger.log(`Handling new open quote: ${event.entityId}`);
                // @ts-expect-error This will be removed in the future
                const quote = await this.quoteService.getQuoteById(event.entityId, { populate: ['endUser'] });

                await this.quoteService.newQuoteEvent(quote);
                this.logger.log(`New open quote ID: ${event.entityId} handled successfully`);
                return;
            } catch (error) {
                this.logger.error(
                    { err: error, quoteId: event.entityId },
                    `Error handling new open quote ID: ${event.entityId}`,
                );
            }
        }
    }
}
