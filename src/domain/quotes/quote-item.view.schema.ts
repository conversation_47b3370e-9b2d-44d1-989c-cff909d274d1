import { sql } from 'drizzle-orm';
import { integer, jsonb, numeric, pgView, text, varchar } from 'drizzle-orm/pg-core';

export const vQuoteItems = pgView('v_quote_items', {
    id: text(),
    itemNo: integer('item_no'),
    quoteId: text('quote_id'),
    serialNo: text('serial_no'),
    productSku: text('product_sku'),
    serviceLevelSku: text('service_level_sku'),
    serviceGroupSku: text('service_group_sku'),
    resellerPrice: numeric('reseller_price', { precision: 20, scale: 4 }),
    distributorPrice: numeric('distributor_price', { precision: 20, scale: 4 }),
    endCustomerPrice: numeric('end_customer_price', { precision: 20, scale: 4 }),
    quantity: numeric({ precision: 10, scale: 0 }),
    resellerPriceFinal: numeric('reseller_price_final'),
    distributorPriceFinal: numeric('distributor_price_final'),
    endCustomerPriceFinal: numeric('end_customer_price_final'),
    data: jsonb(),
    serviceGroupLabel: text('service_group_label'),
    productName: text('product_name'),
    serviceName: text('service_name'),
    quoteNo: text('quote_no'),
    quoteStatus: text('quote_status'),
    startDate: text('start_date'),
    endDate: text('end_date'),
    coverageStatus: varchar('coverage_status', { length: 255 }),
    currency: text(),
    distributorId: text('distributor_id'),
    vendorId: text('vendor_id'),
    endUserId: text('end_user_id'),
    resellerId: text('reseller_id'),
    supportLifeEndDate: text('support_life_end_date'),
}).as(
    sql`SELECT qi.id, qi.item_no, qi.quote_id, qi.vendor_id, qi.serial_no, qi.product_sku, qi.service_level_sku, qi.service_group_sku, qi.reseller_price, qi.distributor_price, qi.end_customer_price, qi.quantity, qi.reseller_price AS reseller_price_final, qi.distributor_price AS distributor_price_final, qi.end_customer_price AS end_customer_price_final, qi.data, qi.service_group_label, cp.description AS product_name, sp.description AS service_name, q.quote_no, q.status AS quote_status, ae.coverage_status, q.currency, q.distributor_id, q.end_user_id, q.reseller_id, (qi.data ->> 'SupportLifeEndDate') AS support_life_end_date, (qi.data ->> 'StartDate') AS start_date, (qi.data ->> 'EndDate') AS end_date FROM quote_items qi LEFT JOIN products cp ON cp.sku = qi.product_sku AND cp.vendor_id = qi.vendor_id LEFT JOIN products sp ON sp.sku = qi.service_level_sku AND sp.vendor_id = qi.vendor_id LEFT JOIN quotes q ON q.id = qi.quote_id LEFT JOIN asset_enrichments ae ON ae.serial_number = qi.serial_no AND ae.product_sku = qi.product_sku`,
);
