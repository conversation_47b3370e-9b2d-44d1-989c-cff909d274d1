export enum QuoteStatusEnum {
    OPEN = 'Open',
    RENEWED = 'Renewed',
    ORDERED = 'Ordered',
    LOST = 'Lost',
    IN_PREPARATION = 'In Preparation',
    EXPIRES_IN_90_DAYS = 'Expires in 90 days',
    EXPIRES_IN_60_DAYS = 'Expires in 60 days',
    EXPIRES_IN_30_DAYS = 'Expires in 30 days',
    EXPIRED = 'Expired',
    CHANGE_REQUESTED = 'Change Requested',
    CANCELLED = 'Cancelled',
    ARCHIVED = 'Archived',
    ACTIVE = 'Active',
    DISCOUNT_APPROVAL = 'Discount Approval',
    QUOTE_READY = 'Quote Ready',
    CUSTOMER_CONTACT = 'Customer Contact',
    READY_TO_ORDER = 'Ready to Order',
    INVOICED = 'Invoiced',
    CREATED = 'Created',
}

// Created -> Invalid
// Preparation -> Invalid
// Quote Ready -> Invalid
// Cancelled -> Invalid
// Archived -> Invalid
// Renewed -> Invalid
// Expired -> Invalid
// In Preparation -> Invalid
// Customer Contact -> Open
// Customer Order -> Open
// Expires in 90 days -> Open
// Expires in 60 days -> Open
// Expires in 30 days -> Open
// Discount Approval -> Change Requested
// Ready to Order -> Ordered
// Order Confirmed -> Ordered
// Invoiced -> Ordered
// Active -> Open
// Cancelled -> Invalid
// Lost -> Lost
