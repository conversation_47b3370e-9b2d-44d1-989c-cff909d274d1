import { FindOneOptions } from '@mikro-orm/core';
import { InjectRepository } from '@mikro-orm/nestjs';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { and, eq, inArray } from 'drizzle-orm';
import { alias, PgSelect } from 'drizzle-orm/pg-core';
import { DateTime } from 'luxon';

import * as schema from '../../../../drizzle/schema';
import { AssetNotFoundBySerialNoAndSkuException } from '../../assets/exceptions/asset-not-found-by-serial-no-and-sku.exception';
import { Context } from '../../common/interfaces';
import { PaginationParamsModel } from '../../common/pagination/pagination.model';
import {
    ExpressMulterFile,
    FreshdeskService,
    TemplateService,
    TicketPriority,
    TicketStatus,
    TicketType,
} from '../../common/services';
import { getContactTypeByQuote } from '../../contacts';
import { ContactListModel } from '../../contacts/models';
import { ContactListPresenter } from '../../contacts/presenters';
import { ContactDrizzleRepository } from '../../contacts/repositories/contact.drizzle.repository';
import { EntityDrizzleRepository } from '../../entities/repositories/entity.drizzle.repository';
import { FileService } from '../../files';
import { IAssetService } from '../../iasset/iasset.service';
import { IAssetFile } from '../../iasset/iasset-file';
import { OrganizationsService } from '../../organizations';
import { Product } from '../../products/entities/product.entity';
import { ProductRepository } from '../../products/repositories/product.repository';
import { User } from '../../users/entities';
import { UserService } from '../../users/services';
import { QuoteGlobalSearchFilterDto } from '../dtos/quote-global-search-filter.dto';
import { FileQuoteRequestHistory, Quote, QuoteItem } from '../entities';
import { QuoteActionEnum, QuoteStatusEnum, QuoteUiStatusEnum } from '../enums';
import { QuoteNotFoundException } from '../exceptions/quote-not-found.exception';
import {
    ApproveQuoteModel,
    DeclineQuoteModel,
    QuoteFilterModel,
    QuoteListModel,
    QuoteModel,
    RequestQuoteChangeModel,
    RequestQuoteModel,
} from '../models';
import { QuotePresenter } from '../presenters';
import { FileQuoteRequestHistoryRepository, QuoteItemRepository, QuoteRepository } from '../repositories';
import { QuoteDrizzleRepository } from '../repositories/quote.drizzle.repository';
import { EmailService } from './email.service';
import { QuoteRequestHistoryService } from './quote-request-history.service';

@Injectable()
export class QuotesService {
    private readonly logger = new Logger(QuotesService.name);

    constructor(
        private readonly configService: ConfigService,
        private readonly contactListPresenter: ContactListPresenter,
        private readonly quotePresenter: QuotePresenter,
        private readonly iAssetService: IAssetService,
        private readonly freshdeskService: FreshdeskService,
        private readonly templateService: TemplateService,
        private readonly emailService: EmailService,
        private readonly organizationService: OrganizationsService,
        private readonly fileService: FileService,
        private readonly userService: UserService,
        private readonly quoteRequestHistoryService: QuoteRequestHistoryService,
        @InjectRepository(Quote)
        private readonly quoteRepository: QuoteRepository,
        @Inject(QuoteDrizzleRepository)
        private readonly quoteDrizzleRepository: QuoteDrizzleRepository,
        @Inject(ContactDrizzleRepository)
        private readonly contactDrizzleRepository: ContactDrizzleRepository,
        @Inject(EntityDrizzleRepository)
        private readonly entityDrizzleRepository: EntityDrizzleRepository,
        @InjectRepository(Product)
        private readonly productRepository: ProductRepository,
        @InjectRepository(QuoteItem)
        private readonly quoteItemRepository: QuoteItemRepository,
        @InjectRepository(FileQuoteRequestHistory)
        private readonly fileQuoteRequestHistoryRepository: FileQuoteRequestHistoryRepository,
    ) {}

    async checkIfQuoteExistsByQuoteId(
        resellerIds: string[],
        endUserIds: string[] | null,
        quoteId: string,
    ): Promise<void> {
        const res = await this.quoteDrizzleRepository.checkIfQuoteExistsByQuoteId(resellerIds, endUserIds, quoteId);

        if (!res) {
            throw new QuoteNotFoundException();
        }
    }

    async getQuoteById(quoteId: string, options: FindOneOptions<Quote, never, '*', never> = {}): Promise<Quote> {
        const entity = await this.quoteRepository.findOne({ id: quoteId }, options);
        if (!entity) {
            throw new QuoteNotFoundException();
        }

        return entity;
    }

    async getQuoteModelById(context: Context, quoteId: string): Promise<QuoteModel> {
        const quote = await this.quoteDrizzleRepository.findQuoteByResellerIdAndQuoteId(
            context.resellerIds,
            context.endUserIds,
            quoteId,
            true,
        );

        if (!quote) {
            throw new QuoteNotFoundException();
        }

        return this.quotePresenter.toFullModel(context, quote);
    }

    async getQuotesByResellerIds(
        resellerIds: string[],
        endUserIds: string[] | null,
        paginationParams: PaginationParamsModel,
        filter?: QuoteFilterModel | QuoteGlobalSearchFilterDto,
    ): Promise<QuoteListModel> {
        const vendorSchema = alias(schema.entities, 'vendor');
        const paginatedResult = await this.quoteDrizzleRepository.findQuotesByResellerIds(
            resellerIds,
            endUserIds,
            paginationParams,
            filter,
            {
                id: schema.quotes.id,
                quoteNo: schema.quotes.quoteNo,
                status: schema.quotes.status,
                uiStatus: schema.quotes.uiStatus,
                expiryDate: schema.quotes.expiryDate,
                groupId: schema.quotes.groupId,
                resellerTotalPrice: schema.quotes.resellerTotalPrice,
                endCustomerTotalPrice: schema.quotes.endCustomerTotalPrice,
                currency: schema.quotes.currency,
                startDate: schema.quotes.startDate,
                endDate: schema.quotes.endDate,
                endUserName: schema.entities.name,
                vendorName: vendorSchema.name,
                endUser: {
                    id: schema.entities.id,
                    name: schema.entities.name,
                },
                vendor: {
                    id: vendorSchema.id,
                    name: vendorSchema.name,
                },
            },
            (query: PgSelect) => {
                query
                    .innerJoin(schema.entities, eq(schema.entities.id, schema.quotes.endUserId))
                    .innerJoin(
                        vendorSchema,
                        and(
                            eq(vendorSchema.id, schema.quotes.vendorId),
                            filter?.vendorNames?.length ? inArray(vendorSchema.name, filter.vendorNames) : undefined,
                        ),
                    );
            },
        );

        if (!paginatedResult) {
            return new QuoteListModel();
        }

        const items = paginatedResult.data.map((entity) => this.quotePresenter.toListItemModel(entity));
        const model = new QuoteListModel();
        model.setData(items, paginatedResult.meta);

        return model;
    }

    async globalSearch(resellerIds: string[], endUserIds: string[] | null, searchPhrase?: string) {
        return this.quoteDrizzleRepository.globalSearch(resellerIds, endUserIds, searchPhrase);
    }

    async getQuotesByResellerAndEndUserId(
        resellerIds: string[],
        endUserIds: string[] | null,
        endUserId: string,
        paginationParams: PaginationParamsModel,
        filter?: QuoteFilterModel,
    ): Promise<QuoteListModel> {
        const vendorSchema = alias(schema.entities, 'vendor');

        const paginatedResult = await this.quoteDrizzleRepository.findQuotesByResellerAndEndUserId(
            resellerIds,
            endUserIds,
            endUserId,
            paginationParams,
            filter,
            {
                id: schema.quotes.id,
                quoteNo: schema.quotes.quoteNo,
                status: schema.quotes.status,
                uiStatus: schema.quotes.uiStatus,
                expiryDate: schema.quotes.expiryDate,
                groupId: schema.quotes.groupId,
                resellerTotalPrice: schema.quotes.resellerTotalPrice,
                endCustomerTotalPrice: schema.quotes.endCustomerTotalPrice,
                currency: schema.quotes.currency,
                startDate: schema.quotes.startDate,
                endDate: schema.quotes.endDate,
                endUserName: schema.entities.name,
                endUser: {
                    id: schema.entities.id,
                    name: schema.entities.name,
                },
                vendor: {
                    id: vendorSchema.id,
                    name: vendorSchema.name,
                },
            },
            (query: PgSelect) => {
                query
                    .innerJoin(schema.entities, eq(schema.entities.id, schema.quotes.endUserId))
                    .innerJoin(
                        vendorSchema,
                        and(
                            eq(vendorSchema.id, schema.quotes.vendorId),
                            filter?.vendorNames?.length ? inArray(vendorSchema.name, filter.vendorNames) : undefined,
                        ),
                    );
            },
        );

        if (!paginatedResult) {
            return new QuoteListModel();
        }

        const items = paginatedResult.data.map((entity) => this.quotePresenter.toListItemModel(entity));
        const model = new QuoteListModel();
        model.setData(items, paginatedResult.meta);

        return model;
    }

    async getQuotesByResellerAndSerialNumber(
        resellerIds: string[],
        endUserIds: string[] | null,
        serialNo: string,
        paginationParams: PaginationParamsModel,
        filter?: QuoteFilterModel,
    ): Promise<QuoteListModel> {
        const paginatedResult = await this.quoteDrizzleRepository.findQuotesByResellerAndSerialNumber(
            resellerIds,
            endUserIds,
            serialNo,
            paginationParams,
            filter,
        );

        if (!paginatedResult) {
            return new QuoteListModel();
        }

        const items = paginatedResult.data.map((entity) => this.quotePresenter.toListItemModel(entity));
        const model = new QuoteListModel();
        model.setData(items, paginatedResult.meta);

        return model;
    }

    async getQuotesByContract(
        resellerIds: string[],
        endUserIds: string[] | null,
        contractId: string,
        paginationParams: PaginationParamsModel,
        filter?: QuoteFilterModel,
    ): Promise<QuoteListModel> {
        const paginatedResult = await this.quoteDrizzleRepository.findQuotesByContract(
            resellerIds,
            endUserIds,
            contractId,
            paginationParams,
            filter,
        );

        if (!paginatedResult) {
            return new QuoteListModel();
        }

        const items = paginatedResult.data.map((entity) => this.quotePresenter.toListItemModel(entity));
        const model = new QuoteListModel();
        model.setData(items, paginatedResult.meta);

        return model;
    }

    async getContactsByQuoteId(quoteId: string): Promise<ContactListModel> {
        const quote = await this.quoteRepository.findOne({ id: quoteId });

        if (!quote) {
            throw new QuoteNotFoundException();
        }

        const ids: string[] = [
            quote.data?.RenewalContactId,
            quote.data?.CustomerContactId,
            quote.data?.EndUserContactId,
        ]
            .filter((id) => !!id)
            .map((id) => String(id));

        const contacts = await this.contactDrizzleRepository.findContactByIds(ids);

        return this.contactListPresenter.toModel(
            contacts.map((contact) => ({
                ...contact,
                type: getContactTypeByQuote(quote, contact),
            })),
        );
    }

    async getQuotePdf(resellerIds: string[], endUserIds: string[] | null, quoteId: string): Promise<IAssetFile> {
        const quoteNo = await this.quoteDrizzleRepository.findQuoteNumberByQuoteId(resellerIds, endUserIds, quoteId);

        if (!quoteNo) {
            throw new QuoteNotFoundException();
        }

        const iAssetFile = await this.iAssetService.getDownloadQuotePdfFromIAsset(quoteNo);

        if (!iAssetFile.getStream()) {
            throw new Error('Failed to get stream for download');
        }

        return iAssetFile;
    }

    async approveQuoteByQuoteId(context: Context, quoteId: string, data: ApproveQuoteModel): Promise<void> {
        const { user: actor } = context;

        this.logger.log('Quote approval started', {
            quoteId,
            userId: actor.id,
            poNumber: data.poNumber,
            hasAttachment: !!data.attachPo,
            organizationId: context.organizationId,
        });

        try {
            const quote = await this.getQuoteById(quoteId);
            this.logger.debug('Quote retrieved for approval', {
                quoteId,
                quoteNumber: quote.quoteNo,
                currentStatus: quote.uiStatus,
                resellerId: quote.resellerId,
                totalPrice: quote.resellerTotalPrice,
                currency: quote.currency,
            });

            const organization = await this.organizationService.getOrganizationByEntityId(quote.resellerId);
            if (!organization) {
                this.logger.error('Organization not found for quote approval', {
                    quoteId,
                    resellerId: quote.resellerId,
                    userId: actor.id,
                });
                throw new Error('Organization not found');
            }

            const ticketId = await this.createApproveQuoteFreshdeskTicket(
                actor,
                quote,
                data.poNumber,
                data.comment,
                data.attachPo,
            );
            this.logger.debug('Freshdesk ticket created for quote approval', {
                quoteId,
                ticketId,
                userId: actor.id,
            });

            // Update quote status
            await this.quoteRepository.updateUiStatus(quote, QuoteUiStatusEnum.ORDERED);
            this.logger.debug('Quote status updated to ORDERED', {
                quoteId,
                previousStatus: quote.uiStatus,
                newStatus: QuoteUiStatusEnum.ORDERED,
            });

            const quoteRequestHistory = await this.quoteRequestHistoryService.createHistoryRecord({
                quoteId,
                action: QuoteActionEnum.APPROVAL,
                request: {
                    ticketId,
                    userId: actor.id,
                    quoteNumber: quote.quoteNo,
                    poNumber: data.poNumber,
                    comment: data.comment,
                    resellerPrice: quote.resellerTotalPrice,
                    currency: quote.currency,
                },
                triggeredBy: {
                    userId: actor.id,
                },
            });
            this.logger.debug('Quote approval history record created', {
                quoteId,
                historyId: quoteRequestHistory.id,
                userId: actor.id,
            });

            if (data.attachPo) {
                const fileEntry = await this.fileService.createFile(context, {
                    file: data.attachPo,
                    type: 'quote-approve',
                });
                if (fileEntry) {
                    await this.fileQuoteRequestHistoryRepository.linkFileToQuoteRequestHstory({
                        file: fileEntry,
                        quoteRequestHistory,
                    });
                    this.logger.debug('PO attachment processed for quote approval', {
                        quoteId,
                        fileId: fileEntry.id,
                        fileName: data.attachPo.originalname,
                        userId: actor.id,
                    });
                }
            }

            await this.emailService.sendQuoteApprovalConfirmationEmail({
                firstName: actor.firstName,
                lastName: actor.lastName,
                email: actor.email,
                ccEmails: [],
                locale: actor.locale,
                documentNumber: quote.quoteNo,
                startDate: new Date(quote.startDate).toLocaleDateString(),
                poNumber: data.poNumber,
                totalSellPrice: new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: quote.currency,
                }).format(Number(quote.resellerTotalPrice)),
                organizationId: organization.id,
            });

            this.logger.log('Quote approved successfully', {
                quoteId,
                quoteNumber: quote.quoteNo,
                userId: actor.id,
                poNumber: data.poNumber,
                totalPrice: quote.resellerTotalPrice,
                currency: quote.currency,
                ticketId,
                organizationId: organization.id,
            });
        } catch (error) {
            this.logger.error('Quote approval failed', {
                quoteId,
                userId: actor.id,
                poNumber: data.poNumber,
                organizationId: context.organizationId,
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined,
            });
            throw error;
        }
    }

    async quoteUpdatedEvent(quoteId: string): Promise<void> {
        const uri = this.configService.get<string>('FRONTEND_QUOTE_URI');
        const quote = await this.getQuoteById(quoteId, {
            // @ts-expect-error - TODO: fix this
            populate: ['endUser'],
        });

        const organization = await this.organizationService.getOrganizationByEntityId(quote.resellerId);
        if (!organization) {
            this.logger.warn('No organization connected to entity');
            return;
        }

        this.logger.log(`Quote updated logic for : ${quoteId}`, JSON.stringify(quote, null, 2));

        const latestRequestActorEmail = await this.getLatestChangeRequestActorEmail(quote);
        const renewalContactEmail = await this.getRenewalContactEmail(quote);
        if (!renewalContactEmail && !latestRequestActorEmail) {
            this.logger.warn('No renewal contact or latest request actor email found');
            return;
        }

        const email = latestRequestActorEmail || renewalContactEmail;
        if (!email) {
            this.logger.warn(`No email found for quote updated event - ${quoteId}`);
            return;
        }
        const user = await this.userService.getUserByEmail(email);
        let locale = organization.locale;
        if (user) {
            locale = user.locale;
        }

        await this.emailService.sendQuoteUpdatedEmail({
            email,
            locale,
            ccEmails: latestRequestActorEmail ? [renewalContactEmail] : [],
            documentNumber: quote.quoteNo,
            startDate: DateTime.fromJSDate(quote.startDate).toFormat('dd.MM.yyyy'),
            endDate: DateTime.fromJSDate(quote.endDate).toFormat('dd.MM.yyyy'),
            customerName: quote.endUser.name,
            url: `${uri}/${quoteId}`,
            organizationId: organization.id,
            resellerPrice: new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: quote.currency,
            }).format(Number(quote.resellerTotalPrice)),
        });

        this.logger.log(`Quote updated email sent for : ${quoteId}`);
    }

    async newQuoteEvent(quote: Quote): Promise<void> {
        this.logger.log(`New Quote logic for : ${quote.id}`, JSON.stringify(quote, null, 2));

        const uri = this.configService.get<string>('FRONTEND_QUOTE_URI');

        const organization = await this.organizationService.getOrganizationByEntityId(quote.resellerId);
        if (!organization) {
            this.logger.warn('No organization connected to entity');
            return;
        }

        const resellerContactEmail = await this.getResellerContactEmail(quote);
        const renewalContactEmail = await this.getRenewalContactEmail(quote);
        if (!renewalContactEmail && !resellerContactEmail) {
            this.logger.warn('No reseller contact or renewal contact email found');
            return;
        }

        let salesMessage: string | null = null;
        if (quote.data?.CustomerNotes) {
            salesMessage = `<p>${quote.data.CustomerNotes.replace(/\n/g, '<br>')}</p>`;
        }

        const email = resellerContactEmail || renewalContactEmail;
        if (!email) {
            this.logger.warn(`No email found for quote updated event - ${quote.id}`);
            return;
        }
        const user = await this.userService.getUserByEmail(email);
        let locale = organization.locale;
        if (user) {
            locale = user.locale;
        }

        await this.emailService.sendNewQuoteAvailableEmail({
            email,
            locale,
            ccEmails: resellerContactEmail ? [renewalContactEmail] : [],
            documentNumber: quote.quoteNo,
            startDate: DateTime.fromJSDate(quote.startDate).toFormat('dd.MM.yyyy'),
            endDate: DateTime.fromJSDate(quote.endDate).toFormat('dd.MM.yyyy'),
            customerName: quote.endUser.name,
            url: `${uri}/${quote.id}`,
            organizationId: organization.id,
            salesMessage,
            resellerPrice: new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: quote.currency,
            }).format(Number(quote.resellerTotalPrice)),
        });

        this.logger.log(`New Quote email sent for : ${quote.id}`);
    }

    async declineQuoteByQuoteId(
        actor: typeof schema.users.$inferSelect,
        quoteId: string,
        data: DeclineQuoteModel,
    ): Promise<void> {
        this.logger.log('Quote decline started', {
            quoteId,
            userId: actor.id,
            declineReason: data.declineReason,
        });

        try {
            const quote = await this.getQuoteById(quoteId);
            this.logger.debug('Quote retrieved for decline', {
                quoteId,
                quoteNumber: quote.quoteNo,
                currentStatus: quote.uiStatus,
                resellerId: quote.resellerId,
                totalPrice: quote.resellerTotalPrice,
                currency: quote.currency,
            });

            const ticketId = await this.createDeclineQuoteFreshdeskTicket(actor, quote, data.declineReason);
            this.logger.debug('Freshdesk ticket created for quote decline', {
                quoteId,
                ticketId,
                userId: actor.id,
                declineReason: data.declineReason,
            });

            // Update quote status
            await this.quoteRepository.updateUiStatus(quote, QuoteUiStatusEnum.LOST);
            this.logger.debug('Quote status updated to LOST', {
                quoteId,
                previousStatus: quote.uiStatus,
                newStatus: QuoteUiStatusEnum.LOST,
            });

            await this.quoteRequestHistoryService.createHistoryRecord({
                quoteId,
                action: QuoteActionEnum.DECLINE,
                request: {
                    ticketId,
                    userId: actor.id,
                    quoteNumber: quote.quoteNo,
                    declineReason: data.declineReason,
                    resellerPrice: quote.resellerTotalPrice,
                    currency: quote.currency,
                },
                triggeredBy: {
                    userId: actor.id,
                },
            });

            this.logger.log('Quote declined successfully', {
                quoteId,
                quoteNumber: quote.quoteNo,
                userId: actor.id,
                declineReason: data.declineReason,
                totalPrice: quote.resellerTotalPrice,
                currency: quote.currency,
                ticketId,
            });
        } catch (error) {
            this.logger.error('Quote decline failed', {
                quoteId,
                userId: actor.id,
                declineReason: data.declineReason,
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined,
            });
            throw error;
        }
    }

    async requestQuote(context: Context, data: RequestQuoteModel): Promise<void> {
        const { user: actor, entityId } = context;

        const entityName = await this.entityDrizzleRepository.findEntityNameById(entityId);
        const entity = await this.entityDrizzleRepository.findById(entityId);
        const organization = await this.organizationService.getOrganizationByEntityId(entityId);

        const customerId = data.customerId ?? null;
        let customerName: string | null = data.customerName ?? null;
        if (customerId) {
            customerName = await this.entityDrizzleRepository.findEntityNameById(customerId);
        }

        const assets = [];
        for (const asset of data.assets) {
            const serviceGroupSku = asset.serviceGroupSku ?? null;
            let serviceGroup = null;
            if (serviceGroupSku) {
                serviceGroup = await this.productRepository.findOne({
                    sku: asset.serviceGroupSku,
                    vendorId: asset.serviceGroupVendor,
                });
                if (!serviceGroup) {
                    throw new Error(
                        `Provided combination of service group sku ${asset.serviceGroupSku} and service group vendor ${asset.serviceGroupVendor} does not exist`,
                    );
                }
            }

            assets.push({
                serviceGroupVendor: serviceGroup?.vendor ?? null,
                serviceGroupSku: serviceGroup?.sku ?? null,
                serviceGroupDescription: serviceGroup?.description ?? null,
                serialNumber: asset.serialNumber,
                productSku: asset.productSku ?? null,
                endDate: asset.endDate ? new Date(asset.endDate) : null,
            });
        }

        // TODO: Create quote request history record

        const ticketId = await this.createQuoteRequestFreshdeskTicket(
            actor,
            entityName,
            customerName,
            assets,
            data.message ?? null,
            data.files ?? [],
            customerId ? null : data.address,
            customerId ? null : data.contact,
            entity.data?.AccountManagerId,
        );

        const quoteRequestHistory = await this.quoteRequestHistoryService.createHistoryRecord({
            action: QuoteActionEnum.REQUEST_QUOTE,
            request: {
                ticketId,
                userId: actor.id,
                customerName,
                assets,
                ...(customerId ? {} : { address: data.address, contact: data.contact }),
            },
            triggeredBy: {
                userId: actor.id,
            },
        });

        if (data.files?.length) {
            for (const file of data.files) {
                const fileEntry = await this.fileService.createFile(context, {
                    file,
                    type: 'quote-request',
                });

                if (fileEntry) {
                    await this.fileQuoteRequestHistoryRepository.linkFileToQuoteRequestHstory({
                        file: fileEntry,
                        quoteRequestHistory,
                    });
                }
            }
        }

        await this.emailService.sendRequestQuoteConfirmationEmail({
            firstName: actor.firstName,
            lastName: actor.lastName,
            email: actor.email,
            ccEmails: [],
            locale: actor.locale,
            customerName,
            assets: data.assets.map((asset) => ({
                serial: asset.serialNumber,
                productSku: asset.productSku,
                serviceGroup: asset.serviceGroupSku,
                endDate: DateTime.fromISO(asset.endDate).toFormat('dd.MM.yyyy'),
            })),
            message: data.message,
            organizationId: organization.id,
        });
    }

    async requestQuoteChange(actor: User, quoteId: string, data: RequestQuoteChangeModel): Promise<void> {
        const quote = await this.getQuoteById(quoteId);
        await this.quoteRepository.populate(quote, ['reseller', 'endUser']);

        const changedAssets = [];
        const cancelledAssets = [];

        for (const assetItem of data.assets) {
            const assetData = await this.getChangeRequestAsset(quote, assetItem.productSku, assetItem.serialNumber);

            let newServiceGroup: Product | null = null;
            let oldServiceGroup: Product | null = null;
            if (assetItem.newServiceGroupVendor) {
                newServiceGroup = await this.productRepository.findOne({
                    vendorId: assetItem.newServiceGroupVendor,
                    sku: assetItem.newServiceGroupSku,
                });
            }
            if (assetItem.oldServiceGroupVendor) {
                oldServiceGroup = await this.productRepository.findOne({
                    vendorId: assetItem.oldServiceGroupVendor,
                    sku: assetItem.oldServiceGroupSku,
                });
            }

            const item = {
                groupId: quote.groupId,
                sarNumber: assetData.sarNumber,
                serialNumber: assetData.serialNumber,
                assetSku: assetData.assetSku,
                assetDescription: assetData.assetDescription,
                newServiceGroupSku: newServiceGroup?.sku ?? null,
                newServiceGroupDescription: newServiceGroup?.description ?? null,
                oldServiceGroupSku: oldServiceGroup?.sku ?? null,
                oldServiceGroupDescription: oldServiceGroup?.description ?? null,
            };

            if (newServiceGroup) {
                changedAssets.push(item);
            } else {
                cancelledAssets.push(item);
            }
        }

        // We change quote status to DISCOUNT_APPROVAL to fetch unchanged status from iAsset
        await this.quoteRepository.updateUiStatus(quote, QuoteUiStatusEnum.CHANGE_REQUESTED);
        await this.quoteRepository.updateStatus(quote, QuoteStatusEnum.DISCOUNT_APPROVAL);

        const ticketId = await this.createQuoteChangeRequestFreshdeskTicket(
            actor,
            quote,
            changedAssets,
            cancelledAssets,
            data.message ?? null,
            data.cancellationReason ?? null,
        );

        await this.quoteRequestHistoryService.createHistoryRecord({
            quoteId,
            action: QuoteActionEnum.REQUEST_CHANGE,
            request: {
                ticketId,
                userId: actor.id,
                quoteNumber: quote.quoteNo,
                cancellationReason: data.cancellationReason,
                message: data.message,
                assets: data.assets,
                resellerPrice: quote.resellerTotalPrice,
                currency: quote.currency,
            },
            triggeredBy: {
                userId: actor.id,
            },
        });
    }

    private async getResellerContactEmail(quote: Quote) {
        const resellerContactId = quote.data?.CustomerContactId;
        if (!resellerContactId) {
            // TODO: notify team about missing reseller contact
            this.logger.warn(`No reseller contact found for quote: ${quote.id}`);
            return;
        }

        const resellerContact = await this.contactDrizzleRepository.findContactById(resellerContactId.toString());
        if (!resellerContact) {
            // TODO: notify team about missing reseller contact
            this.logger.warn(`No reseller contact found for quote: ${quote.id}`);
            return;
        }

        if (!resellerContact.data?.EmailAddress) {
            // TODO: notify team about missing reseller contact
            this.logger.warn(`No email address found for reseller contact: ${quote.id}`);
            return;
        }

        return resellerContact.data.EmailAddress;
    }

    private async getRenewalContactEmail(quote: Quote) {
        const renewalContactId = quote.data?.RenewalContactId;
        if (!renewalContactId) {
            this.logger.warn(`No renewal contact found for quote: ${quote.id}`);
            return;
        }

        const renewalContact = await this.contactDrizzleRepository.findContactById(renewalContactId);
        if (!renewalContact) {
            this.logger.warn(`No renewal contact found for quote: ${quote.id}`);
            return;
        }

        if (!renewalContact.data?.EmailAddress) {
            this.logger.warn(`No email address found for renewal contact: ${quote.id}`);
            return;
        }

        return renewalContact.data.EmailAddress;
    }

    private async getLatestChangeRequestActorEmail(quote: Quote) {
        const latestRequest = await this.quoteRequestHistoryService.getLatestRequest(quote.id);
        if (!latestRequest) {
            // TODO: notify team about missing latest request
            this.logger.warn(`No latest request found for quote: ${quote.id}`);
            return null;
        }

        if (!latestRequest.user?.email) {
            // TODO: notify team about missing latest request
            this.logger.warn(`No actor's email found in latest request for quote: ${quote.id}`);
            return null;
        }

        return latestRequest.user.email;
    }

    private async createApproveQuoteFreshdeskTicket(
        user: User,
        quote: Quote,
        poNumber: string | null,
        comment: string | null,
        file: ExpressMulterFile | null,
    ) {
        await this.quoteRepository.populate(quote, ['reseller', 'endUser', 'quoteItems']);

        const companyId = await this.freshdeskService.createCompany({
            name: quote.reseller.name,
        });
        const contactId = await this.freshdeskService.createContact({
            name: `${user.firstName} ${user.lastName}`,
            email: user.email,
            companyId,
        });
        const currency = quote.currency ? quote.currency.valueOf() : '';

        if (file) {
            file.objectType = 'ExpressMulterFile';
        }

        const ticketId = await this.freshdeskService.createTicket({
            contactId,
            subject: `Quote approved - ${quote.quoteNo}`,
            ticketType: TicketType.APPROVED,
            ticketStatus: TicketStatus.OPEN,
            ticketPriority: TicketPriority.HIGH,
            description: await this.templateService.render('freshdesk/approve-quote', {
                quoteNumber: quote.quoteNo,
                poNumber: poNumber,
                resellerPrice: new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: quote.currency,
                }).format(Number(quote.resellerTotalPrice)),
                currency: currency,
                comment: comment,
            }),
            attachments: file ? [file] : [],
            tags: [this.freshdeskService.getCapitalizedEnvironment()],
            ampId: quote.groupId,
            quoteId: quote.quoteNo,
            responderId: await this.findAgentIdByEmailInDocumentRenewalContacts(quote),
            customerName: quote.endUser.name,
        });

        return ticketId;
    }

    private async findAgentIdByEmailInDocumentRenewalContacts(quote: Quote) {
        const contactId: string = quote.data?.RenewalContactId?.toString();
        if (!contactId) {
            return null;
        }

        const contact = await this.contactDrizzleRepository.findContactById(contactId);
        if (!contact) {
            return null;
        }
        if (!contact.data?.EmailAddress) {
            return null;
        }
        const email = contact.data.EmailAddress;
        const agentId = await this.freshdeskService.findAgentIdByEmail(email);
        if (!agentId) {
            return null;
        }
        return agentId;
    }

    private async findAgentIdByAccountManagerId(accountManagerId: string) {
        if (!accountManagerId) {
            return null;
        }

        const contact = await this.contactDrizzleRepository.findContactById(accountManagerId);
        if (!contact) {
            return null;
        }
        if (!contact.data?.EmailAddress) {
            return null;
        }
        const email = contact.data.EmailAddress;
        const agentId = await this.freshdeskService.findAgentIdByEmail(email);
        if (!agentId) {
            return null;
        }
        return agentId;
    }

    private async createDeclineQuoteFreshdeskTicket(
        user: typeof schema.users.$inferSelect,
        quote: Quote,
        declineReason: string | null,
    ) {
        await this.quoteRepository.populate(quote, ['reseller', 'endUser']);

        const companyId = await this.freshdeskService.createCompany({
            name: quote.reseller.name,
        });
        const contactId = await this.freshdeskService.createContact({
            name: `${user.firstName} ${user.lastName}`,
            email: user.email,
            companyId,
        });

        const ticketId = await this.freshdeskService.createTicket({
            contactId,
            subject: `Quote declined - ${quote.quoteNo}`,
            ticketType: TicketType.DECLINED,
            ticketStatus: TicketStatus.OPEN,
            ticketPriority: TicketPriority.LOW,
            description: await this.templateService.render('freshdesk/decline-quote', {
                quoteNumber: quote.quoteNo,
                message: declineReason,
            }),
            attachments: [],
            tags: [this.freshdeskService.getCapitalizedEnvironment()],
            ampId: quote.groupId,
            quoteId: quote.quoteNo,
            responderId: await this.findAgentIdByEmailInDocumentRenewalContacts(quote),
            customerName: quote.endUser.name,
        });

        return ticketId;
    }

    private async createQuoteRequestFreshdeskTicket(
        user: User,
        entityName: string,
        customerName: string | null,
        assets: any[],
        message: string | null,
        files: any[],
        address: any,
        contact: any,
        accountManagerId: string | null,
    ) {
        const resellerName = entityName;

        const companyId = await this.freshdeskService.createCompany({
            name: resellerName,
        });
        const contactId = await this.freshdeskService.createContact({
            name: `${user.firstName} ${user.lastName}`,
            email: user.email,
            companyId,
        });

        if (files) {
            for (const file of files) {
                file.objectType = 'ExpressMulterFile';
            }
        }

        const ticketId = await this.freshdeskService.createTicket({
            contactId,
            subject: `Quote request`,
            ticketType: TicketType.NEW_BUSINESS,
            ticketStatus: TicketStatus.OPEN,
            ticketPriority: TicketPriority.LOW,
            description: await this.templateService.render('freshdesk/request-quote', {
                assets: assets,
                message: message,
                endCustomer: customerName,
                address: address,
                contact: contact,
            }),
            attachments: files || [],
            tags: [this.freshdeskService.getCapitalizedEnvironment()],
            responderId: (await this.findAgentIdByAccountManagerId(accountManagerId)) ?? '',
            customerName: customerName,
        });

        return ticketId;
    }

    private async getChangeRequestAsset(quote: Quote, productSku: string, serialNumber: string): Promise<any> {
        const quoteItem = await this.quoteItemRepository.findOne({
            quoteId: quote.id,
            productSku,
            serialNo: serialNumber,
        });

        if (!quoteItem) {
            throw new AssetNotFoundBySerialNoAndSkuException(serialNumber, productSku);
        }

        const product = await this.productRepository.findOne({
            sku: quoteItem.productSku,
            vendorId: quoteItem.vendorId,
        });

        return {
            sarNumber: quoteItem.data?.SAR ?? null,
            serialNumber: quoteItem.serialNo,
            assetSku: quoteItem.productSku,
            assetDescription: product?.description,
        };
    }

    private async createQuoteChangeRequestFreshdeskTicket(
        user: User,
        quote: Quote,
        changedAssets: any[],
        cancelledAssets: any[],
        message: string | null,
        cancellationReason: string | null,
    ) {
        const companyId = await this.freshdeskService.createCompany({
            name: quote.reseller?.name ?? 'N/A',
        });
        const contactId = await this.freshdeskService.createContact({
            name: `${user.firstName} ${user.lastName}`,
            email: user.email,
            companyId,
        });

        let title = 'Quote change request';
        if (quote.groupId) {
            title += ` - ${quote.groupId}`;
        }
        if (quote.quoteNo) {
            title += ` - ${quote.quoteNo}`;
        }

        const ticketId = await this.freshdeskService.createTicket({
            contactId,
            subject: title,
            ticketType: TicketType.QUOTE_CHANGE,
            ticketStatus: TicketStatus.OPEN,
            ticketPriority: TicketPriority.LOW,
            description: await this.templateService.render('freshdesk/request-quote-change', {
                changedAssets,
                cancelledAssets,
                message,
                cancellationReason,
            }),
            attachments: [],
            tags: [this.freshdeskService.getCapitalizedEnvironment()],
            ampId: quote.groupId,
            quoteId: quote.quoteNo,
            responderId: await this.findAgentIdByEmailInDocumentRenewalContacts(quote),
            customerName: quote.endUser.name,
        });

        return ticketId;
    }
}
