import { Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { EmailTemplateEnum } from '../../common/enums';
import { EmailService as CommonEmailService } from '../../common/services/email';
import { OrganizationsService } from '../../organizations';

interface BaseEmailParams {
    organizationId: string;
    locale: string;
}

interface SendQuoteApprovalConfirmationEmailParams extends BaseEmailParams {
    firstName: string;
    lastName: string;
    email: string;
    ccEmails: string[];
    documentNumber: string;
    startDate: string;
    poNumber: string;
    totalSellPrice?: string;
}

interface SendQuoteUpdatedEmailParams extends BaseEmailParams {
    email: string;
    ccEmails: string[];
    documentNumber: string;
    customerName: string;
    startDate: string;
    endDate: string;
    url: string;
    resellerPrice: string;
}

interface SendNewQuoteAvailableEmailParams extends BaseEmailParams {
    email: string;
    ccEmails: string[];
    documentNumber: string;
    customerName: string;
    startDate: string;
    endDate: string;
    url: string;
    salesMessage?: string;
    resellerPrice: string;
}

interface SendRequestQuoteConfirmationEmailParams extends BaseEmailParams {
    email: string;
    ccEmails: string[];
    firstName: string;
    lastName: string;
    customerName: string;
    groupId?: string;
    contractNo?: string;
    assets: {
        serial: string;
        productSku: string;
        serviceGroup: string;
        endDate: string;
    }[];
    message?: string;
}

@Injectable()
export class EmailService {
    private readonly logger = new Logger('QuoteEmailService');

    constructor(
        private configService: ConfigService,
        @Inject(CommonEmailService)
        private emailService: CommonEmailService,
        private organizationService: OrganizationsService,
    ) {}

    public async sendQuoteApprovalConfirmationEmail({
        email,
        ccEmails,
        documentNumber,
        startDate,
        poNumber,
        totalSellPrice,
        firstName,
        lastName,
        organizationId,
        locale,
    }: SendQuoteApprovalConfirmationEmailParams) {
        try {
            this.logger.log(`Sending quote approval confirmation email to ${email}`);

            const organization = await this.organizationService.getOrganizationById(organizationId);
            if (!organization) {
                this.logger.log('No organization found');
                return;
            }

            if (!organization.emailable) {
                this.logger.log('Organization is not emailable');
                return;
            }

            await this.emailService.send({
                toEmails: [email],
                ccEmails,
                locale,
                templateName: EmailTemplateEnum.QUOTE_APPROVAL,
                tags: ['Quote'],
                params: {
                    poNumber,
                    contact: {
                        firstName,
                        lastName,
                    },
                    document: {
                        startDate,
                        totalSellPrice,
                        number: documentNumber,
                    },
                },
            });
        } catch (error) {
            this.logger.error({ err: error, email }, `Error sending quote approval confirmation email to ${email}`);
        }
    }

    public async sendQuoteUpdatedEmail({
        email,
        ccEmails,
        documentNumber,
        startDate,
        endDate,
        customerName,
        url,
        organizationId,
        locale,
        resellerPrice,
    }: SendQuoteUpdatedEmailParams) {
        try {
            this.logger.log(`Sending quote updated email to ${email}`);

            const organization = await this.organizationService.getOrganizationById(organizationId);
            if (!organization) {
                this.logger.log('No organization found');
                return;
            }

            if (!organization.emailable) {
                this.logger.log('Organization is not emailable');
                return;
            }

            await this.emailService.send({
                toEmails: [email],
                ccEmails,
                locale,
                templateName: EmailTemplateEnum.QUOTE_UPDATED,
                tags: ['Quote'],
                params: {
                    url,
                    document: {
                        customerName,
                        startDate,
                        endDate,
                        resellerPrice,
                        number: documentNumber,
                    },
                },
            });
        } catch (error) {
            this.logger.error({ err: error, email }, `Error sending quote updated confirmation email to ${email}`);
        }
    }

    public async sendNewQuoteAvailableEmail({
        email,
        ccEmails,
        documentNumber,
        startDate,
        endDate,
        customerName,
        url,
        organizationId,
        locale,
        salesMessage,
        resellerPrice,
    }: SendNewQuoteAvailableEmailParams) {
        try {
            this.logger.log(`Sending new quote available email to ${email}`);

            const organization = await this.organizationService.getOrganizationById(organizationId);
            if (!organization) {
                this.logger.log('No organization found');
                return;
            }

            if (!organization.emailable) {
                this.logger.log('Organization is not emailable');
                return;
            }

            await this.emailService.send({
                toEmails: [email],
                ccEmails,
                locale,
                templateName: EmailTemplateEnum.QUOTE_NEW_AVAILABLE,
                tags: ['Quote'],
                params: {
                    url,
                    document: {
                        customerName,
                        startDate,
                        endDate,
                        resellerPrice,
                        number: documentNumber,
                    },
                    salesMessage,
                },
            });
        } catch (error) {
            this.logger.error({ err: error, email }, `Error sending new quote available email to ${email}`);
        }
    }

    public async sendRequestQuoteConfirmationEmail({
        email,
        ccEmails,
        customerName,
        groupId,
        contractNo,
        assets,
        firstName,
        lastName,
        message,
        organizationId,
        locale,
    }: SendRequestQuoteConfirmationEmailParams) {
        try {
            this.logger.log(`Sending request quote confirmation email to ${email}`);

            const organization = await this.organizationService.getOrganizationById(organizationId);
            if (!organization) {
                this.logger.log('No organization found');
                return;
            }

            if (!organization.emailable) {
                this.logger.log('Organization is not emailable');
                return;
            }

            await this.emailService.send({
                toEmails: [email],
                ccEmails,
                locale,
                templateName: EmailTemplateEnum.QUOTE_REQUEST_CONFIRMATION,
                tags: ['Quote'],
                params: {
                    customerName,
                    groupId: null,
                    message,
                    assets,
                    contact: {
                        firstName,
                        lastName,
                    },
                    document: {
                        contractNo: null,
                    },
                },
            });
        } catch (error) {
            this.logger.error({ err: error, email }, `Error sending new quote available email to ${email}`);
        }
    }
}
