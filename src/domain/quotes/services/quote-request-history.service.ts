import { InjectRepository } from '@mikro-orm/nestjs';
import { Injectable, Logger } from '@nestjs/common';

import { UserService } from '../../users/services';
import { Quote, QuoteRequestHistory } from '../entities';
import { QuoteHistoryRecordCreateModel } from '../models';
import { QuoteRequestHistoryModel } from '../models/quote-request-history.model';
import { QuoteRequestHistoryPresenter } from '../presenters/quote-request-history.presenter';
import { QuoteRepository, QuoteRequestHistoryRepository } from '../repositories';

@Injectable()
export class QuoteRequestHistoryService {
    private readonly logger = new Logger(QuoteRequestHistoryService.name);

    constructor(
        @InjectRepository(Quote)
        private readonly quoteRepository: QuoteRepository,
        @InjectRepository(QuoteRequestHistory)
        private readonly quoteRequestHistoryRepository: QuoteRequestHistoryRepository,
        private readonly userService: UserService,
        private presenter: QuoteRequestHistoryPresenter,
    ) {}

    async createHistoryRecord({
        action,
        request,
        quoteId,
        triggeredBy,
    }: QuoteHistoryRecordCreateModel): Promise<QuoteRequestHistory> {
        const record = new QuoteRequestHistory();

        if (quoteId) {
            const quote = await this.quoteRepository.findOneOrFail({
                id: quoteId,
            });

            record.quote = quote;
        }

        record.action = action;
        record.request = request;
        record.triggeredBy = triggeredBy;

        await this.quoteRequestHistoryRepository.upsert(record);

        return record;
    }

    async getRequestHistory(quoteId: string) {
        const requestHistory = await this.quoteRequestHistoryRepository.findRequestsByQuoteId(quoteId);

        if (!requestHistory) {
            throw new Error('Request history not found');
        }

        const items: QuoteRequestHistoryModel[] = await Promise.all(
            requestHistory.map(async (entity) => {
                const user = await this.userService.getUserById(entity.triggeredBy.userId);

                return this.presenter.toModel(entity, user);
            }),
        );

        const model = new QuoteRequestHistoryModel();
        model.setData(items);

        return model;
    }

    async getLatestRequest(quoteId: string): Promise<QuoteRequestHistoryModel | null> {
        const latestRequest = await this.quoteRequestHistoryRepository.findLatestRequestByQuoteId(quoteId);

        if (!latestRequest) {
            throw null;
        }

        const user = await this.userService.getUserById(latestRequest.triggeredBy.userId);
        return this.presenter.toModel(latestRequest, user);
    }
}
