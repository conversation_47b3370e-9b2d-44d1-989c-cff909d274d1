import { Inject, Injectable, Logger } from '@nestjs/common';

import { NoAssetsFoundForExportException } from '../../assets/exceptions/no-assets-found-for-export.exception';
import { AssetListModel } from '../../common/models/asset-list.model';
import {
    PaginatedResponseModel,
    PaginationMetaModel,
    PaginationParamsModel,
} from '../../common/pagination/pagination.model';
import { ExportFormat, ExportService } from '../../common/services/export.service';
import { AssetType } from '../../common/types';
import { ceilTwoDecimals } from '../../files/services';
import { QuoteNotFoundByIdException } from '../exceptions/quote-not-found-by-id.exception';
import { QuoteItemFilterModel } from '../models';
import { QuoteItemPresenter } from '../presenters';
import { QuoteItemViewDrizzleRepository } from '../repositories';
import { QuoteDrizzleRepository } from '../repositories/quote.drizzle.repository';

@Injectable()
export class QuoteItemsService {
    private readonly logger = new Logger(QuoteItemsService.name);

    constructor(
        private readonly presenter: QuoteItemPresenter,
        private readonly exportService: ExportService,

        @Inject(QuoteDrizzleRepository)
        private readonly quoteDrizzleRepository: QuoteDrizzleRepository,

        @Inject(QuoteItemViewDrizzleRepository)
        private readonly quoteItemViewDrizzleRepository: QuoteItemViewDrizzleRepository,
    ) {}

    async getAssetsByQuoteId(
        resellerIds: string[],
        endUserIds: string[] | null,
        quoteId: string,
        paginationParams?: PaginationParamsModel,
        filter?: QuoteItemFilterModel,
    ): Promise<AssetListModel> {
        const isQuoteExists = await this.quoteDrizzleRepository.checkIfQuoteExistsByQuoteId(
            resellerIds,
            endUserIds,
            quoteId,
        );

        if (!isQuoteExists) {
            throw new QuoteNotFoundByIdException(quoteId);
        }

        const result = await this.quoteItemViewDrizzleRepository.findAssetsByQuoteId(
            resellerIds,
            endUserIds,
            quoteId,
            paginationParams,
            filter,
        );

        let assets: AssetType[];
        let meta: PaginationMetaModel | undefined;

        if (paginationParams && result && 'data' in result && 'meta' in result) {
            assets = (result as PaginatedResponseModel<AssetType>).data;
            meta = (result as PaginatedResponseModel<AssetType>).meta;
        } else if (!paginationParams && Array.isArray(result)) {
            assets = result as AssetType[];
            meta = undefined;
        } else if (!paginationParams && result && 'data' in result && 'meta' in result) {
            assets = (result as PaginatedResponseModel<AssetType>).data;
            meta = (result as PaginatedResponseModel<AssetType>).meta;
        } else {
            this.logger.error('Unexpected result type from findAssetsByQuoteId', {
                quoteId,
                paginationParams,
                filter,
                resultType: typeof result,
            });
            assets = [];
            meta = undefined;
        }

        const items = assets.map((entity) => this.presenter.toAssetModel(entity));

        const model = new AssetListModel();
        model.setData(items);
        if (meta) {
            model.setMeta(meta);
        }

        return model;
    }

    async exportAssetsByQuoteId(
        resellerIds: string[],
        endUserIds: string[] | null,
        quoteId: string,
        format: ExportFormat,
        filter?: QuoteItemFilterModel,
    ): Promise<{ data: Buffer; filename: string }> {
        const limit = 1000;
        const exportItems = [];
        let cursor: string | undefined = undefined;

        while (true) {
            const assetList = await this.getAssetsByQuoteId(
                resellerIds,
                endUserIds,
                quoteId,
                {
                    limit,
                    cursor,
                },
                filter,
            );

            const entries = assetList.getData();

            exportItems.push(...entries);

            const meta = assetList.getMeta();
            if (!meta.hasNextPage) {
                break;
            }

            cursor = meta.nextCursor;
        }

        if (!exportItems.length) {
            throw new NoAssetsFoundForExportException();
        }

        const exportData = exportItems.map((asset) => ({
            Name: asset.name || '',
            SerialNumber: asset.serialNumber || '',
            CoverageStatus: asset.coverageStatus || 'UNKNOWN',
            ServiceGroupLabel: asset.serviceGroupLabel || '',
            ServiceGroupSku: asset.serviceGroupSku || '',
            ProductSku: asset.productSku || '',
            Quantity: asset.quantity || 1,
            ResellerPriceFinalSum: asset.resellerPriceFinalSum ? ceilTwoDecimals(asset.resellerPriceFinalSum) : 0,
            EndCustomerPriceFinalSum: asset.endCustomerPriceFinalSum
                ? ceilTwoDecimals(asset.endCustomerPriceFinalSum)
                : 0,
            Currency: asset.currency || 'CHF',
        }));

        return this.exportService.exportData(exportData, format, `assets-quote-${quoteId}-${new Date().toISOString()}`);
    }
}
