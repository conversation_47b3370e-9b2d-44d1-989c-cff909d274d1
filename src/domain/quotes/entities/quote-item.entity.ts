import { Entity, EntityRepositoryType, Index, ManyToOne, PrimaryKey, Property } from '@mikro-orm/core';

import { ContractItem } from '../../contracts/entities/contract-item.entity';
import { QuoteItemDataField } from '../quote-item.schema';
import { QuoteItemRepository } from '../repositories';
import { Quote } from './quote.entity';

@Entity({
    tableName: 'quote_items',
    repository: () => QuoteItemRepository,
})
@Index({ name: 'idx_quote_items_v_p_sku', properties: ['vendorId', 'productSku'] })
@Index({ name: 'idx_quote_items_v_sl_sku', properties: ['vendorId', 'serviceLevelSku'] })
@Index({ name: 'idx_quote_items_sno', properties: 'serialNo' })
@Index({ name: 'idx_quote_items_p_sku', properties: 'productSku' })
@Index({ name: 'idx_quote_items_ecp', properties: 'endCustomerPrice' })
@Index({ name: 'idx_quote_items_q_id', properties: 'quoteId' })
@Index({ name: 'idx_quote_items_sci', properties: 'sourceContractItemId' })
@Index({ name: 'idx_quote_items_rci', properties: 'resultingContractItemId' })
export class QuoteItem {
    [EntityRepositoryType]?: QuoteItemRepository;

    @PrimaryKey({ type: 'text' })
    id: string;

    @Property({ type: 'text', nullable: true })
    sourceContractItemId?: string;

    @Property({ type: 'text', nullable: true })
    resultingContractItemId?: string;

    @Property({ type: 'integer', nullable: true })
    itemNo?: number;

    @Property({ type: 'text', nullable: true })
    quoteId?: string;

    @Property({ type: 'text', nullable: true })
    vendorId?: string;

    @Property({ type: 'text', nullable: true })
    serialNo?: string;

    @Property({ type: 'text', nullable: true })
    productSku?: string;

    @Property({ type: 'text', nullable: true })
    serviceLevelSku?: string;

    @Property({ type: 'text', nullable: true })
    serviceGroupSku?: string;

    @Property({ type: 'text', nullable: true })
    serviceGroupLabel?: string;

    @Property({ type: 'numeric', precision: 20, scale: 4, nullable: true, default: null })
    resellerPrice?: number;

    @Property({ type: 'numeric', precision: 20, scale: 4, nullable: true, default: null })
    distributorPrice?: number;

    @Property({ type: 'numeric', precision: 20, scale: 4, nullable: true, default: null })
    endCustomerPrice?: number;

    @Property({ type: 'numeric', nullable: true, default: null })
    quantity?: number;

    @Property({ type: 'object', nullable: true })
    data?: QuoteItemDataField;

    @ManyToOne(() => Quote, { nullable: true, persist: false })
    quote?: Quote;

    @ManyToOne(() => ContractItem, { nullable: true, persist: false })
    sourceContractItem?: ContractItem;

    @ManyToOne(() => ContractItem, { nullable: true, persist: false })
    resultingContractItem?: ContractItem;
}
