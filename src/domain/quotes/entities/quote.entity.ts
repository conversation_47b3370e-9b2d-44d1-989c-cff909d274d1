import { Entity, EntityRepositoryType, Enum, Index, ManyToOne, OneToMany, PrimaryKey, Property } from '@mikro-orm/core';

import { CurrencyEnum } from '../../common/enums/currency.enum';
import { EntityEntity } from '../../entities/entities/entity.entity';
import { QuoteUiStatusEnum } from '../enums';
import { QuoteDataField } from '../quote.schema';
import { QuoteRepository } from '../repositories';
import { QuoteItem } from './quote-item.entity';

@Entity({
    tableName: 'quotes',
    repository: () => QuoteRepository,
})
@Index({ name: 'idx_quotes_r_id', properties: ['resellerId'] })
export class Quote {
    [EntityRepositoryType]?: QuoteRepository;

    @PrimaryKey({ type: 'text' })
    id: string;

    @Property({ type: 'text', nullable: true })
    quoteNo?: string;

    @Property({ type: 'text', nullable: true })
    quoteType?: string;

    @Property({ type: 'text', nullable: true })
    status?: string;

    @Enum({ items: () => QuoteUiStatusEnum, nativeEnumName: 'quote_ui_status', nullable: true })
    uiStatus?: QuoteUiStatusEnum;

    @Property({ type: 'text', nullable: true })
    groupId?: string; // Also known as AmpId

    @Property({ type: 'text', nullable: true })
    startDate?: Date;

    @Property({ type: 'text', nullable: true })
    endDate?: Date;

    @Property({ type: 'text', nullable: true })
    expiryDate?: Date;

    @Property({ type: 'text', nullable: true })
    currency?: CurrencyEnum;

    @Property({ type: 'numeric', precision: 20, scale: 4, nullable: true })
    resellerTotalPrice?: number;

    @Property({ type: 'numeric', precision: 20, scale: 4, nullable: true })
    distributorTotalPrice?: number;

    @Property({ type: 'numeric', precision: 20, scale: 4, nullable: true })
    endCustomerTotalPrice?: number;

    @Index()
    @Property({ type: 'text', nullable: true })
    vendorId?: string;

    @Index()
    @Property({ type: 'text', nullable: true })
    distributorId?: string;

    @Index()
    @Property({ type: 'text', nullable: true })
    resellerId?: string;

    @Index()
    @Property({ type: 'text', nullable: true })
    endUserId?: string;

    @Property({ type: 'object', nullable: true })
    data?: QuoteDataField;

    @OneToMany(() => QuoteItem, (quoteItem) => quoteItem.quote, { nullable: true, persist: false })
    quoteItems?: QuoteItem[];

    @ManyToOne(() => EntityEntity, { nullable: true, persist: false })
    distributor?: EntityEntity;

    @ManyToOne(() => EntityEntity, { nullable: true, persist: false })
    reseller?: EntityEntity;

    @ManyToOne(() => EntityEntity, { nullable: true, persist: false })
    endUser?: EntityEntity;

    @ManyToOne(() => EntityEntity, { nullable: true, persist: false })
    vendor?: EntityEntity;

    updatedAt: Date;
}
