import { sql } from 'drizzle-orm';
import { check, foreignKey, jsonb, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { quotes } from './quote.schema';

export const quoteRequestHistories = pgTable(
    'quote_request_histories',
    {
        id: uuid().primaryKey().notNull(),
        action: text().notNull(),
        request: jsonb().notNull(),
        quoteId: text('quote_id'),
        triggeredBy: jsonb('triggered_by').notNull(),
        createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }).notNull(),
        updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }).notNull(),
    },
    (table) => [
        foreignKey({
            columns: [table.quoteId],
            foreignColumns: [quotes.id],
            name: 'quote_request_histories_quote_id_foreign',
        }).onUpdate('cascade'),
        check(
            'quote_request_histories_action_check',
            sql`action = ANY (ARRAY['APPROVAL'::text, 'DECLINE'::text, 'REQUEST_CHANGE'::text, 'REQUEST_QUOTE'::text])`,
        ),
    ],
);
