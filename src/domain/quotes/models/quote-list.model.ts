import { PaginationMetaModel } from '../../common/pagination/pagination.model';
import { QuoteListSchema } from '../schemas/quote-list.schema';
import { QuoteListItemModel } from './quote-list-item.model';

export class QuoteListModel {
    zodSchema = QuoteListSchema;

    private data: QuoteListItemModel[] = [];
    private meta: PaginationMetaModel = new PaginationMetaModel();

    public setData(data: QuoteListItemModel[], meta: PaginationMetaModel) {
        this.data = data;
        this.meta = meta;
    }

    public getData() {
        return this.zodSchema.parse({
            data: this.data,
            meta: this.meta,
        });
    }
}
