import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { addMetaProperties } from '../../../helpers/zod.helper';

const maxFileSize = 20 * 1024 * 1024; // 20MB
const allowedFileTypes = {
    'application/pdf': ['.pdf'],
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
    'application/vnd.ms-excel': ['.xls'],
    'text/csv': ['.csv'],
    'application/msword': ['.doc'],
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
};

const BaseRequestQuoteSchema = z
    .object({
        customerId: z.string().nullable().optional().describe('FIELDS.CUSTOMER_ID'),
        customerName: z.string().nullable().optional().describe('FIELDS.CUSTOMER_NAME'),
        message: addMetaProperties(z.string().nullable().optional().describe('FIELDS.MESSAGE'), {
            'ui:widget': 'text-editor',
        }),
        files: addMetaProperties(
            z
                .array(
                    extendApi(
                        z
                            .custom<Express.Multer.File>(
                                (file) => {
                                    // Check if file exists and is an object
                                    if (!file || typeof file !== 'object') return false;

                                    // Check if file has mimetype property
                                    const fileObj = file as any;
                                    return fileObj.mimetype && Object.keys(allowedFileTypes).includes(fileObj.mimetype);
                                },
                                {
                                    path: ['attachPo'],
                                    message: 'FIELDS.INVALID_FILE_TYPE',
                                },
                            )
                            .optional()
                            .describe('FIELDS.FILE'),
                        {
                            type: 'string',
                            format: 'file',
                        },
                    ),
                )
                .optional()
                .nullable()
                .describe('FIELDS.FILES'),
            {
                'ui:options': {
                    multipleFiles: true,
                    acceptedFormats: allowedFileTypes,
                    maxSize: maxFileSize,
                },
            },
        ),
        assets: z
            .array(
                z
                    .object({
                        serviceGroupVendor: z.string().nullable().optional().describe('FIELDS.SERVICE_GROUP_VENDOR'),
                        serviceGroupSku: z.string().nullable().optional().describe('FIELDS.SERVICE_GROUP_SKU'),
                        serialNumber: z.string().describe('FIELDS.SERIAL_NUMBER'),
                        productSku: z.string().nullable().optional().describe('FIELDS.PRODUCT_SKU'),
                        endDate: z
                            .string()
                            .datetime({ offset: true })
                            .nullable()
                            .optional()
                            .describe('FIELDS.PLANNED_END_DATE'),
                    })
                    .refine(
                        (data) => {
                            const hasVendor = !!data.serviceGroupVendor;
                            const hasSku = !!data.serviceGroupSku;
                            // Either both fields are provided or both are not provided
                            return (hasVendor && hasSku) || (!hasVendor && !hasSku);
                        },
                        {
                            message: 'serviceGroupVendor and serviceGroupSku must be provided together',
                            path: ['serviceGroupVendor', 'serviceGroupSku'],
                        },
                    ),
            )
            .refine((data) => new Set(data.map((asset) => asset.serialNumber)).size === data.length, {
                message: 'Serial numbers must be unique',
            })
            .optional()
            .default([])
            .describe('FIELDS.ASSETS'),
        address: z
            .object({
                address: z.string().describe('FIELDS.ADDRESS'),
                zip: z.string().describe('FIELDS.ZIP'),
                location: z.string().describe('FIELDS.LOCATION'),
            })
            .nullable()
            .optional()
            .describe('FIELDS.ADDRESS'),
        contact: z
            .object({
                firstName: z.string().describe('FIELDS.FIRST_NAME'),
                lastName: z.string().describe('FIELDS.LAST_NAME'),
                email: z.string().email().describe('FIELDS.EMAIL'),
                phone: z.string().describe('FIELDS.PHONE'),
            })
            .nullable()
            .optional()
            .describe('FIELDS.CONTACT'),
    })
    .describe('REQUEST_QUOTE_FORM.REQUEST_QUOTE');

export const RequestQuoteSchema = BaseRequestQuoteSchema.refine(
    (data) => {
        // Ensure at least one of customerId or customerName is not null/undefined
        return !!data.customerId || !!data.customerName;
    },
    {
        message: 'Either Customer ID or Customer Name must be provided',
        path: ['customerId', 'customerName'],
    },
).describe('REQUEST_QUOTE_FORM.REQUEST_QUOTE');
export class RequestQuoteModel extends createZodDto(RequestQuoteSchema) {}

export class RequestQuoteFilesModel extends createZodDto(
    z
        .object({
            files: BaseRequestQuoteSchema.shape.files,
        })
        .optional()
        .nullable(),
) {}
