import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { addMetaProperties } from '../../../helpers/zod.helper';

const maxFileSize = 20 * 1024 * 1024; // 20MB
const allowedFileTypes = {
    'application/pdf': ['.pdf'],
};

export const ApproveQuoteSchema = extendApi(
    z.object({
        poNumber: z.string().describe('FIELDS.PO_NUMBER'),
        attachPo: addMetaProperties(
            extendApi(
                z
                    .custom<Express.Multer.File>(
                        (file) => {
                            // Check if file exists and is an object
                            if (!file || typeof file !== 'object') return false;

                            // Check if file has mimetype property
                            const fileObj = file as any;
                            return fileObj.mimetype && Object.keys(allowedFileTypes).includes(fileObj.mimetype);
                        },
                        {
                            path: ['attachPo'],
                            message: 'Invalid file type',
                        },
                    )
                    .optional()
                    .describe('FIELDS.ATTACH_PO'),
                {
                    type: 'string',
                    format: 'file',
                },
            ),
            {
                'ui:options': {
                    multipleFiles: false,
                    acceptedFormats: allowedFileTypes,
                    maxSize: maxFileSize,
                },
            },
        ),
        comment: addMetaProperties(z.string().optional().describe('FIELDS.COMMENT'), {
            'ui:widget': 'text-editor',
        }),
    }),
    {
        description: 'Approve Quote',
    },
);

export class ApproveQuoteModel extends createZodDto(ApproveQuoteSchema) {}

export class ApproveQuoteFileModel extends createZodDto(ApproveQuoteSchema.shape.attachPo) {}
