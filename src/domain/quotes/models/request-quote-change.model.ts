import { createZodDto } from '@anatine/zod-nestjs';
import { z } from 'zod';

import { addMetaProperties } from '../../../helpers/zod.helper';
import { QuoteCancellationReasonEnum } from '../enums/quote-cancellation-reason.enum';

const quoteCancellationReasonKeys = Object.values(QuoteCancellationReasonEnum);

export const RequestQuoteChangeSchema = z
    .object({
        message: addMetaProperties(z.string().nullable().optional(), {
            'ui:widget': 'text-editor',
            title: 'MESSAGE',
        }),
        cancellationReason: addMetaProperties(z.string().nullable().optional(), {
            title: 'FIELDS.CANCELLATION_REASON',
            description: 'QUOTES.CANCELLATION_REASONS',
            type: 'string',
            enum: quoteCancellationReasonKeys,
            hideAllOption: true,
        }),

        assets: z
            .array(
                z
                    .object({
                        productSku: z.string().describe('FIELDS.PRODUCT_SKU'),
                        serialNumber: z.string().describe('FIELDS.SERIAL_NUMBER').optional().nullable(),

                        oldServiceGroupVendor: z
                            .string()
                            .nullable()
                            .optional()
                            .describe('FIEDLS.OLD_SERVICE_GROUP_VENDOR'),
                        oldServiceGroupSku: z.string().nullable().optional().describe('FIEDLS.OLD_SERVICE_GROUP_SKU'),

                        newServiceGroupVendor: z
                            .string()
                            .nullable()
                            .optional()
                            .describe('FIEDLS.NEW_SERVICE_GROUP_VENDOR'),
                        newServiceGroupSku: z.string().nullable().optional().describe('FIEDLS.NEW_SERVICE_GROUP_SKU'),
                    })
                    .refine(
                        (data) => {
                            const hasOldVendor = !!data.oldServiceGroupVendor;
                            const hasOldSku = !!data.oldServiceGroupSku;
                            // Either both fields are provided or both are not provided
                            return (hasOldVendor && hasOldSku) || (!hasOldVendor && !hasOldSku);
                        },
                        {
                            message: 'oldServiceGroupVendor and oldServiceGroupSku must be provided together',
                            path: ['oldServiceGroupVendor', 'oldServiceGroupSku'],
                        },
                    )
                    .refine(
                        (data) => {
                            const hasNewVendor = !!data.newServiceGroupVendor;
                            const hasNewSku = !!data.newServiceGroupSku;
                            // Either both fields are provided or both are not provided
                            return (hasNewVendor && hasNewSku) || (!hasNewVendor && !hasNewSku);
                        },
                        {
                            message: 'newServiceGroupVendor and newServiceGroupSku must be provided together',
                            path: ['newServiceGroupVendor', 'newServiceGroupSku'],
                        },
                    ),
            )
            .describe('Assets'),
    })
    .describe('Request Quote Change');

export class RequestQuoteChangeModel extends createZodDto(RequestQuoteChangeSchema) {}
