import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { addMetaProperties } from '../../../helpers/zod.helper';
import { QuoteCancellationReasonEnum } from '../enums/quote-cancellation-reason.enum';

const quoteCancellationReasonKeys = Object.values(QuoteCancellationReasonEnum);

export const DeclineQuoteSchema = extendApi(
    z.object({
        declineReason: addMetaProperties(z.string().optional(), {
            title: 'DECLINE_REASON',
            description: 'QUOTES.CANCELLATION_REASONS',
            type: 'string',
            enum: quoteCancellationReasonKeys,
            hideAllOption: true,
        }),
    }),
    {
        description: 'Decline Quote',
    },
);

export class DeclineQuoteModel extends createZodDto(DeclineQuoteSchema) {}
