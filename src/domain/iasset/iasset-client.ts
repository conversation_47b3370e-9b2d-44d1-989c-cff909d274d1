import { Injectable, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import * as ContentDisposition from 'content-disposition';
import { DateTime } from 'luxon';

import { ContractNotFoundException } from '../contracts/exceptions/contract-not-found.exception';
import { QuoteNotFoundException } from '../quotes/exceptions/quote-not-found.exception';
import { IAssetFile } from './iasset-file';
import { Rfc2047Decoder } from './rfc2047-decoder.service';

const TOKEN_LIFETIME_SECONDS = 5 * 60; // 5 minutes

@Injectable()
export class IAssetClient {
    private domain: string | null = null;
    private authorizationTime: DateTime | null = null;
    private token: string | null = null;
    private client: AxiosInstance;

    constructor(
        private readonly configService: ConfigService,
        private readonly rfc2047Decoder: Rfc2047Decoder,
    ) {
        this.client = axios.create({
            validateStatus: function (status) {
                return status >= 200 && status < 300; // Default
            },
        });
        this.domain = this.configService.get<string>('IASSET_DOMAIN');
    }

    private isAuthorized(): boolean {
        if (this.token === null || this.authorizationTime === null) {
            return false;
        }

        const now = DateTime.now();
        const diffInSeconds = now.diff(this.authorizationTime, 'seconds').seconds;
        return diffInSeconds < TOKEN_LIFETIME_SECONDS;
    }

    private async makeApiRequest(
        method: string,
        path: string,
        options: any = {},
        prefix = 'v3',
    ): Promise<AxiosResponse> {
        if (!this.isAuthorized()) {
            await this.authorize();
        }

        options.headers = {
            ...options.headers,
            Authorization: `Bearer ${this.token}`,
        };

        try {
            const response = await this.client.request({
                method,
                url: `https://${this.domain}.iasset.com/${prefix}/${path}`,
                ...options,
            });
            return response;
        } catch (error) {
            console.error('Error:', error.message);
            if (axios.isAxiosError(error) && error.response) {
                if (error.response.status === 404) {
                    console.error(`Resource not found at ${path}`);
                    throw new NotFoundException('PDF not found');
                }
                console.error(`API request to ${path} failed with status ${error.response.status}`);
                throw new InternalServerErrorException('API request failed');
            }
            console.error('API request failed');
            throw new InternalServerErrorException('API request failed');
        }
    }

    private async authorize(): Promise<void> {
        if (this.isAuthorized()) {
            return;
        }

        try {
            const response = await this.client.post(`https://${this.domain}.iasset.com/v2/Core/Tokens`, {
                UserName: this.configService.get<string>('IASSET_USERNAME'),
                Password: this.configService.get<string>('IASSET_PASSWORD'),
                GrantType: 'password',
            });

            const json = response.data;
            if (!json.accessToken) {
                throw new Error('Unable to authorize iAsset client: accessToken not found');
            }

            this.token = json.accessToken;
            this.authorizationTime = DateTime.now();
        } catch (error) {
            console.error('Error during authorization:', error.message);
            if (axios.isAxiosError(error) && error.response) {
                console.error(`Unable to authorize iAsset client. Status: ${error.response.status}`);
                throw new InternalServerErrorException('API request failed');
            }
            console.error('Unable to authorize iAsset client');
            throw new InternalServerErrorException('API request failed');
        }
    }

    public async findQuoteIdByNumber(quoteNumber: string): Promise<number> {
        const response = await this.makeApiRequest('GET', 'data/quotes', {
            params: {
                $filter: `QuoteNo eq '${quoteNumber}'`,
            },
        });

        const data = response.data;
        if (!Array.isArray(data.value) || !data.value[0]?.QuoteId) {
            throw new QuoteNotFoundException();
        }

        if (data.value.length > 1) {
            console.warn(`Multiple quotes found while searching by quote "${quoteNumber}"`);
        }

        return data.value[0].QuoteId;
    }

    public async getQuoteOwnerEntityByQuoteId(quoteId: number | string): Promise<string> {
        const response = await this.makeApiRequest('GET', `data/quotes/${quoteId}/`, {
            params: {
                $expand: 'OwnerEntity',
            },
        });

        const data = response.data;
        if (!data.OwnerEntity?.AccountReference) {
            console.error('Unable to get quote owner entity');
            throw new InternalServerErrorException('API request failed');
        }

        const accountReference = data.OwnerEntity.AccountReference;

        if (!accountReference) {
            console.error('Quote owner entity account reference not found.');
            throw new InternalServerErrorException('API request failed');
        }
        return accountReference;
    }

    public async downloadQuotePdf(quoteNumber: string, accountReference: string): Promise<IAssetFile> {
        const response = await this.makeApiRequest('GET', 'core/quotes/QuoteOutput', {
            params: {
                quoteNo: quoteNumber,
                ownerAccountReference: accountReference,
            },
            responseType: 'stream',
        });

        return await this.parsePdfContentAndRewindStreamByResponse(response, `Quote - ${quoteNumber}.pdf`);
    }

    public async findContractIdByNumber(contractNumber: string): Promise<number> {
        const response = await this.makeApiRequest('GET', 'data/contracts', {
            params: {
                $filter: `ContractNo eq '${contractNumber}'`,
            },
        });

        const data = response.data;
        if (!Array.isArray(data.value) || !data.value[0]?.ContractId) {
            throw new ContractNotFoundException();
        }

        if (data.value.length > 1) {
            console.warn(`Multiple contracts found while searching by contract "${contractNumber}"`);
        }

        return data.value[0].ContractId;
    }

    public async getContractOwnerEntityByContractId(contractId: number | string): Promise<string> {
        const response = await this.makeApiRequest('GET', `data/contracts/${contractId}/`, {
            params: {
                $expand: 'ContractEntities($expand=Entity)',
            },
        });

        const data = response.data;
        if (!data.ContractEntities?.[0]?.Entity) {
            console.error('Unable to get contract owner entity');
            throw new InternalServerErrorException('API request failed');
        }

        let accountReference = '';
        for (const entity of data.ContractEntities) {
            if (entity.Entity.EntityId === entity.Entity.OwnerEntityId) {
                accountReference = entity.Entity.AccountReference;
                break;
            }
        }
        if (!accountReference) {
            console.error('Contract owner entity account reference not found.');
            throw new InternalServerErrorException('API request failed');
        }
        return accountReference;
    }

    public async downloadContractPdf(contractNumber: string, accountReference: string): Promise<IAssetFile> {
        const response = await this.makeApiRequest('GET', 'core/contracts/ContractDoc', {
            params: {
                contractNo: contractNumber,
                ownerAccountReference: accountReference,
            },
            responseType: 'stream',
        });

        return await this.parsePdfContentAndRewindStreamByResponse(response, `Contract - ${contractNumber}.pdf`);
    }

    public async downloadMultipleContractsPdf(
        contractNumbers: string[],
        accountReference: string,
        contractPrintName?: string,
        languageCode?: string,
    ): Promise<IAssetFile> {
        const params: any = {
            contractNos: contractNumbers.join(','),
            ownerAccountReference: accountReference,
        };

        if (contractPrintName) {
            params.contractPrintName = contractPrintName;
        }

        if (languageCode) {
            params.languageCode = languageCode;
        }

        const response = await this.makeApiRequest(
            'GET',
            'core/contracts/ContractDoc',
            {
                params,
                responseType: 'stream',
            },
            'v4',
        );

        const defaultFilename = contractPrintName
            ? `${contractPrintName}.pdf`
            : `Contracts - ${contractNumbers.join(', ')}.pdf`;

        return await this.parsePdfContentAndRewindStreamByResponse(response, defaultFilename);
    }

    private async parsePdfContentAndRewindStreamByResponse(
        response: AxiosResponse,
        defaultFilename: string,
    ): Promise<IAssetFile> {
        const contentDispositionHeader = response.headers['content-disposition'];
        if (!contentDispositionHeader) {
            throw new InternalServerErrorException('Failed to download PDF: Content-Disposition header missing');
        }

        const parsedContentDisposition = ContentDisposition.parse(contentDispositionHeader);
        let filename = parsedContentDisposition.parameters?.filename || defaultFilename;

        if (this.rfc2047Decoder.isEncodedString(filename)) {
            try {
                filename = this.rfc2047Decoder.decodeString(filename);
            } catch (error) {
                console.warn('(Non-critical) Failed to decode RFC-2047 encoded Content-Disposition filename');
                filename = defaultFilename;
            }
        }

        const stream = response.data;
        if (!stream.readableLength && stream.readableEnded) {
            throw new InternalServerErrorException('PDF is empty or stream has already been consumed.');
        }
        if (!stream.readable) {
            throw new InternalServerErrorException('PDF stream is not readable.');
        }

        return new IAssetFile(filename, stream);
    }
}
