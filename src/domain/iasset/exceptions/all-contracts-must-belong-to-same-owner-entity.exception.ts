import { BadRequestException } from '@nestjs/common';
import { I18nContext } from 'nestjs-i18n';

import { I18nPath } from '../../../../i18n/generated/i18n.generated';

export class AllContractsMustBelongToSameOwnerEntityException extends BadRequestException {
    constructor() {
        super(I18nContext.current().t<I18nPath>('errors.CONTRACT.ALL_CONTRACTS_MUST_BELONG_TO_SAME_OWNER_ENTITY'));
    }
}
