import { Injectable } from '@nestjs/common';

import { AllContractsMustBelongToSameOwnerEntityException } from './exceptions/all-contracts-must-belong-to-same-owner-entity.exception';
import { IAssetClient } from './iasset-client';
import { IAssetFile } from './iasset-file';

@Injectable()
export class IAssetService {
    constructor(private readonly client: IAssetClient) {}

    public async getDownloadQuotePdfFromIAsset(quoteNo: string): Promise<IAssetFile> {
        const quoteId = await this.client.findQuoteIdByNumber(quoteNo);
        const accountReference = await this.client.getQuoteOwnerEntityByQuoteId(quoteId);
        return await this.client.downloadQuotePdf(quoteNo, accountReference);
    }

    public async getDownloadContractPdfFromIAsset(contractNo: string): Promise<IAssetFile> {
        const contractId = await this.client.findContractIdByNumber(contractNo);
        const accountReference = await this.client.getContractOwnerEntityByContractId(contractId);
        return await this.client.downloadContractPdf(contractNo, accountReference);
    }

    public async getDownloadMultipleContractsPdfFromIAsset(
        contractNumbers: string[],
        contractPrintName?: string,
        languageCode?: string,
    ): Promise<IAssetFile> {
        const firstContractId = await this.client.findContractIdByNumber(contractNumbers[0]);
        const accountReference = await this.client.getContractOwnerEntityByContractId(firstContractId);

        for (const contractNumber of contractNumbers.slice(1)) {
            const contractId = await this.client.findContractIdByNumber(contractNumber);
            const contractAccountRef = await this.client.getContractOwnerEntityByContractId(contractId);

            if (contractAccountRef !== accountReference) {
                throw new AllContractsMustBelongToSameOwnerEntityException();
            }
        }

        return await this.client.downloadMultipleContractsPdf(
            contractNumbers,
            accountReference,
            contractPrintName,
            languageCode,
        );
    }
}
