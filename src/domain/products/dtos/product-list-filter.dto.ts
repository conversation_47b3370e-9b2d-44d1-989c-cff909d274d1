import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

export const ProductListFilterSchema = extendApi(
    z.object({
        vendor: z.string().optional(),
        showInDropdown: z.preprocess((val) => {
            if (val === 'true') return true;
            if (val === 'false') return false;
            return val;
        }, z.boolean().optional()),
        type: z.string().optional(),
    }),
    {
        title: 'ProductListFilter',
        description: 'Product list filter model',
    },
);

export class ProductListFilterDto extends createZodDto(ProductListFilterSchema) {}
