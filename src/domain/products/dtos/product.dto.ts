import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

const ProductSchema = extendApi(
    z.object({
        vendorId: z.string(),
        sku: z.string(),
        description: z.string(),
        showInDropdown: z.boolean(),
        weight: z.number(),
    }),
    {
        title: 'Product',
        description: 'Product model',
    },
);

export class ProductDto extends createZodDto(ProductSchema) {}
