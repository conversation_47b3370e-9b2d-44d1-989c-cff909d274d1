import { Inject, Injectable } from '@nestjs/common';
import { and, eq, inArray, sql } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

import * as schema from '../../../../drizzle/schema';
import { DRIZZLE_PROVIDER } from '../../../drizzle/drizzle.provider';
import { ProductListFilterDto } from '../dtos/product-list-filter.dto';
import { Product } from '../entities/product.entity';

@Injectable()
export class ProductDrizzleRepository {
    protected db: NodePgDatabase<typeof schema>;

    constructor(
        @Inject(DRIZZLE_PROVIDER)
        db: NodePgDatabase<typeof schema>,
    ) {
        this.db = db;
    }

    async findProducts(filter?: ProductListFilterDto): Promise<Product[]> {
        const conditions = this.buildWhereConditions(filter);

        const result = await this.db
            .selectDistinctOn([schema.products.description], {
                description: schema.products.description,
                productId: schema.products.productId,
                sku: schema.products.sku,
                vendorId: schema.products.vendorId,
            })
            .from(schema.products)
            .where(conditions?.length ? and(...conditions) : undefined)
            .orderBy(schema.products.description, sql`random()`);

        return result.map((row) => this.mapToModel(row));
    }

    async findBySkus(skus: string[]): Promise<Product[]> {
        const result = await this.db
            .select({
                vendor: schema.products.vendorId,
                sku: schema.products.sku,
                productId: schema.products.productId,
                description: schema.products.description,
                showInDropdown: schema.products.showInDropdown,
                weight: schema.products.weight,
            })
            .from(schema.products)
            .where(inArray(schema.products.sku, skus));

        return result.map((row) => this.mapToModel(row));
    }

    async findDescriptionsBySkus(skus: string[]): Promise<{ [x: string]: string }> {
        const result = await this.db
            .select({
                sku: schema.products.sku,
                description: schema.products.description,
            })
            .from(schema.products)
            .where(inArray(schema.products.sku, skus));

        return result
            .map((row) => ({
                [row.sku]: row.description,
            }))
            .reduce((acc, curr) => ({ ...acc, ...curr }), {});
    }

    async findIdByVendorAndSku(vendor: string, sku: string): Promise<string | null> {
        const result = await this.db
            .select({
                id: schema.products.productId,
            })
            .from(schema.products)
            .where(and(eq(schema.products.vendorId, vendor), eq(schema.products.sku, sku)))
            .limit(1);

        return result.length > 0 ? result[0].id : null;
    }

    protected buildWhereConditions(filters?: ProductListFilterDto) {
        const conditions = [];

        if (filters?.showInDropdown !== undefined) {
            conditions.push(eq(schema.products.showInDropdown, filters.showInDropdown));
        }

        if (filters?.vendor) {
            conditions.push(eq(schema.products.vendorId, filters.vendor));
        }

        if (filters?.type) {
            conditions.push(eq(sql`${schema.products.data}->>'ProductType'`, filters.type));
        }

        return conditions;
    }

    protected mapToModel(record: any): Product {
        return {
            vendorId: record.vendorId,
            sku: record.sku,
            productId: record.productId,
            description: record.description,
            showInDropdown: record.showInDropdown,
            weight: record.weight,
        };
    }
}
