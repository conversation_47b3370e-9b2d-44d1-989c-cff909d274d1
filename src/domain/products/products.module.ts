import { Module } from '@nestjs/common';

import { ProductsController } from './controllers/products.controller';
import { ProductDrizzleRepository } from './repositories/product.drizzle.repository';
import { ProductService } from './services/product.service';

@Module({
    controllers: [ProductsController],
    providers: [ProductService, ProductDrizzleRepository],
    exports: [ProductDrizzleRepository],
})
export class ProductsModule {}
