import { boolean, integer, jsonb, pgEnum, pgTable, primaryKey, text } from 'drizzle-orm/pg-core';

import { DataSourceEnum } from '../contracts/enums/data-source.enum';

const dataSource = pgEnum('data_source', DataSourceEnum);

export const products = pgTable(
    'products',
    {
        vendorId: text('vendor_id').notNull(),
        sku: text().notNull(),
        productId: text('product_id'),
        description: text(),
        data: jsonb(),
        showInDropdown: boolean('show_in_dropdown').default(false).notNull(),
        weight: integer().default(0).notNull(),

        source: dataSource('source').default(DataSourceEnum.iAsset).notNull(),
    },
    (table) => [primaryKey({ columns: [table.vendorId, table.sku], name: 'products_pkey' })],
);
