import { Entity, EntityRepositoryType, JsonType, PrimaryKey, Property, Unique } from '@mikro-orm/core';

import { ProductRepository } from '../repositories/product.repository';

@Entity({
    tableName: 'products',
    repository: () => ProductRepository,
})
@Unique({ properties: ['vendorId', 'sku'] })
export class Product {
    [EntityRepositoryType]?: ProductRepository;

    @PrimaryKey({ type: 'text' })
    vendorId!: string;

    @PrimaryKey({ type: 'text' })
    sku!: string;

    @Property({ type: 'text', nullable: true, fieldName: 'product_id' })
    productId!: string;

    @Property({ type: 'text', nullable: true })
    description?: string;

    @Property({ type: JsonType, nullable: true })
    data?: Record<string, unknown>;

    @Property({ default: false })
    showInDropdown: boolean;

    @Property({ type: 'number', default: 0 })
    weight: number;
}
