import { relations } from 'drizzle-orm';
import { index, jsonb, pgEnum, pgTable, text } from 'drizzle-orm/pg-core';

import { DataSourceEnum } from '../contracts/enums/data-source.enum';
import { entities } from '../entities/entity.schema';

const dataSource = pgEnum('data_source', DataSourceEnum);

export const addresses = pgTable(
    'addresses',
    {
        id: text().primaryKey().notNull(),
        entityId: text('entity_id').notNull(),
        data: jsonb(),

        source: dataSource('source').default(DataSourceEnum.iAsset).notNull(),
    },
    (table) => [index('idx_addresses_entity_id').on(table.entityId)],
);

export const addressesRelations = relations(addresses, ({ one }) => ({
    entity: one(entities, {
        fields: [addresses.entityId],
        references: [entities.id],
    }),
}));
