import { relations } from 'drizzle-orm';
import { boolean, foreignKey, pgEnum, pgTable, timestamp, uuid, varchar } from 'drizzle-orm/pg-core';

import { organizationEntities } from '../../../drizzle/schema';
import { roles } from '../users/role.schema';

export const organizationStatus = pgEnum('organization_status', ['active', 'inactive']);

export const organizations = pgTable(
    'organizations',
    {
        id: uuid().primaryKey().notNull(),
        name: varchar({ length: 255 }).notNull(),
        parentId: uuid('parent_id'),
        createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }).notNull(),
        updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }).notNull(),
        deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
        status: organizationStatus().default('inactive').notNull(),
        emailable: boolean().default(false).notNull(),
        advPerms: boolean('adv_perms').default(false).notNull(),
        locale: varchar({ length: 3 }).default('de').notNull(),
    },
    (table) => [
        foreignKey({
            columns: [table.parentId],
            foreignColumns: [table.id],
            name: 'organizations_parent_id_foreign',
        })
            .onUpdate('cascade')
            .onDelete('set null'),
    ],
);

export const organizationsRelations = relations(organizations, ({ one, many }) => ({
    organizationEntities: many(organizationEntities),
    organization: one(organizations, {
        fields: [organizations.parentId],
        references: [organizations.id],
        relationName: 'organizations_parentId_organizations_id',
    }),
    organizations: many(organizations, {
        relationName: 'organizations_parentId_organizations_id',
    }),
    roles: many(roles),
}));
