import { Injectable } from '@nestjs/common';

import { EntityEntity } from '../../entities/entities/entity.entity';
import { Organization } from '../entities';
import { OrganizationDto } from '../models';

@Injectable()
export class OrganizationPresenter {
    toFullModel(entity: Organization): OrganizationDto {
        const model = new OrganizationDto();
        model.id = entity.id;
        model.status = entity.status;
        model.name = entity.name;
        model.locale = entity.locale;
        model.emailable = entity.emailable;
        model.createdAt = entity.createdAt;
        model.updatedAt = entity.updatedAt;
        model.deletedAt = entity.deletedAt;
        model.entities = (entity as any)?.entities ?? [];
        model.advPerms = entity.advPerms;

        // Links
        model._links = {};

        model._links.self = {
            uri: `/admin/organizations/${entity.id}`,
        };
        model._links.users = {
            uri: `organizations/${entity.id}/users`,
        };

        return model;
    }

    toYourOrganizationModel(entity: Organization, entities: EntityEntity[]): OrganizationDto {
        const model = new OrganizationDto();
        model.id = entity.id;
        model.status = entity.status;
        model.name = entity.name;
        model.locale = entity.locale;
        model.emailable = entity.emailable;
        model.createdAt = entity.createdAt;
        model.updatedAt = entity.updatedAt;
        model.entities = entities;
        model.advPerms = entity.advPerms;

        // Links
        model._links = {};

        model._links.users = {
            uri: `organizations/${entity.id}/users`,
        };

        return model;
    }
}
