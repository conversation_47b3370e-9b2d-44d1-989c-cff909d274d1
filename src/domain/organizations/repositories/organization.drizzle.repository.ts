import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { and, desc, eq, isNull, sql } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { v4 as uuidv4 } from 'uuid';

import * as schema from '../../../../drizzle/schema';
import { DRIZZLE_PROVIDER } from '../../../drizzle/drizzle.provider';
import { BaseDrizzleRepository } from '../../common/repositories/base.drizzle.repository';
import { EntityEntity } from '../../entities/entities/entity.entity';
import { UserFilterModel } from '../../users/models/user-filter.model';
import { Organization } from '../entities';
import { OrganizationStatusEnum } from '../enums';
import { OrganizationNotFoundByIdException } from '../exceptions/organization-not-found-by-id.exception';
import { CreateOrganizationDto, UpdateOrganizationDto } from '../models';

// Type interfaces for better type safety
interface Role {
    id: string;
    name: string;
    organizationId: string;
}

interface UserWithRole {
    id: string;
    keycloakId: string;
    email: string;
    firstName: string;
    lastName: string;
    phone: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
    lastLoginAt: string;
    locale: string;
    timezone: string;
    roleId: string;
    roleName: string;
    role: {
        id: string;
        name: string;
    };
    ignoreAdvPerms: boolean;
}

interface InvitedUserWithRole {
    id: string;
    email: string;
    isActive: boolean;
    keycloakId: string;
    createdAt: string;
    updatedAt: string;
    roleId: string;
    roleName: string;
    role: {
        id: string;
        name: string;
    };
}

interface OrganizationEntityLink {
    id: string;
    organizationId: string;
    entityId: string;
    createdAt: string;
    updatedAt: string;
}

@Injectable()
export class OrganizationDrizzleRepository extends BaseDrizzleRepository<Organization> {
    constructor(
        @Inject(DRIZZLE_PROVIDER)
        db: NodePgDatabase<typeof schema>,
    ) {
        super(db, schema.organizations);
    }

    async findEntityIdByOrganizationId(organizationId: string): Promise<string | null> {
        const data = await this.db
            .select({
                entity_id: schema.organizationEntities.entityId,
            })
            .from(schema.organizationEntities)
            .where(eq(schema.organizationEntities.organizationId, organizationId))
            .limit(1);

        return data.length > 0 ? data[0].entity_id : null;
    }

    async findEntityIdsByOrganizationId(organizationId: string): Promise<string[]> {
        const data = await this.db
            .select({
                entity_id: schema.organizationEntities.entityId,
            })
            .from(schema.organizationEntities)
            .where(eq(schema.organizationEntities.organizationId, organizationId));

        return data.map((item) => item.entity_id);
    }

    async getAdvPermFlagOrganizationId(organizationId: string, keycloakUserId: string): Promise<boolean> {
        const data = await this.db
            .select({
                advPerms: sql`(CASE WHEN ${schema.userRoles.ignoreAdvPerms} = true THEN false ELSE ${schema.organizations.advPerms} END)::boolean`,
            })
            .from(schema.organizations)
            .leftJoin(schema.roles, eq(schema.organizations.id, schema.roles.organizationId))
            .leftJoin(schema.userRoles, eq(schema.roles.id, schema.userRoles.roleId))
            .leftJoin(schema.users, eq(schema.userRoles.userId, schema.users.id))
            .where(and(eq(schema.users.keycloakId, keycloakUserId), eq(schema.organizations.id, organizationId)))
            .groupBy(schema.organizations.advPerms, schema.userRoles.ignoreAdvPerms)
            .orderBy(desc(schema.userRoles.ignoreAdvPerms), desc(schema.organizations.advPerms))
            .limit(1);

        // @ts-expect-error The result is always boolean
        return data.length > 0 ? data[0].advPerms : false;
    }

    async getAllowedEndUserIdsByKeycloakIdAndResellerId(keycloakId: string, resellerIds: string[]): Promise<string[]> {
        const data = await this.db.execute(sql`
            select 
                reseller_id,
                array_agg(end_user_id) as end_user_ids
            from mv_adv_permissions
            where keycloak_id = ${keycloakId}
              AND reseller_id IN ${resellerIds}
            group by reseller_id;
        `);

        // @ts-expect-error TODO: fix this
        return data.rows.length > 0 ? data.rows[0].end_user_ids : [];
    }

    async updateDataInAdvPermView(): Promise<void> {
        await this.db.execute(sql`REFRESH MATERIALIZED VIEW CONCURRENTLY mv_adv_permissions;`);
    }

    async findOrganizationsByUserId(userId: string): Promise<Organization[]> {
        const query = sql`
            WITH user_org_ids AS (
                SELECT r.organization_id
                FROM users u
                JOIN user_roles ur ON u.id = ur.user_id
                JOIN roles r ON ur.role_id = r.id
                WHERE u.keycloak_id = ${userId}
            )
            SELECT 
                o.id,
                o.name,
                o.parent_id as "parentId",
                o.status,
                o.emailable,
                o.created_at as "createdAt",
                o.updated_at as "updatedAt",
                COALESCE(
                    json_agg(
                        json_build_object(
                            'id', e.id,                            
                            'name', e.name,
                            'type', e.type,
                            'ownerId', e.owner_id
                        )
                    ) FILTER (WHERE e.id IS NOT NULL), '[]'
                ) AS entities
            FROM organizations o
            JOIN user_org_ids uoi ON o.id = uoi.organization_id
            LEFT JOIN organization_entities oe ON o.id = oe.organization_id
            LEFT JOIN entities e ON oe.entity_id = e.id
            WHERE o.deleted_at IS NULL
            GROUP BY o.id, o.name, o.parent_id, o.status, o.emailable, o.created_at, o.updated_at
            ORDER BY o.name ASC;
        `;

        const result = await this.db.execute(query);
        return result.rows as unknown as Organization[];
    }

    async findOrganizationsAdmin(): Promise<Organization[]> {
        const query = sql`
            SELECT 
                o.id,
                o.name,
                o.parent_id as "parentId",
                o.status,
                o.emailable,
                o.created_at as "createdAt",
                o.updated_at as "updatedAt",
                o.adv_perms as "advPerms",
                COALESCE(
                    json_agg(
                        json_build_object(
                            'id', e.id,
                            'name', e.name,
                            'type', e.type,
                            'ownerId', e.owner_id
                        )
                    ) FILTER (WHERE e.id IS NOT NULL), '[]'
                ) AS entities
            FROM organizations o
            LEFT JOIN organization_entities oe ON o.id = oe.organization_id
            LEFT JOIN entities e ON oe.entity_id = e.id
            WHERE o.deleted_at IS NULL
            GROUP BY o.id, o.name, o.parent_id, o.status, o.emailable, o.created_at, o.updated_at
        `;

        const result = await this.db.execute(query);
        return result.rows as unknown as Organization[];
    }

    async findOrganizationById(id: string): Promise<Organization | null> {
        const organizations = await this.db
            .select()
            .from(schema.organizations)
            .where(and(eq(schema.organizations.id, id), isNull(schema.organizations.deletedAt)))
            .limit(1);

        if (!organizations.length) {
            throw new OrganizationNotFoundByIdException(id);
        }

        return organizations[0] as unknown as Organization;
    }

    async findRolesByOrganizationId(organizationId: string): Promise<Role[]> {
        return this.db.select().from(schema.roles).where(eq(schema.roles.organizationId, organizationId)) as Promise<
            Role[]
        >;
    }

    async findUsersByOrganizationId(organizationId: string, filter?: UserFilterModel): Promise<UserWithRole[]> {
        const query = sql`
            SELECT DISTINCT
                u.id,
                u.keycloak_id as "keycloakId",
                u.email,
                u.first_name as "firstName",
                u.last_name as "lastName",
                u.phone,
                u.is_active as "isActive",
                u.created_at as "createdAt",
                u.updated_at as "updatedAt",
                u.last_login_at as "lastLoginAt",
                u.locale,
                u.timezone,
                r.id as "roleId",
                r.name as "roleName",
                ur.ignore_adv_perms as "ignoreAdvPerms",
                                u.status as "status",
                u.invite_expires_at as "inviteExpiresAt"
            FROM users u
            JOIN user_roles ur ON u.id = ur.user_id
            JOIN roles r ON ur.role_id = r.id
            WHERE r.organization_id = ${organizationId}
            ${
                filter?.search
                    ? sql`AND (
                u.first_name ILIKE ${'%' + filter.search + '%'} OR
                u.last_name ILIKE ${'%' + filter.search + '%'} OR
                u.email ILIKE ${'%' + filter.search + '%'}
            )`
                    : sql``
            }
        `;

        const result = await this.db.execute(query);

        return (result.rows as unknown as UserWithRole[]).map((user: UserWithRole) => ({
            ...user,
            role: {
                id: user.roleId,
                name: user.roleName,
            },
        }));
    }

    async findInvitedUsersByOrganizationId(
        organizationId: string,
        filter?: UserFilterModel,
    ): Promise<InvitedUserWithRole[]> {
        const query = sql`
            SELECT 
                u.id, 
                u.email, 
                u.is_active as "isActive", 
                u.keycloak_id as "keycloakId", 
                u.created_at as "createdAt", 
                u.updated_at as "updatedAt", 
                r.id as "roleId", 
                r.name as "roleName",
                u.status as "status",
                u.invite_expires_at as "inviteExpiresAt"
            FROM invitations i
            LEFT JOIN LATERAL jsonb_array_elements_text(i.role_ids) AS role_id(role_id_text) ON true
            LEFT JOIN roles r ON role_id.role_id_text::uuid = r.id
            LEFT JOIN users u ON i.user_id::uuid = u.id
            WHERE i.organization_ids @> ${JSON.stringify([organizationId])}::jsonb
            AND i.is_accepted = false
            ${
                filter?.search
                    ? sql`AND (
                u.first_name ILIKE ${'%' + filter.search + '%'} OR 
                u.last_name ILIKE ${'%' + filter.search + '%'} OR 
                u.email ILIKE ${'%' + filter.search + '%'}
            )`
                    : sql``
            }
        `;

        const result = await this.db.execute(query);

        return (result.rows as unknown as InvitedUserWithRole[]).map((user: InvitedUserWithRole) => ({
            ...user,
            role: {
                id: user.roleId,
                name: user.roleName,
            },
        }));
    }

    async createOrganization(dto: CreateOrganizationDto): Promise<Organization> {
        const [organization] = await this.db
            .insert(schema.organizations)
            .values({
                id: uuidv4(),
                name: dto.name,
                status: OrganizationStatusEnum.INACTIVE as any,
                emailable: false,
                locale: dto?.locale ?? 'de',
                advPerms: dto.advPerms || false,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
            })
            .returning();

        if (dto.entityIds) {
            for (const entityId of dto.entityIds) {
                await this.linkEntityToOrganization(organization.id, entityId);
            }
        }

        return organization as unknown as Organization;
    }

    async updateOrganization(id: string, dto: UpdateOrganizationDto): Promise<Organization> {
        const [updated] = await this.db
            .update(schema.organizations)
            .set({
                ...dto,
                updatedAt: new Date().toISOString(),
            })
            .where(eq(schema.organizations.id, id))
            .returning();

        if (!updated) {
            throw new OrganizationNotFoundByIdException(id);
        }

        return updated as unknown as Organization;
    }

    async deleteOrganization(id: string): Promise<Organization> {
        const [updated] = await this.db
            .update(schema.organizations)
            .set({
                deletedAt: new Date().toISOString(),
            })
            .where(eq(schema.organizations.id, id))
            .returning();

        if (!updated) {
            throw new OrganizationNotFoundByIdException(id);
        }

        return updated as unknown as Organization;
    }

    async findEntityByOrganizationId(organizationId: string): Promise<EntityEntity | null> {
        const data = await this.db
            .select({
                entity: schema.entities,
            })
            .from(schema.organizationEntities)
            .innerJoin(schema.entities, eq(schema.organizationEntities.entityId, schema.entities.id))
            .where(eq(schema.organizationEntities.organizationId, organizationId))
            .limit(1);

        return data.length > 0 ? (data[0].entity as unknown as EntityEntity) : null;
    }

    async findEntitiesByOrganizationId(organizationId: string): Promise<EntityEntity[]> {
        const data = await this.db
            .select({
                entity: schema.entities,
            })
            .from(schema.organizationEntities)
            .innerJoin(schema.entities, eq(schema.organizationEntities.entityId, schema.entities.id))
            .where(eq(schema.organizationEntities.organizationId, organizationId));

        return data.map((item) => item.entity as unknown as EntityEntity);
    }

    async findOrganizationByEntityId(entityId: string): Promise<Organization | null> {
        const data = await this.db
            .select({
                organization: schema.organizations,
            })
            .from(schema.organizationEntities)
            .innerJoin(schema.organizations, eq(schema.organizationEntities.organizationId, schema.organizations.id))
            .where(eq(schema.organizationEntities.entityId, entityId))
            .limit(1);

        return data.length > 0 ? (data[0].organization as unknown as Organization) : null;
    }

    async findOrganizationLinkByEntityId(entityId: string): Promise<OrganizationEntityLink | null> {
        const data = await this.db
            .select()
            .from(schema.organizationEntities)
            .where(eq(schema.organizationEntities.entityId, entityId))
            .limit(1);

        return data.length > 0 ? (data[0] as OrganizationEntityLink) : null;
    }

    async linkEntityToOrganization(organizationId: string, entityId: string): Promise<OrganizationEntityLink> {
        const organization = await this.findOrganizationById(organizationId);
        if (!organization) {
            throw new NotFoundException(`Organization with ID ${organizationId} not found`);
        }

        const entity = await this.db.select().from(schema.entities).where(eq(schema.entities.id, entityId)).limit(1);
        if (!entity.length) {
            throw new NotFoundException(`Entity with ID ${entityId} not found`);
        }

        const [link] = await this.db
            .insert(schema.organizationEntities)
            .values({
                id: uuidv4(),
                organizationId,
                entityId,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
            })
            .returning();

        return link as OrganizationEntityLink;
    }

    async unlinkEntityFromOrganization(organizationId: string, entityId: string): Promise<void> {
        const link = await this.db
            .select()
            .from(schema.organizationEntities)
            .where(
                and(
                    eq(schema.organizationEntities.organizationId, organizationId),
                    eq(schema.organizationEntities.entityId, entityId),
                ),
            )
            .limit(1);

        if (!link.length) {
            throw new NotFoundException(`Link between organization ${organizationId} and entity ${entityId} not found`);
        }

        await this.db
            .delete(schema.organizationEntities)
            .where(
                and(
                    eq(schema.organizationEntities.organizationId, organizationId),
                    eq(schema.organizationEntities.entityId, entityId),
                ),
            );
    }

    async updateEntityLink(organizationId: string, newEntityId: string): Promise<OrganizationEntityLink> {
        const currentLink = await this.db
            .select()
            .from(schema.organizationEntities)
            .where(eq(schema.organizationEntities.organizationId, organizationId))
            .limit(1);

        if (currentLink.length) {
            await this.db
                .delete(schema.organizationEntities)
                .where(eq(schema.organizationEntities.organizationId, organizationId));
        }

        return this.linkEntityToOrganization(organizationId, newEntityId);
    }

    async manageEntities(organizationId: string, entityIds: string[]): Promise<OrganizationEntityLink[]> {
        const currentLinks = await this.db
            .select()
            .from(schema.organizationEntities)
            .where(eq(schema.organizationEntities.organizationId, organizationId));

        this.logger.log('currentLinks', currentLinks);

        const removedLinks = currentLinks.filter((link) => !entityIds.includes(link.entityId));
        const addedLinks = entityIds.filter((id) => !currentLinks.some((link) => link.entityId === id));

        this.logger.log('removedLinks', removedLinks);
        this.logger.log('addedLinks', addedLinks);

        for (const link of removedLinks) {
            await this.unlinkEntityFromOrganization(organizationId, link.entityId);
        }

        for (const link of addedLinks) {
            await this.linkEntityToOrganization(organizationId, link);
        }

        const links = await this.db
            .select()
            .from(schema.organizationEntities)
            .where(eq(schema.organizationEntities.organizationId, organizationId));

        const organization = await this.findOrganizationById(organizationId);

        if (!links.length) {
            await this.db
                .update(schema.organizations)
                .set({
                    status: OrganizationStatusEnum.INACTIVE as any,
                })
                .where(eq(schema.organizations.id, organizationId));
        }

        if (links.length && organization.emailable) {
            await this.db
                .update(schema.organizations)
                .set({
                    status: OrganizationStatusEnum.ACTIVE as any,
                })
                .where(eq(schema.organizations.id, organizationId));
        }

        return links;
    }
}
