import { FilterQuery } from '@mikro-orm/core';
import { SqlEntityManager, wrap } from '@mikro-orm/postgresql';
import { BadRequestException, Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

import * as schema from '../../../../drizzle/schema';
import { DRIZZLE_PROVIDER } from '../../../drizzle/drizzle.provider';
import { BaseRepository } from '../../common/repositories/base.repository';
import { EntityEntity } from '../../entities/entities/entity.entity';
import { Role } from '../../users/entities/role.entity';
import { User } from '../../users/entities/user.entity';
import { UserRole } from '../../users/entities/user-role.entity';
import { UserFilterModel } from '../../users/models/user-filter.model';
import { Organization, OrganizationEntity } from '../entities';
import { OrganizationStatusEnum } from '../enums';
import { OrganizationNotFoundByIdException } from '../exceptions/organization-not-found-by-id.exception';
import { CreateOrganizationDto, UpdateOrganizationDto } from '../models';

@Injectable()
export class OrganizationRepository extends BaseRepository<Organization> {
    private readonly logger = new Logger(OrganizationRepository.name);

    constructor(
        em: SqlEntityManager,
        @Inject(DRIZZLE_PROVIDER)
        db: NodePgDatabase<typeof schema>,
    ) {
        super(em, Organization.name, db);
    }

    async findOrganizationsByUserId(userId: string): Promise<Organization[]> {
        const user = await this.em.findOne(User, { keycloakId: userId });
        if (!user) {
            this.logger.warn(`User with ID ${userId} not found`);
            return [];
        }

        const userRoles = await this.em.find(UserRole, { user: { id: user.id } }, { populate: ['role.organization'] });
        if (!userRoles.length) {
            this.logger.warn(`No roles found for user ID ${userId}`);
            return [];
        }

        const organizationIds = userRoles.map((userRole) => userRole.role.organization.id);
        if (!organizationIds.length) {
            return [];
        }

        const query = `
            SELECT 
                o.id,
                o.name,
                o.parent_id,
                o.status,
                o.emailable,
                o.created_at as "createdAt",
                o.updated_at as "updatedAt",
                COALESCE(
                    json_agg(
                        json_build_object(
                            'id', e.id,
                            'name', e.name,
                            'type', e.type
                        )
                    ) FILTER (WHERE e.id IS NOT NULL), '[]'
                ) AS entities
            FROM organizations o
            LEFT JOIN organization_entities oe ON o.id = oe.organization_id
            LEFT JOIN entities e ON oe.entity_id = e.id
            WHERE o.id IN (${organizationIds.map(() => '?').join(', ')}) 
            GROUP BY o.id, o.name;
        `;

        const result = await this.getKnex().raw(query, organizationIds);
        const organizations = result.rows as Organization[];
        return organizations;
    }

    async findOrganizationsAdmin(): Promise<Organization[]> {
        const query = `
            SELECT 
                o.id,
                o.name,
                o.parent_id,
                o.status,
                o.emailable,
                o.created_at as "createdAt",
                o.updated_at as "updatedAt",
                COALESCE(
                    json_agg(
                        json_build_object(
                            'id', e.id,
                            'name', e.name,
                            'type', e.type
                        )
                    ) FILTER (WHERE e.id IS NOT NULL), '[]'
                ) AS entities
            FROM organizations o
            LEFT JOIN organization_entities oe ON o.id = oe.organization_id
            LEFT JOIN entities e ON oe.entity_id = e.id
            GROUP BY o.id, o.name;
        `;

        const result = await this.getKnex().raw(query);
        const organizations = result.rows as Organization[];

        return organizations;
    }

    async findOrganizationById(id: string): Promise<Organization | null> {
        const organization = await this.em.findOne(Organization, { id });

        if (!organization) {
            throw new OrganizationNotFoundByIdException(id);
        }

        return organization;
    }

    async findRolesByOrganizationId(organizationId: string): Promise<Role[]> {
        return this.em.find(Role, { organization: { id: organizationId } });
    }

    async findUsersByOrganizationId(organizationId: string, filter?: UserFilterModel): Promise<User[]> {
        const where: FilterQuery<UserRole> = {
            role: {
                organization: {
                    id: organizationId,
                },
            },
        };
        if (filter?.search) {
            where.$or = [
                {
                    user: {
                        firstName: {
                            $ilike: `%${filter.search}%`,
                        },
                    },
                },
                {
                    user: {
                        lastName: {
                            $ilike: `%${filter.search}%`,
                        },
                    },
                },
                {
                    user: {
                        email: {
                            $ilike: `%${filter.search}%`,
                        },
                    },
                },
            ];
        }
        const userRoles = await this.em.find(UserRole, where, {
            populate: ['user', 'role'],
        });

        const users = userRoles.reduce((acc: User[], { user, role }) => {
            const existing = acc.find((entry) => entry.id === user.id);
            return existing
                ? acc
                : [
                      ...acc,
                      {
                          ...user,
                          role,
                      },
                  ];
        }, []);

        return users;
    }

    async findInvitedUsersByOrganizationId(organizationId: string, filter?: UserFilterModel): Promise<User[]> {
        let query = `
             SELECT u.id as user_id, u.email as user_email, u.is_active as user_is_active, u.keycloak_id, u.created_at as user_created_at, u.updated_at as user_updated_at, r.id as role_id, r.name as role_name
                FROM invitations i
                LEFT JOIN roles r ON i.role_id::uuid = r.id
                LEFT JOIN users u ON i.user_id::uuid = u.id
             WHERE i.organization_ids @> '["${organizationId}"]'::jsonb
        `;

        if (filter?.search) {
            query += ` AND (u.first_name ILIKE '%${filter.search}%' OR u.last_name ILIKE '%${filter.search}%' OR u.email ILIKE '%${filter.search}%')`;
        }
        const result = await this.getKnex().raw(query);
        const invitationsRaw = result.rows as any[];

        const users = invitationsRaw.map((entry: any) => ({
            id: entry.user_id,
            email: entry.user_email,
            isActive: entry.user_is_active,
            keycloakId: entry.user_keycloak_id,
            createdAt: new Date(entry.user_created_at),
            updatedAt: new Date(entry.user_updated_at),
            role: {
                id: entry.role_id,
                name: entry.role_name,
            },
        })) as unknown as User[];

        return users as User[];
    }

    async createOrganization(dto: CreateOrganizationDto): Promise<Organization> {
        const organization = new Organization();
        organization.name = dto.name;

        await this.em.persistAndFlush(organization);

        if (dto.entityIds) {
            for (const entityId of dto.entityIds) {
                await this.linkEntityToOrganization(organization.id, entityId);
            }
        }

        return organization;
    }

    async updateOrganization(id: string, dto: UpdateOrganizationDto): Promise<Organization> {
        const organization = await this.findOneOrFail({ id });

        wrap(organization).assign(dto, { mergeObjectProperties: true });

        await this.em.persistAndFlush(organization);

        return organization;
    }

    async findEntityByOrganizationId(organizationId: string): Promise<EntityEntity | null> {
        const link = await this.em.findOne(
            OrganizationEntity,
            { organization: organizationId },
            { populate: ['entity'] },
        );
        return link?.entity || null;
    }

    async byId(organizationId: string) {
        return this.findOne({
            id: organizationId,
        });
    }

    async findOrganizationByEntityId(entityId: string): Promise<Organization | null> {
        const link = await this.em.findOne(OrganizationEntity, { entity: entityId }, { populate: ['organization'] });

        return link?.organization || null;
    }

    async findOrganizationLinkByEntityId(entityId: string): Promise<OrganizationEntity | null> {
        return this.em.findOne(OrganizationEntity, { entity: entityId });
    }

    async linkEntityToOrganization(organizationId: string, entityId: string): Promise<OrganizationEntity> {
        const organization = await this.findOne(organizationId);
        if (!organization) {
            throw new OrganizationNotFoundByIdException(organizationId);
        }

        const entity = await this.em.findOne(EntityEntity, entityId);
        if (!entity) {
            throw new NotFoundException(`Entity with ID ${entityId} not found`);
        }

        // Check if this entity is already linked to any organization
        const existingLink = await this.em.findOne(OrganizationEntity, { entity: entityId });
        if (existingLink) {
            throw new BadRequestException(
                `Entity is already linked to organization with ID ${existingLink.organization.id}`,
            );
        }

        const existingOrganizationRelation = await this.em.findOne(OrganizationEntity, {
            organization: organizationId,
        });

        if (existingOrganizationRelation) {
            throw new BadRequestException(
                `Organization with ID ${existingOrganizationRelation.organization.id} already has related entity`,
            );
        }

        // Create new link
        const link = new OrganizationEntity();
        link.organization = organization;
        link.entity = entity;

        await this.nativeUpdate({ id: organizationId }, { status: OrganizationStatusEnum.ACTIVE });

        await this.em.persistAndFlush(link);
        return link;
    }

    async unlinkEntityFromOrganization(organizationId: string, entityId: string): Promise<void> {
        const link = await this.em.findOne(OrganizationEntity, {
            organization: organizationId,
            entity: entityId,
        });

        if (!link) {
            throw new NotFoundException(`Link between organization ${organizationId} and entity ${entityId} not found`);
        }

        await this.nativeUpdate({ id: organizationId }, { status: OrganizationStatusEnum.INACTIVE });

        await this.em.removeAndFlush(link);
    }

    async updateEntityLink(organizationId: string, newEntityId: string): Promise<OrganizationEntity> {
        // Find current link
        const currentLink = await this.em.findOne(OrganizationEntity, { organization: organizationId });

        // If exists, remove it
        if (currentLink) {
            await this.em.removeAndFlush(currentLink);
        }

        // Create new link
        return this.linkEntityToOrganization(organizationId, newEntityId);
    }
}
