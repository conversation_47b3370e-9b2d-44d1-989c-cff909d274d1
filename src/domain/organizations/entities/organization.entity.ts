import { Collection, Entity, Enum, ManyToOne, OneToMany, PrimaryKey, Property } from '@mikro-orm/core';
import { v4 as uuidv4 } from 'uuid';

import { Role } from '../../users/entities/role.entity';
import { OrganizationStatusEnum } from '../enums';

/**
 * Organization entity represents a business organization.
 * Organizations can have a parent-child relationship and contain roles
 * that define the organizational boundaries for user permissions.
 */
@Entity({ tableName: 'organizations' })
export class Organization {
    @PrimaryKey({ type: 'uuid' })
    id: string = uuidv4();

    @Property()
    name: string;

    @Enum({
        items: () => OrganizationStatusEnum,
        nativeEnumName: 'organization_status',
        default: OrganizationStatusEnum.INACTIVE,
    })
    status: OrganizationStatusEnum = OrganizationStatusEnum.INACTIVE;

    @Property({ type: 'boolean', default: false })
    emailable: boolean = false;

    @ManyToOne(() => Organization, { nullable: true })
    parent?: Organization;

    @Property({ default: 'de' })
    locale: string = 'de';

    @OneToMany(() => Role, (role) => role.organization)
    roles = new Collection<Role>(this);

    @Property({ type: 'datetime' })
    createdAt: Date = new Date();

    @Property({ type: 'datetime', onUpdate: () => new Date() })
    updatedAt: Date = new Date();

    @Property({ type: 'datetime' })
    deletedAt?: Date;

    advPerms: boolean = false;
}
