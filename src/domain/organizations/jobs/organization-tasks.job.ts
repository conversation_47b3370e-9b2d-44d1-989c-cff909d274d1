import { Injectable, Logger } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';

import { OrganizationsService } from '../services';

@Injectable()
export class OrganizationTasksJob {
    private readonly logger = new Logger(OrganizationTasksJob.name);

    constructor(private readonly organizationsService: OrganizationsService) {}

    @Cron('0 * * * *')
    async handleCron() {
        this.logger.debug('Updating advanced permissioning materialized view');
        await this.organizationsService.updateAdvancedPermissions();
    }
}
