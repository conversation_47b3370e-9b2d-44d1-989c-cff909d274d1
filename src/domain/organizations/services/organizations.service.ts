import { Inject, Injectable, Logger } from '@nestjs/common';

import { EventNameEnum } from '../../common/enums';
import { Context } from '../../common/interfaces';
import { EventsService } from '../../common/services';
import { EntityEntity } from '../../entities/entities/entity.entity';
import { UserModel } from '../../users/models/user.model';
import { UserFilterModel } from '../../users/models/user-filter.model';
import { UserPresenter } from '../../users/presenters/user.presenter';
import { Organization } from '../entities/organization.entity';
import { OrganizationStatusEnum } from '../enums';
import { OrganizationNotFoundByIdException } from '../exceptions/organization-not-found-by-id.exception';
import { CreateOrganizationDto, OrganizationDto, UpdateOrganizationDto } from '../models';
import { OrganizationPresenter } from '../presenters';
import { OrganizationDrizzleRepository } from '../repositories/organization.drizzle.repository';

// Type interfaces for Drizzle repository return types
interface DrizzleRole {
    id: string;
    name: string;
    organizationId: string;
}

interface DrizzleUserWithRole {
    id: string;
    keycloakId: string;
    email: string;
    firstName: string;
    lastName: string;
    phone: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
    lastLoginAt: string;
    locale: string;
    timezone: string;
    roleId: string;
    roleName: string;
    role: {
        id: string;
        name: string;
    };
    ignoreAdvPerms?: boolean;
}

interface DrizzleInvitedUserWithRole {
    id: string;
    email: string;
    isActive: boolean;
    keycloakId: string;
    createdAt: string;
    updatedAt: string;
    roleId: string;
    roleName: string;
    role: {
        id: string;
        name: string;
    };
}

interface DrizzleOrganizationEntityLink {
    id: string;
    organizationId: string;
    entityId: string;
    createdAt: string;
    updatedAt: string;
}

@Injectable()
export class OrganizationsService {
    private readonly logger = new Logger(OrganizationsService.name);

    constructor(
        @Inject(OrganizationDrizzleRepository)
        private readonly organizationDrizzleRepository: OrganizationDrizzleRepository,
        private readonly organizationPresenter: OrganizationPresenter,
        private readonly eventsService: EventsService,
        private readonly userPresenter: UserPresenter,
    ) {}

    async getOrganizationsByUserId(userId: string): Promise<OrganizationDto[]> {
        const entries = await this.organizationDrizzleRepository.findOrganizationsByUserId(userId);

        const items = entries.map((entity) => this.organizationPresenter.toFullModel(entity));

        return items;
    }

    async getRolesByOrganizationId(organizationId: string): Promise<DrizzleRole[]> {
        const organization = await this.organizationDrizzleRepository.findOrganizationById(organizationId);

        if (!organization) {
            throw new OrganizationNotFoundByIdException(organizationId);
        }
        return this.organizationDrizzleRepository.findRolesByOrganizationId(organization.id);
    }

    async getOrganizationById(id: string): Promise<OrganizationDto> {
        const organization = await this.organizationDrizzleRepository.findOrganizationById(id);

        if (!organization) {
            throw new OrganizationNotFoundByIdException(id);
        }

        const entities = await this.organizationDrizzleRepository.findEntitiesByOrganizationId(id);
        return this.organizationPresenter.toYourOrganizationModel(organization, entities);
    }

    async getUsersForOrganization(context: Context, id: string, filter?: UserFilterModel): Promise<UserModel[]> {
        const organization = await this.organizationDrizzleRepository.findOrganizationById(id);

        if (!organization) {
            throw new OrganizationNotFoundByIdException(id);
        }

        const [orgUsers, invitedUsers] = await Promise.all([
            this.organizationDrizzleRepository.findUsersByOrganizationId(id, filter),
            this.organizationDrizzleRepository.findInvitedUsersByOrganizationId(id, filter),
        ]);

        const users = [...orgUsers, ...invitedUsers].reduce(
            (acc: (DrizzleUserWithRole | DrizzleInvitedUserWithRole)[], user) =>
                acc.find((entry) => entry.id === user.id) ? acc : [...acc, user],
            [],
        );

        return users.map((user) => this.userPresenter.toFullModel(context, user));
    }

    async createOrganization(dto: CreateOrganizationDto): Promise<Organization> {
        this.logger.log('Organization creation started', {
            organizationName: dto.name,
            locale: dto.locale,
            advPerms: dto.advPerms,
            entityIds: dto.entityIds,
        });

        try {
            const organization = await this.organizationDrizzleRepository.createOrganization(dto);

            this.logger.debug('Organization created in database', {
                organizationId: organization.id,
                organizationName: organization.name,
                status: organization.status,
                locale: organization.locale,
            });

            this.eventsService.emitEvent({
                eventName: EventNameEnum.ORGANIZATION_CREATED_V1,
                payload: organization,
            });

            this.logger.log('Organization created successfully', {
                organizationId: organization.id,
                organizationName: organization.name,
                status: organization.status,
                locale: organization.locale,
                advPerms: organization.advPerms,
            });

            return organization;
        } catch (error) {
            this.logger.error('Organization creation failed', {
                organizationName: dto.name,
                locale: dto.locale,
                advPerms: dto.advPerms,
                entityIds: dto.entityIds,
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined,
            });
            throw error;
        }
    }

    async updateOrganization(context: Context, id: string, dto: UpdateOrganizationDto): Promise<Organization> {
        this.logger.log('Organization update started', {
            organizationId: id,
            userId: context.user.id,
            updatedFields: Object.keys(dto),
        });

        try {
            const links = await this.organizationDrizzleRepository.findEntityIdsByOrganizationId(id);

            const organization = await this.organizationDrizzleRepository.updateOrganization(id, {
                ...dto,
                ...(dto.emailable !== undefined
                    ? dto.emailable && links.length > 0
                        ? { status: OrganizationStatusEnum.ACTIVE }
                        : { status: OrganizationStatusEnum.INACTIVE }
                    : {}),
            });

            this.logger.debug('Organization updated in database', {
                organizationId: id,
                organizationName: organization.name,
                userId: context.user.id,
                updatedFields: Object.keys(dto),
            });

            this.eventsService.emitEvent({
                eventName: EventNameEnum.ORGANIZATION_UPDATED_V1,
                payload: organization,
            });

            await this.updateAdvancedPermissions();
            this.logger.debug('Advanced permissions updated after organization update', {
                organizationId: id,
                userId: context.user.id,
            });

            this.logger.log('Organization updated successfully', {
                organizationId: id,
                organizationName: organization.name,
                userId: context.user.id,
                updatedFields: Object.keys(dto),
            });

            return organization;
        } catch (error) {
            this.logger.error('Organization update failed', {
                organizationId: id,
                userId: context.user.id,
                updatedFields: Object.keys(dto),
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined,
            });
            throw error;
        }
    }

    async deleteOrganization(context: Context, id: string): Promise<Organization> {
        this.logger.log('Organization delete started', {
            organizationId: id,
            userId: context.user.id,
            updatedFields: ['deletedAt'],
        });

        try {
            const organization = await this.organizationDrizzleRepository.deleteOrganization(id);
            this.eventsService.emitEvent({
                eventName: EventNameEnum.ORGANIZATION_DELETED_V1,
                payload: organization,
            });

            return organization;
        } catch (error) {
            this.logger.error('Organization deleted failed', {
                organizationId: id,
                userId: context.user.id,
                updatedFields: ['deletedAt'],
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined,
            });
            throw error;
        }
    }

    async getAllOrganizations(): Promise<OrganizationDto[]> {
        const entries = await this.organizationDrizzleRepository.findOrganizationsAdmin();
        const items = entries.map((entity) => this.organizationPresenter.toFullModel(entity));

        return items;
    }

    async getAdvPermFlagOrganizationId(organizationId: string, keycloakUserId: string): Promise<any> {
        return this.organizationDrizzleRepository.getAdvPermFlagOrganizationId(organizationId, keycloakUserId);
    }

    async getAllowedEndUserIdsByKeycloakIdAndResellerId(keycloakId: string, resellerIds: string[]): Promise<string[]> {
        return await this.organizationDrizzleRepository.getAllowedEndUserIdsByKeycloakIdAndResellerId(
            keycloakId,
            resellerIds,
        );
    }

    async getEntityIdByOrganizationId(organizationId: string): Promise<string | null> {
        return this.organizationDrizzleRepository.findEntityIdByOrganizationId(organizationId);
    }

    async getEntityIdsByOrganizationId(organizationId: string): Promise<string[]> {
        return this.organizationDrizzleRepository.findEntityIdsByOrganizationId(organizationId);
    }

    async getEntityByOrganizationId(organizationId: string): Promise<EntityEntity | null> {
        return this.organizationDrizzleRepository.findEntityByOrganizationId(organizationId);
    }

    async getEntitiesByOrganizationId(organizationId: string): Promise<EntityEntity[]> {
        return this.organizationDrizzleRepository.findEntitiesByOrganizationId(organizationId);
    }

    async getOrganizationByEntityId(entityId: string): Promise<Organization | null> {
        return this.organizationDrizzleRepository.findOrganizationByEntityId(entityId);
    }

    async linkEntityToOrganization(organizationId: string, entityId: string): Promise<DrizzleOrganizationEntityLink> {
        return this.organizationDrizzleRepository.linkEntityToOrganization(organizationId, entityId);
    }

    async manageEntities(organizationId: string, entityIds: string[]): Promise<DrizzleOrganizationEntityLink[]> {
        return this.organizationDrizzleRepository.manageEntities(organizationId, entityIds);
    }

    async unlinkEntityFromOrganization(organizationId: string, entityId: string): Promise<void> {
        await this.organizationDrizzleRepository.unlinkEntityFromOrganization(organizationId, entityId);
    }

    async updateEntityLink(organizationId: string, newEntityId: string): Promise<DrizzleOrganizationEntityLink> {
        return this.organizationDrizzleRepository.updateEntityLink(organizationId, newEntityId);
    }

    async updateAdvancedPermissions(): Promise<void> {
        await this.organizationDrizzleRepository.updateDataInAdvPermView();
    }
}
