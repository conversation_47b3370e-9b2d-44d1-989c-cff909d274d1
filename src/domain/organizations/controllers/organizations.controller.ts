import { Body, Controller, Delete, Get, Logger, Param, Patch, Post, Query, Request, Res } from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Response } from 'express';

import { RequestContext } from '../../../auth/context.decorator';
import { UserModel } from '../../../domain/users/models/user.model';
import { DisablePermissionsGuard, RequireResourceAction } from '../../../rbac/permissions.decorator';
import { BaseController } from '../../common/controllers/base.controller';
import { Context } from '../../common/interfaces';
import { RequestInterface } from '../../common/interfaces/request.interface';
import { EntityEntity } from '../../entities/entities/entity.entity';
import { ActionType, ResourceType } from '../../users/entities/permission.entity';
import { UserFilterModel } from '../../users/models/user-filter.model';
import { OrganizationNotFoundException } from '../exceptions/organization-not-found.exception';
import {
    ArchiveOrganizationDto,
    CreateOrganizationDto,
    LinkEntityDto,
    OrganizationDto,
    OrganizationEntityDto,
    OrganizationIdParamModel,
    OrganizationListDto,
    UpdateOrganizationDto,
} from '../models';
import { ManageEntitiesDto } from '../models/manage-entities.dto';
import { OrganizationsService } from '../services';

@Controller('organizations')
export class OrganizationsController extends BaseController {
    private readonly logger = new Logger(OrganizationsController.name);

    constructor(private readonly organizationsService: OrganizationsService) {
        super();
    }

    @DisablePermissionsGuard()
    @Get()
    @ApiResponse({
        status: 200,
        type: OrganizationListDto,
    })
    async getAllOrganizations(@Request() req: RequestInterface, @Res() res: Response) {
        const user = req.user;
        const organizations = await this.organizationsService.getOrganizationsByUserId(user.sub);
        return this.sendOk(res, organizations);
    }

    @DisablePermissionsGuard()
    @Get(':id')
    @ApiResponse({
        status: 200,
        type: OrganizationDto,
    })
    async getOrganizationById(@Param() params: OrganizationIdParamModel, @Res() res: Response) {
        const organization = await this.organizationsService.getOrganizationById(params.id);
        if (!organization) {
            throw new OrganizationNotFoundException();
        }
        return this.sendOk(res, organization);
    }

    @DisablePermissionsGuard()
    @Get(':id/roles')
    async getRoles(@Param() params: OrganizationIdParamModel, @Res() res: Response) {
        const roles = await this.organizationsService.getRolesByOrganizationId(params.id);
        return this.sendOk(res, roles);
    }

    @DisablePermissionsGuard()
    @ApiOperation({
        summary: 'Get organization users',
    })
    @ApiResponse({
        status: 200,
        type: [UserModel],
    })
    @ApiResponse({
        status: 400,
    })
    @Get(':id/users')
    async getUsersForOrganization(
        @Res() res: Response,
        @Param() params: OrganizationIdParamModel,
        @Query() query: UserFilterModel,
        @RequestContext() context: Context,
    ) {
        const users = await this.organizationsService.getUsersForOrganization(context, params.id, query);
        return this.sendOk(res, users);
    }

    @RequireResourceAction({ resource: ResourceType.ORGANIZATION, action: ActionType.CREATE })
    @ApiOperation({
        summary: 'Create a new organization',
    })
    @ApiResponse({
        status: 201,
        type: OrganizationDto,
    })
    @Post()
    async createOrganization(@Body() dto: CreateOrganizationDto, @Res() res: Response) {
        const correlationId = res.req.headers['x-correlation-id'] as string;

        this.logger.log('Organization creation started', {
            correlationId,
            organizationName: dto.name,
            locale: dto.locale,
            advPerms: dto.advPerms,
        });

        try {
            const organization = await this.organizationsService.createOrganization(dto);

            this.logger.log('Organization created successfully', {
                correlationId,
                organizationId: organization.id,
                organizationName: organization.name,
                status: organization.status,
                locale: organization.locale,
            });

            return this.sendCreated(res, organization);
        } catch (error) {
            this.logger.error('Organization creation failed', {
                correlationId,
                organizationName: dto.name,
                locale: dto.locale,
                advPerms: dto.advPerms,
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined,
            });
            throw error;
        }
    }

    @RequireResourceAction({ resource: ResourceType.ORGANIZATION, action: ActionType.UPDATE })
    @ApiOperation({
        summary: 'Update an organization',
    })
    @ApiResponse({
        status: 201,
        type: OrganizationDto,
    })
    @Patch('/:id')
    async updateOrganization(
        @Body() dto: UpdateOrganizationDto,
        @Param() { id }: OrganizationIdParamModel,
        @Res() res: Response,
        @RequestContext() context: Context,
    ) {
        const correlationId = res.req.headers['x-correlation-id'] as string;

        this.logger.log('Organization update started', {
            correlationId,
            organizationId: id,
            userId: context.user.id,
            updatedFields: Object.keys(dto),
        });

        try {
            const organization = await this.organizationsService.updateOrganization(context, id, dto);

            this.logger.log('Organization updated successfully', {
                correlationId,
                organizationId: id,
                organizationName: organization.name,
                userId: context.user.id,
                updatedFields: Object.keys(dto),
            });

            return this.sendCreated(res, organization);
        } catch (error) {
            this.logger.error('Organization update failed', {
                correlationId,
                organizationId: id,
                userId: context.user.id,
                updatedFields: Object.keys(dto),
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined,
            });
            throw error;
        }
    }

    @RequireResourceAction({ resource: ResourceType.ORGANIZATION, action: ActionType.UPDATE })
    @ApiOperation({
        summary: 'Delete an organization',
    })
    @ApiResponse({
        status: 204,
    })
    @Post('/archive')
    async archiveOrganization(
        @Body() dto: ArchiveOrganizationDto,
        @Res() res: Response,
        @RequestContext() context: Context,
    ) {
        const correlationId = res.req.headers['x-correlation-id'] as string;
        const { id: organizationId } = dto;

        this.logger.log('Organization deletion started', {
            correlationId,
            organizationId,
            userId: context.user.id,
        });

        try {
            const organization = await this.organizationsService.deleteOrganization(context, organizationId);

            this.logger.log('Organization deleted successfully', {
                correlationId,
                organizationId,
                organizationName: organization.name,
                userId: context.user.id,
            });

            return this.sendOk(res, organization);
        } catch (error) {
            this.logger.error('Organization delete failed', {
                correlationId,
                organizationId,
                userId: context.user.id,
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined,
            });
            throw error;
        }
    }

    @RequireResourceAction({ resource: ResourceType.ORGANIZATION, action: ActionType.READ })
    @ApiOperation({
        summary: 'Get all organizations',
    })
    @ApiResponse({
        status: 200,
        type: OrganizationListDto,
    })
    @Get('admin/all')
    async getAdminOrganizations(@Res() res: Response) {
        const organizations = await this.organizationsService.getAllOrganizations();

        return this.sendOk(res, organizations);
    }

    @RequireResourceAction({ resource: ResourceType.ORGANIZATION, action: ActionType.READ })
    @ApiOperation({
        summary: 'Get organization linked entity',
    })
    @ApiResponse({
        status: 200,
        type: EntityEntity,
    })
    @Get(':id/entities')
    async getOrganizationEntities(@Param() params: OrganizationIdParamModel, @Res() res: Response) {
        const entities = await this.organizationsService.getEntitiesByOrganizationId(params.id);
        return this.sendOk(res, entities);
    }

    @RequireResourceAction({ resource: ResourceType.ORGANIZATION, action: ActionType.UPDATE })
    @ApiOperation({
        deprecated: true,
        summary: 'Link entity to organization',
    })
    @ApiResponse({
        status: 200,
        type: OrganizationEntityDto,
    })
    @Post(':id/entity')
    async linkEntityToOrganization(
        @Param() params: OrganizationIdParamModel,
        @Body() dto: LinkEntityDto,
        @Res() res: Response,
    ) {
        const correlationId = res.req.headers['x-correlation-id'] as string;

        this.logger.log('Entity linking to organization started', {
            correlationId,
            organizationId: params.id,
            entityId: dto.entityId,
        });

        try {
            const link = await this.organizationsService.linkEntityToOrganization(params.id, dto.entityId);

            this.logger.log('Entity linked to organization successfully', {
                correlationId,
                organizationId: params.id,
                entityId: dto.entityId,
                linkId: link.id,
            });

            return this.sendOk(res, link);
        } catch (error) {
            this.logger.error('Entity linking to organization failed', {
                correlationId,
                organizationId: params.id,
                entityId: dto.entityId,
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined,
            });
            throw error;
        }
    }

    @RequireResourceAction({ resource: ResourceType.ORGANIZATION, action: ActionType.UPDATE })
    @ApiOperation({
        deprecated: true,
        summary: 'Manage entities linked to organization',
    })
    @ApiResponse({
        status: 200,
        type: OrganizationEntityDto,
    })
    @Post(':id/entities')
    async manageEntities(
        @Param() params: OrganizationIdParamModel,
        @Body() dto: ManageEntitiesDto,
        @Res() res: Response,
    ) {
        const correlationId = res.req.headers['x-correlation-id'] as string;

        this.logger.log('Organization entities management started', {
            correlationId,
            organizationId: params.id,
            entityIds: dto.entityIds,
            entitiesCount: dto.entityIds.length,
        });

        try {
            const links = await this.organizationsService.manageEntities(params.id, dto.entityIds);

            this.logger.log('Organization entities managed successfully', {
                correlationId,
                organizationId: params.id,
                entityIds: dto.entityIds,
                entitiesCount: dto.entityIds.length,
                linksCreated: links.length,
            });

            return this.sendOk(res, links);
        } catch (error) {
            this.logger.error('Organization entities management failed', {
                correlationId,
                organizationId: params.id,
                entityIds: dto.entityIds,
                entitiesCount: dto.entityIds.length,
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined,
            });
            throw error;
        }
    }
}
