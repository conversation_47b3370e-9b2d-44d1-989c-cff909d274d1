import { MikroOrmModule } from '@mikro-orm/nestjs';
import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { RbacModule } from '../../rbac/rbac.module';
import { EntitiesModule } from '../entities/entities.module';
import { UsersModule } from '../users/users.module';
import { OrganizationsController } from './controllers';
import { Organization, OrganizationEntity } from './entities';
import { OrganizationTasksJob } from './jobs/organization-tasks.job';
import { OrganizationPresenter } from './presenters';
import { OrganizationEntityRepository, OrganizationRepository } from './repositories';
import { OrganizationDrizzleRepository } from './repositories/organization.drizzle.repository';
import { OrganizationsService } from './services';

@Module({
    imports: [
        ConfigModule,
        MikroOrmModule.forFeature([Organization, OrganizationEntity]),
        forwardRef(() => UsersModule),
        forwardRef(() => EntitiesModule),
        forwardRef(() => RbacModule),
    ],
    controllers: [OrganizationsController],
    providers: [
        OrganizationsService,
        OrganizationTasksJob,
        OrganizationRepository,
        OrganizationDrizzleRepository,
        OrganizationEntityRepository,
        OrganizationPresenter,
    ],
    exports: [
        OrganizationRepository,
        OrganizationEntityRepository,
        OrganizationsService,
        OrganizationPresenter,
        MikroOrmModule.forFeature([Organization, OrganizationEntity]),
    ],
})
export class OrganizationsModule {}
