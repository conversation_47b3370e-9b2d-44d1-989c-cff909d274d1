import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

const ManageEntitiesSchema = extendApi(
    z.object({
        entityIds: z.array(z.string()).describe('The IDs of the entities to link to the organization'),
    }),
    {
        title: 'ManageEntities',
        description: 'Data required to manage entities linked to an organization',
    },
);

export class ManageEntitiesDto extends createZodDto(ManageEntitiesSchema) {
    static readonly zodSchema = ManageEntitiesSchema;
}
