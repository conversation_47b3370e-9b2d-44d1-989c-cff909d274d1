import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

const LinkEntitySchema = extendApi(
    z.object({
        entityId: z.string().describe('The ID of the entity to link to the organization'),
    }),
    {
        title: 'LinkEntity',
        description: 'Data required to link an entity to an organization',
    },
);

export class LinkEntityDto extends createZodDto(LinkEntitySchema) {
    static readonly zodSchema = LinkEntitySchema;
}
