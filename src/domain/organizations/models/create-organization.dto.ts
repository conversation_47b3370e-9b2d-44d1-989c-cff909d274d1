import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

const CreateOrganizationSchema = extendApi(
    z.object({
        name: z.string().min(1).max(255).describe('FIELDS.NAME_OF_THE_ORGANIZATION'),
        advPerms: z.boolean().optional().describe('FIELDS.ADVANCED_PERMISSIONS'),
        locale: z.string().max(3).default('de').describe('FIELDS.LOCALE'),
        entityIds: z
            .array(z.string())
            .optional()
            .describe('The IDs of the entities to link to this organization (optional)'),
    }),
    {
        title: 'CreateOrganization',
        description: 'Data required to create a new organization',
    },
);

export class CreateOrganizationDto extends createZodDto(CreateOrganizationSchema) {
    static readonly zodSchema = CreateOrganizationSchema;
}
