import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { OrganizationEntityDto } from './organization-entity.dto';

const OrganizationEntityListSchema = extendApi(
    z.object({
        items: z.array(OrganizationEntityDto.zodSchema),
        meta: z.object({
            total: z.number(),
            page: z.number(),
            limit: z.number(),
        }),
    }),
    {
        title: 'OrganizationEntityList',
        description: 'Organization-Entity relationship list model',
    },
);

export class OrganizationEntityListDto extends createZodDto(OrganizationEntityListSchema) {
    static readonly zodSchema = OrganizationEntityListSchema;
}
