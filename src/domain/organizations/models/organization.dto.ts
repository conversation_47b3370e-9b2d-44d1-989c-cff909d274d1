import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { RelatedItemLinkSchema } from '../../common/schemas/related-item-link.schema';
import { OrganizationStatusEnum } from '../enums';

const OrganizationSchema = extendApi(
    z.object({
        id: z.string().uuid(),
        name: z.string(),
        status: extendApi(z.nativeEnum(OrganizationStatusEnum).default(OrganizationStatusEnum.INACTIVE), {
            description: 'Organization status',
        }),
        emailable: z.boolean().default(false),
        locale: z.string().default('de'),
        advPerms: z.boolean().optional().describe('FIELDS.ADVANCED_PERMISSIONS'),
        parentId: z.string().uuid().nullable().optional(),
        createdAt: z.date(),
        updatedAt: z.date(),
        deletedAt: z.date().nullable().optional(),
        entities: extendApi(
            z
                .array(
                    z.object({
                        id: z.string(),
                        name: z.string(),
                        type: z.string(),
                    }),
                )
                .nullable()
                .optional()
                .default([]),
            {
                description: 'Entities',
            },
        ),
        _links: extendApi(
            z.object({
                self: extendApi(RelatedItemLinkSchema.omit({ count: true }), { description: 'Self' }),
                users: extendApi(RelatedItemLinkSchema.omit({ count: true }), { description: 'Users' }),
                contracts: extendApi(RelatedItemLinkSchema.omit({ count: true }), { description: 'Contracts' }),
                quotes: extendApi(RelatedItemLinkSchema.omit({ count: true }), { description: 'Quotes' }),
            }),
            {
                description: 'Links',
            },
        ),
    }),
    {
        title: 'Organization',
        description: 'Organization model',
    },
);

export class OrganizationDto extends createZodDto(OrganizationSchema) {
    static readonly zodSchema = OrganizationSchema;
}
