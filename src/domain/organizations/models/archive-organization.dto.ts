import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

const ArchiveOrganizationSchema = extendApi(
    z.object({
        id: z.string().describe('The ID of the organization to archive'),
    }),
    {
        title: 'ArchiveOrganization',
        description: 'Data required to archive an organization',
    },
);

export class ArchiveOrganizationDto extends createZodDto(ArchiveOrganizationSchema) {
    static readonly zodSchema = ArchiveOrganizationSchema;
}
