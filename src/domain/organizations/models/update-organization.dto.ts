import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

const UpdateOrganizationSchema = extendApi(
    z.object({
        name: z.string().min(1).max(255).optional().describe('FIELDS.NAME_OF_THE_ORGANIZATION'),
        advPerms: z.boolean().optional().describe('FIELDS.ADVANCED_PERMISSIONS'),
        emailable: z.boolean().optional().describe('FIELDS.EMAILABILITY_OF_THE_ORGANIZATION'),
        locale: z.string().max(3).default('de').describe('FIELDS.LOCALE'),
    }),
    {
        title: 'UpdateOrganization',
        description: 'Data required to update an organization',
    },
);

export class UpdateOrganizationDto extends createZodDto(UpdateOrganizationSchema) {
    static readonly zodSchema = UpdateOrganizationSchema;
}
