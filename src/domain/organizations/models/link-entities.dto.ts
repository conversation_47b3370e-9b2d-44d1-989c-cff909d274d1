import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

const LinkEntitiesSchema = extendApi(
    z.object({
        entityIds: z.array(z.string()).describe('The IDs of the entities to link to the organization'),
    }),
    {
        title: 'LinkEntity',
        description: 'Data required to link an entity to an organization',
    },
);

export class LinkEntitiesDto extends createZodDto(LinkEntitiesSchema) {
    static readonly zodSchema = LinkEntitiesSchema;
}
