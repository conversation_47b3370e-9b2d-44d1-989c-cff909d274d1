import { NotFoundException } from '@nestjs/common';
import { I18nContext } from 'nestjs-i18n';

import { I18nPath } from '../../../../i18n/generated/i18n.generated';

export class OrganizationNotFoundByIdException extends NotFoundException {
    constructor(id: string) {
        super(
            I18nContext.current().t<I18nPath>('errors.ORGANIZATION.ORGANIZATION_NOT_FOUND_BY_ID', {
                args: {
                    id,
                },
            }),
        );
    }
}
