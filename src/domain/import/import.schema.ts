import { boolean, foreignKey, jsonb, pgEnum, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { IImportedContractData } from '../contracts/interfaces/imported-contract-data.interface';
import { users } from '../users/user.schema';
import { ImportStatusEnum } from './enums/import-status.enum';

export const importStatus = pgEnum('import_status', ImportStatusEnum);
export const imports = pgTable(
    'imports',
    {
        id: uuid().primaryKey(),
        data: jsonb().$type<IImportedContractData>(),
        fileUrls: text('file_url').array(),
        createdByUserId: uuid('created_by_user_id'),
        status: importStatus('import_status').notNull(),
        success: boolean().notNull(),
        createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }).notNull().defaultNow(),
    },
    (table) => [
        foreignKey({
            columns: [table.createdByUserId],
            foreignColumns: [users.id],
            name: 'import_created_by_user_foreign',
        })
            .onUpdate('cascade')
            .onDelete('set null'),
    ],
);
