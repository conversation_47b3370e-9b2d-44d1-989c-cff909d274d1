import { Inject, Injectable } from '@nestjs/common';
import { eq, InferSelectModel } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { v4 as uuidv4 } from 'uuid';

import * as schema from '../../../../drizzle/schema';
import { DRIZZLE_PROVIDER } from '../../../drizzle/drizzle.provider';
import { BaseDrizzleRepository } from '../../common/repositories/base.drizzle.repository';
import { ImportCreateDto } from '../dtos/import-create.dto';

type ImportDrizzle = InferSelectModel<typeof schema.imports>;

@Injectable()
export class ImportRepository extends BaseDrizzleRepository<ImportDrizzle> {
    constructor(@Inject(DRIZZLE_PROVIDER) db: NodePgDatabase<typeof schema>) {
        super(db, schema.imports);
    }

    async findById(id: string): Promise<ImportDrizzle | null> {
        const result = await this.db
            .select({
                id: schema.imports.id,
                data: schema.imports.data,
                fileUrls: schema.imports.fileUrls,
                createdByUserId: schema.imports.createdByUserId,
                status: schema.imports.status,
                success: schema.imports.success,
                createdAt: schema.imports.createdAt,
            })
            .from(schema.imports)
            .where(eq(schema.imports.id, id))
            .limit(1);

        return result.length > 0 ? result[0] : null;
    }

    async createOne(input: ImportCreateDto): Promise<ImportDrizzle> {
        const result = await this.db
            .insert(schema.imports)
            .values({
                id: uuidv4(),
                data: input.data,
                fileUrls: input.fileUrls,
                createdByUserId: input.createdByUserId,
                status: input.status,
                success: input.success,
            })
            .returning();

        return result[0];
    }
}
