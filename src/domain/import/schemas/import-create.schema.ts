import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { ImportStatusEnum } from '../enums/import-status.enum';

export const ImportCreateSchema = extendApi(
    z.object({
        data: z.record(z.any()),
        fileUrls: z.string().array().nullable(),
        createdByUserId: z.string().nullable(),
        status: z.nativeEnum(ImportStatusEnum),
        success: z.boolean(),
    }),
);
