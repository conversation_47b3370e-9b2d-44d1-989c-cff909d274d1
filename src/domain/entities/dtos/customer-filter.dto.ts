import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { BaseFilterSchema } from '../../common/pagination/pagination.schema';

export const CUSTOMER_SORTABLE_FIELDS = ['name', 'address', 'city', 'zip', 'country'];
export const CustomerFilterSchema = extendApi(
    BaseFilterSchema.extend({
        search: z.string().optional().describe('Filter customers by name'),
        sortBy: extendApi(
            z
                .union([
                    z.literal('name'),
                    z.literal('address'),
                    z.literal('city'),
                    z.literal('zip'),
                    z.literal('country'),
                ])
                .optional(),
            {
                description: 'Field to sort by',
                type: 'string',
                enum: CUSTOMER_SORTABLE_FIELDS,
                default: 'name',
            },
        ),
    }),
    {
        title: 'CustomerFilter',
        description: 'Filter parameters for customers',
    },
);

export class CustomerFilterDto extends createZodDto(CustomerFilterSchema) {}
