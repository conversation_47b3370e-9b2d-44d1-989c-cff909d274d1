import { Controller, Get, Query, Res } from '@nestjs/common';
import { ApiBadRequestResponse, ApiOkResponse } from '@nestjs/swagger';
import { Response } from 'express';

import { RequestContext } from '../../../auth/context.decorator';
import { RequireResourceAction } from '../../../rbac/permissions.decorator';
import { BaseController } from '../../common/controllers/base.controller';
import { Context } from '../../common/interfaces';
import { ErrorModel } from '../../common/models/error.model';
import { Pagination } from '../../common/pagination/pagination.decorator';
import { PaginationParamsModel } from '../../common/pagination/pagination.model';
import { ActionType, ResourceType } from '../../users/entities/permission.entity';
import { CustomerFilterDto } from '../dtos/customer-filter.dto';
import { EntitiesService } from '../entities.service';
import { EntityListModel } from '../models/entity-list.model';

@Controller('customers')
export class CustomersController extends BaseController {
    constructor(private readonly entitiesService: EntitiesService) {
        super();
    }

    @RequireResourceAction({ resource: ResourceType.ENTITY, action: ActionType.READ })
    @Get()
    @ApiOkResponse({
        description: 'Customers list',
        type: EntityListModel,
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async getCustomers(
        @Res() res: Response,
        @Pagination() paginationParams: PaginationParamsModel,
        @RequestContext() context: Context,
        @Query() filter: CustomerFilterDto,
    ) {
        const model = await this.entitiesService.getCustomers(
            context.resellerIds,
            context.endUserIds,
            paginationParams,
            filter,
        );
        return this.sendOkWithPagination(res, model.getData().data, model.meta, true);
    }
}
