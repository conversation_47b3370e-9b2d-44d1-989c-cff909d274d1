import { Controller, Get, Param, Query, Res } from '@nestjs/common';
import { ApiBadRequestResponse, ApiNotFoundResponse, ApiOkResponse, ApiOperation } from '@nestjs/swagger';
import { Response } from 'express';

import { RequestContext } from '../../../auth/context.decorator';
import { RequireResourceAction } from '../../../rbac/permissions.decorator';
import { BaseController } from '../../common/controllers/base.controller';
import { Context } from '../../common/interfaces';
import { AssetListModel } from '../../common/models/asset-list.model';
import { ErrorModel } from '../../common/models/error.model';
import { ExportFormatModel } from '../../common/models/export-format.model';
import { Pagination } from '../../common/pagination/pagination.decorator';
import { PaginationParamsModel } from '../../common/pagination/pagination.model';
import { ExportFormat } from '../../common/services/export.service';
import { ContactListModel } from '../../contacts/models/contact-list.model';
import { ContractsService } from '../../contracts/contracts.service';
import { ContractFilterModel } from '../../contracts/models/contract-filter.model';
import { ContractListModel } from '../../contracts/models/contract-list.model';
import { QuoteFilterModel, QuoteListModel } from '../../quotes/models';
import { QuotesService } from '../../quotes/services/quotes.service';
import { ActionType, ResourceType } from '../../users/entities/permission.entity';
import { EntitiesService } from '../entities.service';
import { EntityModel } from '../models/entity.model';
import { EntityAssetsFilterModel } from '../models/entity-assets-filter.model';
import { EntityDistinctNamesFilterModel } from '../models/entity-distinct-names-filter.model';
import { EntityFilterModel } from '../models/entity-filter.model';
import { EntityIdParamModel } from '../models/entity-id-param.model';
import { EntityListModel } from '../models/entity-list.model';

@Controller('entities')
export class EntitiesController extends BaseController {
    constructor(
        private readonly quotesService: QuotesService,
        private readonly contractsService: ContractsService,
        private readonly entitiesService: EntitiesService,
    ) {
        super();
    }

    @RequireResourceAction({ resource: ResourceType.ENTITY, action: ActionType.READ })
    @Get()
    @ApiOkResponse({
        description: 'Entities list',
        type: EntityListModel,
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async getEntities(
        @Res() res: Response,
        @Pagination() paginationParams: PaginationParamsModel,
        @Query() filter: EntityFilterModel,
    ) {
        const model = await this.entitiesService.getEntities(paginationParams, filter);
        return this.sendOk(res, model.getData());
    }

    @RequireResourceAction({ resource: ResourceType.ENTITY, action: ActionType.READ })
    @Get(':entityId')
    @ApiOkResponse({
        description: 'The entity object',
        type: EntityModel,
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    @ApiNotFoundResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async getEntityById(@RequestContext() context: Context, @Res() res: Response, @Param() params: EntityIdParamModel) {
        // This endpoint shows only customer (end user) entity
        const model = await this.entitiesService.getCustomerById(
            context.resellerIds,
            context.endUserIds,
            params.entityId,
        );
        return this.sendOk(res, model.getData());
    }

    @RequireResourceAction({ resource: ResourceType.ENTITY, action: ActionType.READ })
    @Get(':entityId/assets')
    @ApiOkResponse({
        description: 'Assets list',
        type: AssetListModel,
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    @ApiNotFoundResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async getAssetsByEntityId(
        @Res() res: Response,
        @Param() params: EntityIdParamModel,
        @Pagination() paginationParams: PaginationParamsModel,
        @Query() filter: EntityAssetsFilterModel,
        @RequestContext() context: Context,
    ) {
        const model = await this.entitiesService.getAssetsByEntityId(
            context.resellerIds,
            context.endUserIds,
            params.entityId,
            paginationParams,
            filter,
        );
        return this.sendOkWithPagination(res, model.getData(), model.getMeta(), true);
    }

    @RequireResourceAction({ resource: ResourceType.ENTITY, action: ActionType.READ })
    @Get(':entityId/contracts')
    @ApiOkResponse({
        description: 'Contracts list',
        type: ContractListModel,
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    @ApiNotFoundResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async getContractsByEntityId(
        @Res() res: Response,
        @Param() params: EntityIdParamModel,
        @Pagination() paginationParams: PaginationParamsModel,
        @RequestContext() context: Context,
        @Query() query: ContractFilterModel,
    ) {
        const model = await this.contractsService.getContractsByResellerAndEndUserId(
            context.resellerIds,
            context.endUserIds,
            params.entityId, // EndUser/Customer
            paginationParams,
            query,
        );

        return this.sendOk(res, model.getData());
    }

    @RequireResourceAction({ resource: ResourceType.ENTITY, action: ActionType.READ })
    @Get(':entityId/quotes')
    @ApiOkResponse({
        description: 'Quotes list',
        type: QuoteListModel,
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    @ApiNotFoundResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async getQuotesByEntityId(
        @Res() res: Response,
        @Param() params: EntityIdParamModel,
        @Pagination() paginationParams: PaginationParamsModel,
        @RequestContext() context: Context,
        @Query() query: QuoteFilterModel,
    ) {
        const model = await this.quotesService.getQuotesByResellerAndEndUserId(
            context.resellerIds,
            context.endUserIds,
            params.entityId, // EndUser/Customer
            paginationParams,
            query,
        );

        return this.sendOk(res, model.getData());
    }

    @RequireResourceAction({ resource: ResourceType.CONTACT, action: ActionType.READ })
    @Get(':entityId/contacts')
    @ApiOkResponse({
        description: 'Contacts list',
        type: ContactListModel,
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    @ApiNotFoundResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async getContactsByEntityId(@Res() res: Response, @Param() params: EntityIdParamModel) {
        const model = await this.entitiesService.getContactsByEntityId(params.entityId);
        return this.sendOk(res, model.getData());
    }

    @RequireResourceAction({ resource: ResourceType.ENTITY, action: ActionType.READ })
    @Get(':entityId/assets/export')
    @ApiOperation({ summary: 'Export assets by entity ID as CSV or XLSX' })
    @ApiOkResponse({
        description: 'Assets exported successfully',
        content: {
            'text/csv': {
                schema: {
                    type: 'string',
                    format: 'binary',
                },
            },
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': {
                schema: {
                    type: 'string',
                    format: 'binary',
                },
            },
        },
    })
    @ApiBadRequestResponse({
        description: 'Error',
        type: ErrorModel,
    })
    async exportAssetsByEntityId(
        @Res() res: Response,
        @Param() params: EntityIdParamModel,
        @Query() filter: EntityAssetsFilterModel,
        @Query() exportFormat: ExportFormatModel,
        @RequestContext() context: Context,
    ) {
        const format = exportFormat.format as ExportFormat;
        const result = await this.entitiesService.exportAssetsByEntityId(
            context.resellerIds,
            context.endUserIds,
            params.entityId,
            format,
            filter,
        );

        const contentType =
            format === ExportFormat.CSV
                ? 'text/csv'
                : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';

        return this.sendFile(res, result.data, result.filename, contentType);
    }

    @Get('/distinct/names')
    @ApiOkResponse({
        description: 'Entity names',
        type: String,
        isArray: true,
    })
    @ApiOperation({ summary: 'Return distinct entity names' })
    async getDistinctVendorNames(
        @Res() res: Response,
        @Query() query: EntityDistinctNamesFilterModel,
        @RequestContext() context: Context,
    ) {
        const result = await this.entitiesService.getDistinctVendorNames(
            query,
            context.resellerIds,
            context.endUserIds,
        );

        return this.sendOk(res, { data: result });
    }
}
