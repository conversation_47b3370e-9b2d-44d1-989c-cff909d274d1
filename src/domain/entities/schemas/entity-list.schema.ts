import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { createPaginatedResponseSchema } from '../../common/pagination/pagination.schema';
import { EntityListItemSchema } from './entity-list-item.schema';

export const EntityListItemsSchema = extendApi(z.array(EntityListItemSchema), {
    title: 'EntityListItems',
    description: 'Entity list items',
});

export const EntityListSchema = createPaginatedResponseSchema(EntityListItemSchema);
