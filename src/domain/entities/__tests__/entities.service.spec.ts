import { faker } from '@faker-js/faker';
import { NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { I18nContext } from 'nestjs-i18n';

import { ExportServiceMock } from '../../../../test/mocks/export.service.mock';
import { AssetListModel } from '../../common/models';
import { ExportFormat, ExportService } from '../../common/services';
import { ContactDrizzleRepository } from '../../contacts';
import { ContactListPresenter } from '../../contacts/presenters';
import { ContractDrizzleRepository } from '../../contracts/repositories/contract.drizzle.repository';
import { QuoteDrizzleRepository } from '../../quotes/repositories/quote.drizzle.repository';
import { EntitiesService } from '../entities.service';
import { EntityModel } from '../models/entity.model';
import { EntityListModel } from '../models/entity-list.model';
import { EntityListItemModel } from '../models/entity-list-item.model';
import { EntityPresenter } from '../presenters/entity-presenter';
import { AssetViewDrizzleRepository } from '../repositories/asset-view.drizzle.repository';
import { EntityDrizzleRepository } from '../repositories/entity.drizzle.repository';

class EntitiesServiceMockDeps {
    readonly entityDrizzleRepository = {
        findById: jest.fn(),
        findCustomer: jest.fn(),
        findEntitiesWithFilter: jest.fn(),
        globalSearch: jest.fn(),
        findCustomers: jest.fn(),
        checkIfEntityExistsByEntityId: jest.fn(),
        findByIds: jest.fn(),
    };

    readonly quoteDrizzleRepository = {
        findQuoteByResellerIdAndQuoteId: jest.fn(),
    };

    readonly assetViewDrizzleRepository = {
        findAssetsByEntityId: jest.fn(),
    };

    readonly contactDrizzleRepository = {
        findContactByIds: jest.fn(),
    };

    readonly contractDrizzleRepository = {
        findContractById: jest.fn(),
    };

    readonly contactListPresenter = {
        toModel: jest.fn(),
    };

    readonly entityPresenter = {
        toListItemModel: jest.fn(),
        toModel: jest.fn(),
        toAssetModel: jest.fn(),
    };
}

describe('Entities Service', () => {
    let service: EntitiesService;
    let deps: EntitiesServiceMockDeps;
    let entityDrizzleRepository: EntityDrizzleRepository;
    let presenter: EntityPresenter;
    let exportService: ExportServiceMock;

    beforeEach(async () => {
        jest.clearAllMocks();

        jest.spyOn(I18nContext, 'current').mockReturnValue({
            t: (key: string) => key,
        } as any);
        deps = new EntitiesServiceMockDeps();
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                EntitiesService,
                ContactListPresenter,
                EntityPresenter,
                { provide: ExportService, useClass: ExportServiceMock },
                { provide: EntityDrizzleRepository, useValue: deps.entityDrizzleRepository },
                { provide: QuoteDrizzleRepository, useValue: deps.quoteDrizzleRepository },
                { provide: AssetViewDrizzleRepository, useValue: deps.assetViewDrizzleRepository },
                { provide: ContactDrizzleRepository, useValue: deps.contactDrizzleRepository },
                { provide: ContractDrizzleRepository, useValue: deps.contractDrizzleRepository },
                { provide: EntityPresenter, useValue: deps.entityPresenter },
                { provide: ContactListPresenter, useValue: deps.contactListPresenter },
            ],
        }).compile();

        service = module.get(EntitiesService);
        entityDrizzleRepository = module.get(EntityDrizzleRepository);
        presenter = module.get(EntityPresenter);
        exportService = module.get(ExportService);
    });

    describe('getEntityById', () => {
        it('should return entity if found', async () => {
            const id = faker.string.uuid();
            const mockEntity = { id };
            deps.entityDrizzleRepository.findById.mockResolvedValue(mockEntity);

            const result = await service.getEntityById(id);

            expect(result).toBe(mockEntity);
            expect(entityDrizzleRepository.findById).toBeCalledWith(id);
        });

        it('should throw NotFoundException if entity not found and skipNotFound is false', async () => {
            deps.entityDrizzleRepository.findById.mockResolvedValue(null);

            await expect(service.getEntityById(faker.string.uuid())).rejects.toThrow(NotFoundException);
        });

        it('should return null if entity not found and skipNotFound is true', async () => {
            deps.entityDrizzleRepository.findById.mockResolvedValue(null);

            const result = await service.getEntityById(faker.string.uuid(), true);
            expect(result).toBeNull();
        });
    });

    describe('getCustomerById', () => {
        it('should return customer if found', async () => {
            const entityId = faker.string.uuid();
            const entity = { id: entityId };
            const model: EntityModel = new EntityModel();
            model.id = entity.id;

            deps.entityDrizzleRepository.findCustomer.mockResolvedValue(entity);
            jest.spyOn(presenter, 'toModel').mockReturnValue(model);

            const resellerId = faker.string.uuid();
            const result = await service.getCustomerById([resellerId], null, entityId);

            expect(result).toEqual(model);
            expect(deps.entityDrizzleRepository.findCustomer).toHaveBeenNthCalledWith(1, [resellerId], null, entityId);
            expect(presenter.toModel).toHaveBeenNthCalledWith(1, entity);
        });

        it('should throw NotFoundException if customer not found', async () => {
            deps.entityDrizzleRepository.findCustomer.mockResolvedValue(null);
            await expect(service.getCustomerById(['reseller1'], null, 'non-existent-id')).rejects.toThrow(
                NotFoundException,
            );
        });
    });

    describe('getEntities', () => {
        it('should return list of entities with metadata', async () => {
            const fakeId = faker.string.uuid();
            const fakePagination = { limit: faker.number.int(), offset: 0 };
            const mockEntities = [{ id: fakeId, name: faker.string.alpha() }];
            const mockListItem = new EntityListItemModel();
            mockListItem.id = fakeId;

            deps.entityDrizzleRepository.findEntitiesWithFilter.mockResolvedValue({
                data: mockEntities,
                meta: { count: mockEntities.length, hasMore: false },
            });
            deps.entityPresenter.toListItemModel.mockReturnValue(mockListItem);

            const result = await service.getEntities(fakePagination);

            expect(result).toBeInstanceOf(EntityListModel);
            expect(result.data).toHaveLength(1);
            expect(deps.entityDrizzleRepository.findEntitiesWithFilter).toBeCalledWith(fakePagination, undefined);
            expect(deps.entityPresenter.toListItemModel).toBeCalledWith(mockEntities[0]);
        });
    });

    describe('globalSearch', () => {
        it('should return entities fields if found', async () => {
            const fakeResult = [
                {
                    id: faker.string.uuid(),
                    name: faker.string.alpha(),
                    city: faker.location.city(),
                    address1: faker.location.streetAddress(),
                    address2: faker.location.secondaryAddress(),
                    zip: faker.location.zipCode(),
                },
            ];

            deps.entityDrizzleRepository.globalSearch.mockResolvedValue(fakeResult);
            const resellerIds = [faker.string.uuid()];
            const searchPhrase = faker.string.alpha();

            const result = await service.globalSearch(resellerIds, searchPhrase);

            expect(result).toEqual(fakeResult);
            expect(deps.entityDrizzleRepository.globalSearch).toHaveBeenNthCalledWith(1, resellerIds, searchPhrase);
        });
    });

    describe('getCustomers', () => {
        it('should return customers transformed to list model', async () => {
            const resellerIds = [faker.string.uuid()];
            const endUserIds = [faker.string.uuid()];
            const paginationParams = { limit: 10 } as any;
            const filter = { name: faker.company.name() } as any;
            const mockEntities = [{ id: faker.string.uuid() }, { id: faker.string.uuid() }];
            const mockMeta = { totalCount: mockEntities.length, hasNextPage: false, nextCursor: null };
            const mockListItems = mockEntities.map((e) => ({ id: e.id, name: faker.company.name() }));

            deps.entityDrizzleRepository.findCustomers.mockResolvedValue({ data: mockEntities, meta: mockMeta });
            deps.entityPresenter.toListItemModel.mockImplementation((entity) =>
                mockListItems.find((item) => item.id === entity.id),
            );

            const result = await service.getCustomers(resellerIds, endUserIds, paginationParams, filter);

            expect(deps.entityDrizzleRepository.findCustomers).toBeCalledWith(
                resellerIds,
                endUserIds,
                paginationParams,
                filter,
            );
            expect(result.data).toEqual(mockListItems);
            expect(result.meta).toEqual(mockMeta);
        });

        it('should handle empty data gracefully', async () => {
            deps.entityDrizzleRepository.findCustomers.mockResolvedValue({ data: [], meta: { totalCount: 0 } });
            deps.entityPresenter.toListItemModel.mockImplementation((entity) => entity);

            const result = await service.getCustomers([faker.string.uuid()], null, { limit: 10 } as any);

            expect(result.data).toEqual([]);
            expect(result.meta.totalCount).toBe(0);
        });
    });

    describe('getContactsByEntityId', () => {
        const entityId = faker.string.uuid();

        it('should return contacts when entity and primary contact exist', async () => {
            const primaryContactId = faker.string.uuid();
            const mockEntity = { id: entityId, data: { PrimaryContactId: primaryContactId } };
            const mockContacts = [{ id: primaryContactId }];
            const mockContactModel = { contacts: [mockContacts[0]] };

            deps.entityDrizzleRepository.findById.mockResolvedValue(mockEntity);
            deps.contactDrizzleRepository.findContactByIds.mockResolvedValue(mockContacts);
            deps.contactListPresenter.toModel = jest.fn().mockReturnValue(mockContactModel);

            const result = await service.getContactsByEntityId(entityId);

            expect(result).toBe(mockContactModel);
            expect(deps.entityDrizzleRepository.findById).toBeCalledWith(entityId);
            expect(deps.contactDrizzleRepository.findContactByIds).toBeCalledWith([primaryContactId]);
            expect(deps.contactListPresenter.toModel).toBeCalledWith(mockContacts);
        });

        it('should throw NotFoundException if entity is not found', async () => {
            deps.entityDrizzleRepository.findById.mockResolvedValue(null);

            await expect(service.getContactsByEntityId(entityId)).rejects.toThrow(NotFoundException);
            expect(deps.entityDrizzleRepository.findById).toBeCalledWith(entityId);
        });

        it('should throw NotFoundException if primary contact ID is missing', async () => {
            const mockEntity = { id: entityId, data: {} };
            deps.entityDrizzleRepository.findById.mockResolvedValue(mockEntity);

            await expect(service.getContactsByEntityId(entityId)).rejects.toThrow(NotFoundException);
            expect(deps.entityDrizzleRepository.findById).toBeCalledWith(entityId);
        });
    });

    describe('getAssetsByEntityId', () => {
        const resellerIds = [faker.string.uuid()];
        const endUserIds = [faker.string.uuid()];
        const entityId = faker.string.uuid();
        const paginationParams = { limit: faker.number.int(), cursor: null };
        const filter = { search: faker.string.alpha() };

        it('should return paginated assets when result is paginated', async () => {
            const assetEntities = [{ id: faker.string.uuid() }, { id: faker.string.uuid() }];
            const meta = { totalCount: assetEntities.length, hasNextPage: false, nextCursor: null };
            const assetModels = [{ name: faker.string.alpha() }, { name: faker.string.alpha() }];

            deps.entityDrizzleRepository.checkIfEntityExistsByEntityId.mockReturnValue(true);
            deps.assetViewDrizzleRepository.findAssetsByEntityId.mockResolvedValue({ data: assetEntities, meta });
            deps.entityPresenter.toAssetModel = jest
                .fn()
                .mockImplementation((entity) => assetModels.find((a) => a.name === entity.id));

            const result = await service.getAssetsByEntityId(
                resellerIds,
                endUserIds,
                entityId,
                paginationParams,
                filter,
            );
            jest.spyOn(result, 'getData').mockReturnValue(assetModels);

            expect(result.getData()).toEqual(assetModels);
            expect(result.getMeta()).toEqual(meta);
        });

        it('should return non-paginated asset list when result is array', async () => {
            const assetEntities = [{ id: faker.string.uuid() }];
            const assetModels = [{ name: faker.string.uuid() }];

            deps.entityDrizzleRepository.checkIfEntityExistsByEntityId.mockReturnValue(true);
            deps.assetViewDrizzleRepository.findAssetsByEntityId.mockResolvedValue(assetEntities);
            deps.entityPresenter.toAssetModel = jest
                .fn()
                .mockImplementation((entity) => assetModels.find((a) => a.name === entity.id));

            const result = await service.getAssetsByEntityId(resellerIds, endUserIds, entityId);
            jest.spyOn(result, 'getData').mockReturnValue(assetModels);

            expect(result.getData()).toEqual(assetModels);
            expect(result.getMeta()).toBeUndefined();
        });

        it('should return empty AssetListModel if result is null', async () => {
            deps.entityDrizzleRepository.checkIfEntityExistsByEntityId.mockReturnValue(true);
            deps.assetViewDrizzleRepository.findAssetsByEntityId.mockResolvedValue(null);

            const result = await service.getAssetsByEntityId(resellerIds, endUserIds, entityId);
            jest.spyOn(result, 'getData').mockReturnValue([]);

            expect(result).toBeInstanceOf(AssetListModel);
            expect(result.getData()).toEqual([]);
        });

        it('should throw NotFoundException if entity does not exist', async () => {
            deps.entityDrizzleRepository.checkIfEntityExistsByEntityId.mockReturnValue(false);

            await expect(service.getAssetsByEntityId(resellerIds, endUserIds, entityId)).rejects.toThrow(
                NotFoundException,
            );
        });
    });

    describe('exportAssetsByEntityId', () => {
        const resellerIds = [faker.string.uuid()];
        const endUserIds = [faker.string.uuid()];
        const entityId = faker.string.uuid();
        const format: ExportFormat = ExportFormat.CSV;
        const filter = { search: faker.string.alpha() };

        const mockAssets = [
            {
                name: faker.string.alpha(),
                serialNumber: faker.string.alpha(),
                productSku: faker.string.alpha(),
                itemsCount: faker.number.int(),
                serviceGroupSku: faker.string.alpha(),
                serviceLevelSku: faker.string.alpha(),
                resellerPriceFinalSum: faker.number.int(),
                endCustomerPriceFinalSum: faker.number.float(),
            },
        ];

        it('should export asset data when assets exist', async () => {
            const mockExportResult = { data: Buffer.from('export'), filename: 'mock.csv' };
            const mockAssetList = { getData: () => mockAssets };

            service.getAssetsByEntityId = jest.fn().mockResolvedValue(mockAssetList);
            exportService.setExportDataResult(mockExportResult);

            const result = await service.exportAssetsByEntityId(resellerIds, endUserIds, entityId, format, filter);

            expect(service.getAssetsByEntityId).toBeCalledWith(resellerIds, endUserIds, entityId, undefined, filter);

            expect(exportService.exportData).toBeCalledWith(
                mockAssets.map((asset) => ({
                    Name: asset.name,
                    SerialNumber: asset.serialNumber || '',
                    ProductSku: asset.productSku || '',
                    ItemsCount: asset.itemsCount || 0,
                    ServiceGroupSku: asset.serviceGroupSku,
                    ServiceLevelSku: asset.serviceLevelSku,
                    RetailPriceSum: asset.resellerPriceFinalSum,
                    EndCustomerPriceSum: asset.endCustomerPriceFinalSum,
                })),
                format,
                expect.stringContaining(`assets-entity-${entityId}-`),
            );

            expect(result).toBe(mockExportResult);
        });

        it('should throw if no assets found', async () => {
            const mockAssetList = { getData: () => [] };
            service.getAssetsByEntityId = jest.fn().mockResolvedValue(mockAssetList);

            await expect(
                service.exportAssetsByEntityId(resellerIds, endUserIds, entityId, format, filter),
            ).rejects.toThrow(Error);
        });

        it('should throw if getData returns null', async () => {
            const mockAssetList = { getData: () => null };
            service.getAssetsByEntityId = jest.fn().mockResolvedValue(mockAssetList);

            await expect(
                service.exportAssetsByEntityId(resellerIds, endUserIds, entityId, format, filter),
            ).rejects.toThrow(Error);
        });
    });

    describe('findByContractId', () => {
        const resellerIds = [faker.string.uuid()];
        const endUserIds = [faker.string.uuid()];
        const contractId = faker.string.uuid();

        it('should return entity list model when contract exists', async () => {
            const mockContract = {
                vendorId: faker.string.uuid(),
                distributorId: faker.string.uuid(),
                resellerId: faker.string.uuid(),
                endUserId: faker.string.uuid(),
            };

            const mockEntity = { id: faker.string.uuid(), name: 'Test Entity', type: 'company', owner_id: 'owner-id' };
            const expectedModel = new EntityListModel();
            expectedModel.setData([deps.entityPresenter.toListItemModel(mockEntity)], {
                totalCount: 1,
                hasNextPage: false,
                nextCursor: null,
            });

            deps.contractDrizzleRepository.findContractById.mockResolvedValue(mockContract);
            deps.entityDrizzleRepository.findByIds.mockResolvedValue([mockEntity]);

            const result = await service.findByContractId(resellerIds, endUserIds, contractId);

            expect(result).toEqual(expectedModel);
        });

        it('should throw NotFoundException when contract not found', async () => {
            deps.contractDrizzleRepository.findContractById.mockResolvedValue(null);

            await expect(service.findByContractId(resellerIds, endUserIds, contractId)).rejects.toThrow(
                NotFoundException,
            );
        });
    });

    describe('findByResellerIdAndQuoteId', () => {
        it('should return entity list model when quote exists', async () => {
            const mockQuote = {
                vendorId: faker.string.uuid(),
                distributorId: faker.string.uuid(),
                resellerId: faker.string.uuid(),
                endUserId: faker.string.uuid(),
            };

            const mockEntity = {
                id: faker.string.uuid(),
                name: faker.string.alpha(),
                type: faker.string.alpha(),
                owner_id: faker.string.alpha(),
            };

            const expectedItem = deps.entityPresenter.toListItemModel(mockEntity);
            const expectedModel = new EntityListModel();
            expectedModel.setData([expectedItem], {
                totalCount: 1,
                hasNextPage: false,
                nextCursor: null,
            });

            deps.quoteDrizzleRepository.findQuoteByResellerIdAndQuoteId.mockResolvedValue(mockQuote);
            deps.entityDrizzleRepository.findByIds.mockResolvedValue([mockEntity]);

            const result = await service.findByResellerIdAndQuoteId([], [], faker.string.uuid());

            expect(result).toEqual(expectedModel);
        });

        it('should throw NotFoundException if quote is not found', async () => {
            deps.quoteDrizzleRepository.findQuoteByResellerIdAndQuoteId.mockResolvedValue(null);

            await expect(service.findByResellerIdAndQuoteId([], [], faker.string.uuid())).rejects.toThrow(
                NotFoundException,
            );
        });
    });
});
