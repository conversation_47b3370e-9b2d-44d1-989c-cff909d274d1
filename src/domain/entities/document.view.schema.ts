import { sql } from 'drizzle-orm';
import { pgView, text } from 'drizzle-orm/pg-core';

export const vDocuments = pgView('v_documents', {
    id: text(),
    type: text(),
    documentNo: text('document_no'),
    status: text(),
    vendorId: text('vendor_id'),
    distributorId: text('distributor_id'),
    resellerId: text('reseller_id'),
    endUserId: text('end_user_id'),
}).as(
    sql`SELECT contracts.id, 'contract'::text AS type, contracts.contract_no AS document_no, contracts.status, contracts.vendor_id, contracts.distributor_id, contracts.reseller_id, contracts.end_user_id FROM contracts UNION SELECT quotes.id, 'quote'::text AS type, quotes.quote_no AS document_no, quotes.status, quotes.vendor_id, quotes.distributor_id, quotes.reseller_id, quotes.end_user_id FROM quotes`,
);
