import { Injectable } from '@nestjs/common';

import { CurrencyEnum, HttpMethodsEnum } from '../../common/enums';
import { AssetModel } from '../../common/models/asset.model';
import { AssetType, ContractItemType } from '../../common/types';
import { ContractItemApiModel } from '../../contracts/models/contract-item-api.model';
import { EntityEntity } from '../entities/entity.entity';
import { EntityModel } from '../models/entity.model';
import { EntityListItemModel } from '../models/entity-list-item.model';

@Injectable()
export class EntityPresenter {
    toModel(entity: EntityEntity): EntityModel {
        const model = new EntityModel();
        model.id = entity.id;
        model.name = entity.name;
        model.type = entity.type;
        model.addressShort = 'N/A';
        model.logoUrl = 'N/A';

        model._links = {};
        model._links.self = { uri: `/entities/${entity.id}` };
        model._links.contracts = { uri: `/entities/${entity.id}/contracts` };
        model._links.quotes = { uri: `/entities/${entity.id}/quotes` };
        model._links.assets = { uri: `/entities/${entity.id}/assets` };

        return model;
    }

    toListItemModel(entity: EntityEntity): EntityListItemModel {
        const model = new EntityListItemModel();
        model.id = entity.id;
        model.name = entity.name;
        model.type = entity.type;
        model.addressShort = 'N/A';
        model.logoUrl = 'N/A';
        // @ts-expect-error incorrect type of MikroORM
        model.ownerId = entity?.owner_id ?? entity.ownerId;

        if (entity.addresses) {
            model.businessAddress = {
                // @ts-expect-error expected
                address: entity.addresses.address,
                // @ts-expect-error expected
                zip: entity.addresses.zip,
                // @ts-expect-error expected
                city: entity.addresses.city,
                // @ts-expect-error expected
                country: entity.addresses.country,
            };
        }

        // Links
        model._links = {};

        model._links.self = {
            uri: `/entities/${entity.id}`,
        };

        return model;
    }

    toAssetItemModel(entity: ContractItemType, currency: CurrencyEnum): ContractItemApiModel {
        const model = new ContractItemApiModel();
        model.id = entity.id;
        model.itemNo = entity.item_no;
        model.contractId = entity.contract_id;
        model.vendorId = entity.vendor_id;
        model.serialNumber = entity.serial_no;
        model.productSku = entity.product_sku;
        model.serviceLevelSku = entity.service_level_sku;
        model.serviceGroupSku = entity.service_group_sku;
        model.serviceGroupLabel = entity.service_group_label;
        model.serviceName = entity.service_name;
        model.coverageStatus = entity.coverage_status;
        model.resellerPrice = entity.reseller_price;
        model.endCustomerPrice = entity.end_customer_price;
        model.currency = currency;

        // Links
        model._links = {};

        model._links.self = {
            uri: `/contracts/${entity.contract_id}/assets/${entity.id}`,
        };

        model._links.vendor = {
            name: 'N/A',
            uri: `/entities/${entity.vendor_id}`,
        };

        // Actions
        model._actions = {};

        model._actions.edit = {
            method: HttpMethodsEnum.PATCH,
            uri: `/contracts/${entity.contract_id}/assets`,
        };

        return model;
    }

    toAssetModel(entity: AssetType): AssetModel {
        const model = new AssetModel();
        model.name = entity.name;
        model.serialNumber = entity.serial_number;
        model.coverageStatus = entity.coverage_status;
        model.serviceGroupSku = entity.service_group_sku;
        model.serviceGroupLabel = entity.service_group_label;
        model.serviceLevelSku = entity.service_sku;
        model.productSku = entity.product_sku;
        model.itemsCount = Number(entity.items_count);
        model.resellerPriceFinalSum = entity.reseller_price_final_sum;
        // model.distributorPriceFinalSum = entity.distributor_price_final_sum;
        model.endCustomerPriceFinalSum = entity.end_customer_price_final_sum;
        model.items = entity.items.map((item) => this.toAssetItemModel(item, entity.currency));
        model.currency = entity.currency;
        model.startDate = this.adaptDate(entity.start_date);
        model.endDate = this.adaptDate(entity.start_date);
        model.supportLifeEndDate = this.adaptDate(entity.support_life_end_date);

        model._links = {};

        const vendorId = entity.items[0].vendor_id;
        const serialNo = entity.serial_number;
        const id = entity.items[0].id;
        const assetId = serialNo?.length > 0 ? serialNo : id;

        model._links.self = {
            uri: `/assets/${assetId}`,
        };
        model._links.vendor = {
            name: 'N/A',
            uri: `/entities/${vendorId}`,
        };

        return model;
    }

    private adaptDate(date: string | Date | null | undefined): string | null {
        if (!date) {
            return null;
        }

        if (date instanceof Date) {
            return date.toISOString();
        }

        const dateObj = new Date(date);
        return dateObj.toISOString();
    }
}
