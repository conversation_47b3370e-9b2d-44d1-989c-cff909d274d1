import { Inject, Injectable } from '@nestjs/common';
import { and, asc, desc, eq, gt, ilike, inArray, isNotNull, or, sql, SQLWrapper } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

import * as schema from '../../../../drizzle/schema';
import { DRIZZLE_PROVIDER } from '../../../drizzle/drizzle.provider';
import {
    PaginatedResponseModel,
    PaginationMetaModel,
    PaginationParamsModel,
} from '../../common/pagination/pagination.model';
import { BaseDrizzleRepository } from '../../common/repositories/base.drizzle.repository';
import { AssetType } from '../../common/types';
import { AssetViewContractItemSelect } from '../../contracts/consts/asset-view-contract-item.select';
import { AssetViewQuoteItemSelect } from '../../quotes/consts/asset-view-quote-item-select';
import { EntityEntity } from '../entities/entity.entity';
import { EntityAssetsFilterModel } from '../models/entity-assets-filter.model';

@Injectable()
export class AssetViewDrizzleRepository extends BaseDrizzleRepository<EntityEntity> {
    constructor(
        @Inject(DRIZZLE_PROVIDER)
        db: NodePgDatabase<typeof schema>,
    ) {
        super(db, schema.vAssets);
    }

    async findAssetsByEntityId(
        resellerIds: string[],
        endUserIds: string[] | null,
        entityId: string,
        paginationParams?: PaginationParamsModel,
        filter?: EntityAssetsFilterModel,
    ): Promise<PaginatedResponseModel<AssetType> | AssetType[]> {
        const sortBy = filter?.sortBy ?? 'name';
        const sortOrder = filter?.sortOrder || 'asc';

        const whereConditions: SQLWrapper[] = [
            inArray(schema.vAssets.resellerId, resellerIds),
            ...(endUserIds !== null ? [inArray(schema.vAssets.endUserId, endUserIds)] : []),
            eq(schema.vAssets.endUserId, entityId),
        ];

        if (filter?.search) {
            whereConditions.push(...this.buildSearchConditions(filter.search));
        }

        if (filter?.hasEndCustomerPrice !== undefined) {
            whereConditions.push(
                and(isNotNull(schema.vAssets.endCustomerPriceFinal), gt(schema.vAssets.endCustomerPriceFinal, '0')),
            );
        }

        if (filter.hasEndOfServiceLifeDate === true) {
            whereConditions.push(isNotNull(schema.vAssets.supportLifeEndDate));
        }

        const DEFAULT_PAGINATION_LIMIT = parseInt(process.env.PAGINATION_DEFAULT_LIMIT, 10) || 50;
        const MAX_PAGINATION_LIMIT = parseInt(process.env.PAGINATION_MAX_LIMIT, 10) || 100;

        const limit = paginationParams?.limit || DEFAULT_PAGINATION_LIMIT;
        const effectiveLimit = Math.min(limit, MAX_PAGINATION_LIMIT);
        const queryLimit = effectiveLimit + 1;
        const offset = paginationParams?.cursor ? parseInt(paginationParams.cursor, 10) : 0;

        const filteredQuoteItems = this.db
            .select(AssetViewQuoteItemSelect)
            .from(schema.vQuoteItems)
            .where(
                or(
                    eq(schema.vQuoteItems.vendorId, entityId),
                    eq(schema.vQuoteItems.endUserId, entityId),
                    eq(schema.vQuoteItems.resellerId, entityId),
                    eq(schema.vQuoteItems.endUserId, entityId),
                ),
            );

        const filteredContractItems = this.db
            .select(AssetViewContractItemSelect)
            .from(schema.vContractItems)
            .where(
                or(
                    eq(schema.vContractItems.vendorId, entityId),
                    eq(schema.vContractItems.endUserId, entityId),
                    eq(schema.vContractItems.resellerId, entityId),
                    eq(schema.vContractItems.endUserId, entityId),
                ),
            );
        const unionQuery = filteredContractItems.unionAll(filteredQuoteItems as any);
        const query = this.db
            .select({
                name: schema.vAssets.productName,
                serial_number: schema.vAssets.serialNo,
                coverage_status: schema.vAssets.coverageStatus,
                service_group_sku: schema.vAssets.serviceGroupSku,
                service_group_label: schema.vAssets.serviceGroupLabel,
                product_sku: schema.vAssets.productSku,
                currency: schema.vAssets.currency,
                quantity: schema.vAssets.quantity,
                items_count: sql<number>`count(*)`,
                reseller_price_final_sum: sql<number>`sum(${schema.vAssets.resellerPriceFinal})::float`,
                distributor_price_final_sum: sql<number>`sum(${schema.vAssets.distributorPriceFinal})::float`,
                end_customer_price_final_sum: sql<number>`sum(${schema.vAssets.endCustomerPriceFinal})::float`,
                items: sql<any[]>`JSON_AGG(to_json(${schema.vAssets}))`,
                start_date: schema.vAssets.startDate,
                end_date: schema.vAssets.endDate,
                support_life_end_date: schema.vAssets.supportLifeEndDate,
            })
            .from(unionQuery.as('v_assets'))
            .where(and(...whereConditions))
            .groupBy(
                schema.vAssets.productName,
                schema.vAssets.serialNo,
                schema.vAssets.coverageStatus,
                schema.vAssets.serviceGroupSku,
                schema.vAssets.serviceGroupLabel,
                schema.vAssets.productSku,
                schema.vAssets.currency,
                schema.vAssets.quantity,
                schema.vAssets.startDate,
                schema.vAssets.endDate,
                schema.vAssets.supportLifeEndDate,
            )
            .limit(queryLimit)
            .offset(offset);

        const sortModifier = sortOrder.toLowerCase() === 'desc' ? desc : asc;
        switch (sortBy) {
            case 'name':
                query.orderBy(sortModifier(schema.vAssets.productName));
                break;
            case 'serialNumber':
            case 'serialNo':
                query.orderBy(sortModifier(schema.vAssets.serialNo));
                break;
            case 'productSku':
                query.orderBy(sortModifier(schema.vAssets.productSku));
                break;
            case 'serviceGroupSku':
                query.orderBy(sortModifier(schema.vAssets.serviceGroupSku));
                break;
            case 'resellerPriceFinalSum':
            case 'resellerPrice':
                query.orderBy(sortModifier(sql`sum(${schema.vAssets.resellerPriceFinal})::float`));
                break;
            case 'endCustomerPriceFinalSum':
            case 'endCustomerPrice':
                query.orderBy(sortModifier(sql`sum(${schema.vAssets.endCustomerPriceFinal})::float`));
                break;
            case 'quantity':
                query.orderBy(sortModifier(schema.vAssets.quantity));
                break;
            case 'startDate':
                query.orderBy(sortModifier(schema.vAssets.startDate));
                break;
            case 'endDate':
                query.orderBy(sortModifier(schema.vAssets.endDate));
                break;
            case 'supportLifeEndDate':
                query.orderBy(sortModifier(schema.vAssets.supportLifeEndDate));
                break;
            default:
                query.orderBy(sortModifier(sql`${sortBy}`));
                break;
        }

        const assets = await query;

        const hasNextPage = assets.length > effectiveLimit;
        const data = hasNextPage ? assets.slice(0, effectiveLimit) : assets;
        const nextCursor = hasNextPage
            ? paginationParams?.cursor
                ? (parseInt(paginationParams.cursor, 10) + effectiveLimit).toString()
                : effectiveLimit.toString()
            : null;

        const items = data.map((record) => this.mapToEntity(record));

        return new PaginatedResponseModel<AssetType>(items, new PaginationMetaModel(hasNextPage, nextCursor));
    }

    protected mapToEntity(record: any): AssetType {
        return {
            ...record,
            quantity: record.quantity ? Number(record.quantity) : null,
            items_count: record.items_count ? Number(record.items_count) : null,
            reseller_price_final_sum: record.reseller_price_final_sum ? Number(record.reseller_price_final_sum) : null,
            end_customer_price_final_sum: record.end_customer_price_final_sum
                ? Number(record.end_customer_price_final_sum)
                : null,
        } as AssetType;
    }

    protected buildSearchConditions(searchTerm: string): SQLWrapper[] {
        if (!searchTerm) return [];

        return [
            or(
                ilike(schema.vAssets.productName, `%${searchTerm}%`),
                ilike(schema.vAssets.serialNo, `%${searchTerm}%`),
                ilike(schema.vAssets.productSku, `%${searchTerm}%`),
            ),
        ];
    }
}
