import { Inject, Injectable } from '@nestjs/common';
import { and, count, eq, ilike, inArray, sql, SQLWrapper } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { PgSelect } from 'drizzle-orm/pg-core';

import * as schema from '../../../../drizzle/schema';
import { DRIZZLE_PROVIDER } from '../../../drizzle/drizzle.provider';
import { AddressTypeEnum } from '../../common/enums/address-type.enum';
import { PaginatedResponseModel, PaginationParamsModel } from '../../common/pagination/pagination.model';
import { BaseDrizzleRepository } from '../../common/repositories/base.drizzle.repository';
import { EntityEntity } from '../entities/entity.entity';
import { EntityTypeEnum } from '../enums/entity-type.enum';
import { EntityDistinctNamesFilterModel } from '../models/entity-distinct-names-filter.model';
import { EntityFilterModel } from '../models/entity-filter.model';

@Injectable()
export class EntityDrizzleRepository extends BaseDrizzleRepository<EntityEntity> {
    constructor(
        @Inject(DRIZZLE_PROVIDER)
        db: NodePgDatabase<typeof schema>,
    ) {
        super(db, schema.entities);
    }

    protected mapToEntity(record: any): EntityEntity {
        return {
            ...record,
        } as EntityEntity;
    }

    async checkIfEntityExistsByEntityId(
        resellerIds: string[],
        endUserIds: string[] | null,
        entityId: string,
    ): Promise<boolean> {
        const res = await this.findEntityNameByEntityId(resellerIds, endUserIds, entityId);
        return res !== null;
    }

    async findEntityNameByEntityId(
        resellerIds: string[],
        endUserIds: string[] | null,
        entityId: string,
    ): Promise<string | null> {
        const vd = this.db
            .select({
                endUserId: schema.vDocuments.endUserId,
            })
            .from(schema.vDocuments)
            .where(
                and(
                    ...[
                        inArray(schema.vDocuments.resellerId, resellerIds),
                        ...(endUserIds !== null ? [inArray(schema.vDocuments.endUserId, endUserIds)] : []),
                    ],
                ),
            )
            .groupBy(schema.vDocuments.endUserId)
            .as('vd');

        const entity = await this.db
            .select({
                name: schema.entities.name,
            })
            .from(schema.entities)
            .innerJoin(vd, eq(vd.endUserId, schema.entities.id))
            .where(and(eq(schema.entities.id, entityId), eq(schema.entities.type, EntityTypeEnum.EndUser)))
            .limit(1);

        return entity.length > 0 ? entity[0].name : null;
    }

    async findById(entityId: string): Promise<EntityEntity | null> {
        const result = await this.db.select().from(schema.entities).where(eq(schema.entities.id, entityId)).limit(1);

        return result.length > 0 ? this.mapToEntity(result[0]) : null;
    }

    async checkEntityExistsById(entityId: string): Promise<boolean> {
        const result = await this.db
            .select({ count: count() })
            .from(schema.entities)
            .where(eq(schema.entities.id, entityId))
            .limit(1);

        return result[0].count > 0;
    }

    async findByIds(entityIds: string[]): Promise<EntityEntity[]> {
        if (entityIds.length === 0) {
            return [];
        }

        const result = await this.db.select().from(schema.entities).where(inArray(schema.entities.id, entityIds));

        return result.map((record) => this.mapToEntity(record));
    }

    async findEntityNameById(entityId: string): Promise<string | null> {
        const entity = await this.db
            .select({
                name: schema.entities.name,
            })
            .from(schema.entities)
            .where(eq(schema.entities.id, entityId))
            .limit(1);

        return entity.length > 0 ? entity[0].name : null;
    }

    async findEntitiesWithFilter(
        paginationParams: PaginationParamsModel,
        filter?: EntityFilterModel,
    ): Promise<PaginatedResponseModel<EntityEntity>> {
        const whereConditions = [];

        if (filter?.search) {
            whereConditions.push(ilike(schema.entities.name, `%${filter.search}%`));
        }

        if (filter?.name) {
            whereConditions.push(ilike(schema.entities.name, `%${filter.name}%`));
        }

        if (filter?.ids && filter.ids.length > 0) {
            whereConditions.push(inArray(schema.entities.id, filter.ids));
        }

        if (filter?.type) {
            whereConditions.push(eq(schema.entities.type, filter.type));
        }

        if (filter?.ownerId) {
            whereConditions.push(eq(schema.entities.ownerId, filter.ownerId));
        }

        whereConditions.push(
            sql`${schema.addresses.data} ->> 'IsDefault'  = ${true} and ${schema.addresses.data} ->> 'AddressType' = ${AddressTypeEnum.BUSINESS}`,
        );

        filter = {
            ...filter,
            sortBy: filter?.sortBy ?? 'name',
            sortOrder: filter?.sortOrder ?? 'asc',
        };

        const modifier = (query: PgSelect) => {
            query.leftJoin(schema.addresses, eq(this.table.id, schema.addresses.entityId));
            query.groupBy(schema.entities.id, schema.addresses.id);
        };

        const result = await this.findPaginated(
            whereConditions,
            paginationParams,
            filter,
            {
                id: schema.entities.id,
                name: schema.entities.name,
                type: schema.entities.type,
                ownerId: schema.entities.ownerId,
                addresses: {
                    id: schema.addresses.id,
                    address: sql`${schema.addresses.data} -> 'Location' ->> 'Address1' `,
                    zip: sql`${schema.addresses.data} -> 'Location' ->> 'Postcode' `,
                    city: sql`${schema.addresses.data} -> 'Location' ->> 'City' `,
                    country: sql`${schema.addresses.data} -> 'Location' ->> 'CountryCode' `,
                },
            },
            modifier,
        );

        const mappedData = result.data.map((record) => this.mapToEntity(record));
        return new PaginatedResponseModel<EntityEntity>(mappedData, result.meta);
    }

    async findCustomer(
        resellerIds: string[],
        endUserIds: string[] | null,
        entityId: string,
    ): Promise<EntityEntity | null> {
        const vd = this.db
            .select({
                endUserId: schema.vDocuments.endUserId,
            })
            .from(schema.vDocuments)
            .where(
                and(
                    ...[
                        inArray(schema.vDocuments.resellerId, resellerIds),
                        ...(endUserIds !== null ? [inArray(schema.vDocuments.endUserId, endUserIds)] : []),
                    ],
                ),
            )
            .groupBy(schema.vDocuments.endUserId)
            .as('vd');

        const entity = await this.db
            .select({
                id: schema.entities.id,
                name: schema.entities.name,
                type: schema.entities.type,
            })
            .from(schema.entities)
            .innerJoin(vd, eq(vd.endUserId, schema.entities.id))
            .where(and(eq(schema.entities.id, entityId), eq(schema.entities.type, EntityTypeEnum.EndUser)))
            .limit(1);

        return entity.length > 0 ? entity[0] : null;
    }

    async globalSearch(resellerIds: string[] | null, searchPhrase?: string) {
        const whereConditions: SQLWrapper[] = [
            ...(searchPhrase ? [ilike(schema.entities.name, `%${searchPhrase}%`)] : []),
            inArray(schema.vDocuments.resellerId, resellerIds),
        ];

        const query = this.db
            .select({
                id: schema.vDocuments.endUserId,
                name: schema.entities.name,
                city: sql`(
                    SELECT ${schema.addresses.data} -> 'Location' ->> 'City'
                    FROM ${schema.addresses}
                    WHERE ${schema.addresses.entityId} = ${schema.entities.id}
                    AND ${schema.addresses.data} ->> 'IsDefault' = 'true'
                    LIMIT 1
                )`,
                address1: sql`(
                    SELECT ${schema.addresses.data} -> 'Location' ->> 'Address1'
                    FROM ${schema.addresses}
                    WHERE ${schema.addresses.entityId} = ${schema.entities.id}
                    AND ${schema.addresses.data} ->> 'IsDefault' = 'true'
                    LIMIT 1
                )`,
                address2: sql`(
                    SELECT ${schema.addresses.data} -> 'Location' ->> 'Address2'
                    FROM ${schema.addresses}
                    WHERE ${schema.addresses.entityId} = ${schema.entities.id}
                    AND ${schema.addresses.data} ->> 'IsDefault' = 'true'
                    LIMIT 1
                )`,
                zip: sql`(
                    SELECT ${schema.addresses.data} -> 'Location' ->> 'Postcode'
                    FROM ${schema.addresses}
                    WHERE ${schema.addresses.entityId} = ${schema.entities.id}
                    AND ${schema.addresses.data} ->> 'IsDefault' = 'true'
                    LIMIT 1
                )`,
            })
            .from(schema.vDocuments)
            .leftJoin(schema.entities, eq(schema.entities.id, schema.vDocuments.endUserId))
            .where(and(...whereConditions))
            .groupBy(
                schema.vDocuments.endUserId,
                schema.entities.name,
                sql`(
                    SELECT ${schema.addresses.data} -> 'Location' ->> 'City'
                    FROM ${schema.addresses}
                    WHERE ${schema.addresses.entityId} = ${schema.entities.id}
                    AND ${schema.addresses.data} ->> 'IsDefault' = 'true'
                    LIMIT 1
                )`,
                sql`(
                    SELECT ${schema.addresses.data} -> 'Location' ->> 'Address1'
                    FROM ${schema.addresses}
                    WHERE ${schema.addresses.entityId} = ${schema.entities.id}
                    AND ${schema.addresses.data} ->> 'IsDefault' = 'true'
                    LIMIT 1
                )`,
                sql`(
                    SELECT ${schema.addresses.data} -> 'Location' ->> 'Address2'
                    FROM ${schema.addresses}
                    WHERE ${schema.addresses.entityId} = ${schema.entities.id}
                    AND ${schema.addresses.data} ->> 'IsDefault' = 'true'
                    LIMIT 1
                )`,
                sql`(
                    SELECT ${schema.addresses.data} -> 'Location' ->> 'Postcode'
                    FROM ${schema.addresses}
                    WHERE ${schema.addresses.entityId} = ${schema.entities.id}
                    AND ${schema.addresses.data} ->> 'IsDefault' = 'true'
                    LIMIT 1
                )`,
            )
            .limit(5);

        const items = await query;
        return items;
    }

    async findCustomers(
        resellerIds: string[],
        endUserIds: string[] | null,
        paginationParams: PaginationParamsModel,
        filter?: EntityFilterModel,
    ): Promise<PaginatedResponseModel<EntityEntity>> {
        const whereConditions = [];

        whereConditions.push(eq(schema.entities.type, EntityTypeEnum.EndUser));
        whereConditions.push(sql`${schema.addresses.data}->>'IsDefault' = ${true}`);
        whereConditions.push(sql`${schema.addresses.data}->>'AddressType' = ${AddressTypeEnum.BUSINESS}`);

        if (filter?.search) {
            whereConditions.push(ilike(schema.entities.name, `%${filter.search}%`));
        }

        if (filter?.name) {
            whereConditions.push(ilike(schema.entities.name, `%${filter.name}%`));
        }

        if (filter?.ids && filter.ids.length > 0) {
            whereConditions.push(inArray(schema.entities.id, filter.ids));
        }

        if (filter?.type) {
            whereConditions.push(eq(schema.entities.type, filter.type));
        }

        filter = {
            ...filter,
            sortBy: filter?.sortBy ?? 'name',
            sortOrder: filter?.sortOrder ?? 'asc',
        };

        const vd = this.db
            .select({
                endUserId: schema.vDocuments.endUserId,
            })
            .from(schema.vDocuments)
            .where(
                and(
                    ...[
                        inArray(schema.vDocuments.resellerId, resellerIds),
                        ...(endUserIds !== null ? [inArray(schema.vDocuments.endUserId, endUserIds)] : []),
                    ],
                ),
            )
            .groupBy(schema.vDocuments.endUserId)
            .as('vd');

        const result = await this.findPaginated(
            whereConditions,
            paginationParams,
            {
                ...filter,
                sortBy: this.sortByToEntitySort(filter.sortBy),
            },
            {
                id: schema.entities.id,
                name: schema.entities.name,
                type: schema.entities.type,
                ownerId: schema.entities.ownerId,
                address1: sql`${schema.addresses.data}->'Location'->>'Address1'`,
                addressZip: sql`${schema.addresses.data}->'Location'->>'Postcode'`,
                addressCity: sql`${schema.addresses.data}->'Location'->>'City'`,
                addressCountry: sql`${schema.addresses.data}->'Location'->>'CountryCode'`,
                addresses: {
                    id: schema.addresses.id,
                    address: sql`${schema.addresses.data}->'Location'->>'Address1'`,
                    zip: sql`${schema.addresses.data}->'Location'->>'Postcode'`,
                    city: sql`${schema.addresses.data}->'Location'->>'City'`,
                    country: sql`${schema.addresses.data}->'Location'->>'CountryCode'`,
                },
            },
            (query: PgSelect) => {
                query.innerJoin(vd, eq(vd.endUserId, schema.entities.id));
                query.leftJoin(schema.addresses, eq(this.table.id, schema.addresses.entityId));
                query.groupBy(schema.entities.id, schema.addresses.id);
            },
        );

        const mappedData = result.data.map((record) => this.mapToEntity(record));
        return new PaginatedResponseModel<EntityEntity>(mappedData, result.meta);
    }

    async getDistinctVendorNames(
        filter: EntityDistinctNamesFilterModel,
        resellerIds: string[],
        endUserIds: string[] | null,
    ): Promise<string[]> {
        const vd = this.db
            .select({
                vendorId: schema.vDocuments.vendorId,
            })
            .from(schema.vDocuments)
            .where(
                and(
                    ...[
                        inArray(schema.vDocuments.resellerId, resellerIds),
                        ...(endUserIds !== null ? [inArray(schema.vDocuments.endUserId, endUserIds)] : []),
                    ],
                ),
            )
            .groupBy(schema.vDocuments.vendorId)
            .as('vd');
        const qb = this.db
            .selectDistinct({
                name: schema.entities.name,
            })
            .from(schema.entities)
            .innerJoin(vd, eq(vd.vendorId, schema.entities.id));

        if (filter.type) {
            qb.where(eq(schema.entities.type, filter.type));
        }
        this.logger.error(qb.toSQL().sql, qb.toSQL().params);

        return (await qb).map((el) => el.name);
    }

    sortByToEntitySort(sortBy: string): string {
        switch (sortBy) {
            case 'address':
                return `address1`;
            case 'zip':
                return 'addressZip';
            case 'city':
                return 'addressCity';
            case 'country':
                return 'addressCountry';
            default:
                return sortBy;
        }
    }
}
