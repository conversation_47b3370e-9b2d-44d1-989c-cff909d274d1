import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { RbacModule } from '../../rbac/rbac.module';
import { CommonModule } from '../common/common.module';
import { ContactsModule } from '../contacts';
import { ContactDrizzleRepository } from '../contacts/repositories/contact.drizzle.repository';
import { ContractsModule } from '../contracts/contracts.module';
import { QuotesModule } from '../quotes';
import { CustomersController } from './controllers/customers.controller';
import { EntitiesController } from './controllers/entities.controller';
import { EntitiesService } from './entities.service';
import { EntityPresenter } from './presenters/entity-presenter';
import { AssetViewDrizzleRepository } from './repositories/asset-view.drizzle.repository';
import { EntityDrizzleRepository } from './repositories/entity.drizzle.repository';
import { EntityRepository } from './repositories/entity.repository';

@Module({
    imports: [
        ConfigModule,
        forwardRef(() => RbacModule),
        forwardRef(() => CommonModule),
        forwardRef(() => ContactsModule),
        forwardRef(() => QuotesModule),
        forwardRef(() => ContractsModule),
    ],
    controllers: [EntitiesController, CustomersController],
    providers: [
        ContactDrizzleRepository,
        EntityRepository,
        EntityDrizzleRepository,
        EntitiesService,
        EntityPresenter,
        AssetViewDrizzleRepository,
    ],
    exports: [EntityRepository, EntityDrizzleRepository, AssetViewDrizzleRepository, EntitiesService, EntityPresenter],
})
export class EntitiesModule {}
