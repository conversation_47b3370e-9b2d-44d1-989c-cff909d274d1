import { relations } from 'drizzle-orm';
import { index, jsonb, pgEnum, pgTable, text } from 'drizzle-orm/pg-core';

import { addresses, organizationEntities, quotes } from '../../../drizzle/schema';
import { DataSourceEnum } from '../contracts/enums/data-source.enum';
import { EntityTypeEnum } from './enums/entity-type.enum';

export const entityType = pgEnum('entity_type', EntityTypeEnum);

const dataSource = pgEnum('data_source', DataSourceEnum);

export const entities = pgTable(
    'entities',
    {
        id: text().primaryKey().notNull(),
        type: entityType(),
        name: text(),
        ownerId: text('owner_id'),
        data: jsonb(),

        source: dataSource('source').default(DataSourceEnum.iAsset).notNull(),
    },
    (table) => [index('idx_entities_name').using('gin', table.name.op('gin_trgm_ops'))],
);

export const entitiesRelations = relations(entities, ({ many, one }) => ({
    organizationEntities: many(organizationEntities),
    quotes: many(quotes),
    addresses: many(addresses),
}));
