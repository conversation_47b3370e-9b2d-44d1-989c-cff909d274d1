import {
    Collection,
    Entity as ORMEntity,
    EntityRepositoryType,
    Enum,
    ManyToOne,
    OneToMany,
    PrimaryKey,
    Property,
} from '@mikro-orm/core';

import { Address } from '../../common/entities/address.entity';
import { Contact } from '../../contacts/entities/contact.entity';
import { EntityTypeEnum } from '../enums/entity-type.enum';
import { EntityRepository } from '../repositories/entity.repository';

@ORMEntity({
    tableName: 'entities',
    repository: () => EntityRepository,
})
export class EntityEntity {
    [EntityRepositoryType]?: EntityRepository;

    @PrimaryKey({ type: 'text' })
    id: string;

    @Property({ type: 'text', nullable: true })
    name?: string;

    @Enum({ items: () => EntityTypeEnum, nativeEnumName: 'entity_type', nullable: true })
    type?: EntityTypeEnum;

    @Property({ type: 'text', nullable: true })
    owner_id?: string;

    @Property({ type: 'object', nullable: true })
    data?: {
        EntityId?: number;
        EntityName?: string;
        EntityType?: string;
        PrimaryContactId?: number | null;
        AccountManagerId?: string;
    };

    @ManyToOne(() => EntityEntity, { nullable: true, persist: false })
    owner?: EntityEntity;

    @OneToMany(() => Contact, (contact) => contact.entity, { nullable: true, persist: false })
    contacts?: Contact[];

    @OneToMany(() => Address, (address) => address.entity, { nullable: true, persist: false })
    addresses? = new Collection<Address>(this);
}
