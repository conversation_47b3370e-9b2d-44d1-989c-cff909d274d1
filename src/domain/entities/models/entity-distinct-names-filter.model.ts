import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { EntityTypeEnum } from '../enums/entity-type.enum';

const EntityDistinctNamesFilterSchema = extendApi(
    z.object({
        type: z.nativeEnum(EntityTypeEnum).optional().describe('Filter entities by type'),
    }),
);

export class EntityDistinctNamesFilterModel extends createZodDto(EntityDistinctNamesFilterSchema) {}
