import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { BaseFilterSchema } from '../../common/pagination/pagination.schema';
import { EntityTypeEnum } from '../enums/entity-type.enum';

const ENTITY_SORTABLE_FIELDS = ['id', 'name', 'type', 'createdAt', 'updatedAt', 'address', 'city', 'zip', 'country'];

const EntityFilterSchema = extendApi(
    BaseFilterSchema.extend({
        search: z.string().optional().describe('Filter entities by name'),
        name: z.string().optional().describe('Filter entities by name'),
        ownerId: z.string().optional().describe('Filter entities by ownerId'),
        type: z.nativeEnum(EntityTypeEnum).optional().describe('Filter entities by type'),
        ids: z.array(z.string()).optional().describe('Filter entities by ids'),
        sortBy: extendApi(
            z
                .union([
                    z.literal('id'),
                    z.literal('name'),
                    z.literal('type'),
                    z.literal('createdAt'),
                    z.literal('updatedAt'),
                    z.literal('address'),
                    z.literal('city'),
                    z.literal('zip'),
                    z.literal('country'),
                ])
                .optional(),
            {
                description: 'Field to sort by',
                type: 'string',
                enum: ENTITY_SORTABLE_FIELDS,
                default: 'name',
            },
        ),
        contractId: z.string().optional().describe('Filter entities by contractId'),
    }),
    {
        title: 'EntityFilter',
        description: 'Filter parameters for entities',
    },
);

export class EntityFilterModel extends createZodDto(EntityFilterSchema) {}
