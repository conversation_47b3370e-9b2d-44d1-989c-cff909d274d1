import { createZodDto } from '@anatine/zod-nestjs';

import { PaginationMetaModel } from '../../common/pagination/pagination.model';
import { EntityListSchema } from '../schemas/entity-list.schema';
import { EntityListItemModel } from './entity-list-item.model';

export class EntityListModel extends createZodDto(EntityListSchema) {
    zodSchema = EntityListSchema;

    data: EntityListItemModel[] = [];
    meta: PaginationMetaModel = new PaginationMetaModel();

    setData(data: EntityListItemModel[], meta: PaginationMetaModel = new PaginationMetaModel()) {
        this.data = data;
        this.meta = meta;
    }

    getData() {
        return this.zodSchema.parse({
            data: this.data,
            meta: this.meta,
        });
    }
}
