import { Inject, Injectable, Logger } from '@nestjs/common';
import { InferSelectModel } from 'drizzle-orm';

import * as schema from '../../../drizzle/schema';
import { NoAssetsFoundForExportException } from '../assets/exceptions/no-assets-found-for-export.exception';
import { AssetListModel } from '../common/models';
import { PaginationParamsModel } from '../common/pagination/pagination.model';
import { ExportFormat, ExportService } from '../common/services';
import { ContactListModel } from '../contacts/models';
import { ContactListPresenter } from '../contacts/presenters';
import { ContactDrizzleRepository } from '../contacts/repositories/contact.drizzle.repository';
import { Contract } from '../contracts/entities/contract.entity';
import { ContractNotFoundByIdException } from '../contracts/exceptions/contract-not-found-by-id.exception';
import { ContractDrizzleRepository } from '../contracts/repositories/contract.drizzle.repository';
import { Quote } from '../quotes';
import { QuoteNotFoundByIdException } from '../quotes/exceptions/quote-not-found-by-id.exception';
import { QuoteDrizzleRepository } from '../quotes/repositories/quote.drizzle.repository';
import { EntityEntity } from './entities/entity.entity';
import { CustomerNotFoundException } from './exceptions/customer-not-found.exception';
import { EntityNotFoundException } from './exceptions/entity-not-found.exception';
import { EntityNotFoundByIdException } from './exceptions/entity-not-found-by-id.exception';
import { NoPrimaryContactFoundException } from './exceptions/no-primary-contact-found.exception';
import { EntityModel } from './models/entity.model';
import { EntityAssetsFilterModel } from './models/entity-assets-filter.model';
import { EntityDistinctNamesFilterModel } from './models/entity-distinct-names-filter.model';
import { EntityFilterModel } from './models/entity-filter.model';
import { EntityListModel } from './models/entity-list.model';
import { EntityPresenter } from './presenters/entity-presenter';
import { AssetViewDrizzleRepository } from './repositories/asset-view.drizzle.repository';
import { EntityDrizzleRepository } from './repositories/entity.drizzle.repository';

type QuoteDrizzle = Partial<InferSelectModel<typeof schema.quotes>>;

@Injectable()
export class EntitiesService {
    private readonly logger = new Logger(EntitiesService.name);

    constructor(
        private readonly contactListPresenter: ContactListPresenter,
        private readonly entityPresenter: EntityPresenter,
        private readonly exportService: ExportService,

        @Inject(EntityDrizzleRepository)
        private readonly entityDrizzleRepository: EntityDrizzleRepository,
        @Inject(QuoteDrizzleRepository)
        private readonly quoteDrizzleRepository: QuoteDrizzleRepository,
        @Inject(AssetViewDrizzleRepository)
        private readonly assetViewDrizzleRepository: AssetViewDrizzleRepository,
        @Inject(ContactDrizzleRepository)
        private readonly contactDrizzleRepository: ContactDrizzleRepository,
        @Inject(ContractDrizzleRepository)
        private readonly contractDrizzleRepository: ContractDrizzleRepository,
    ) {}

    async getEntityById(entityId: string, skipNotFound = false): Promise<EntityEntity> {
        this.logger.debug('Fetching entity by ID', { entityId, skipNotFound });

        try {
            const entity = await this.entityDrizzleRepository.findById(entityId);

            if (!entity && !skipNotFound) {
                this.logger.warn('Entity not found', { entityId });
                throw new EntityNotFoundException();
            }

            this.logger.debug('Entity retrieved successfully', { entityId, found: !!entity });
            return entity;
        } catch (error) {
            this.logger.error('Failed to fetch entity by ID', {
                entityId,
                skipNotFound,
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined,
            });
            throw error;
        }
    }

    async getCustomerById(resellerIds: string[], endUserIds: string[] | null, entityId: string): Promise<EntityModel> {
        this.logger.log('Fetching customer by ID', {
            entityId,
            resellerIdsCount: resellerIds.length,
            endUserIdsCount: endUserIds?.length || 0,
        });

        try {
            const customer = await this.entityDrizzleRepository.findCustomer(resellerIds, endUserIds, entityId);

            if (!customer) {
                this.logger.warn('Customer not found', { entityId, resellerIds, endUserIds });
                throw new CustomerNotFoundException();
            }

            this.logger.log('Customer retrieved successfully', { entityId, customerId: customer.id });
            return this.entityPresenter.toModel(customer);
        } catch (error) {
            this.logger.error('Failed to fetch customer by ID', {
                entityId,
                resellerIdsCount: resellerIds.length,
                endUserIdsCount: endUserIds?.length || 0,
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined,
            });
            throw error;
        }
    }

    async getEntities(paginationParams: PaginationParamsModel, filter?: EntityFilterModel): Promise<EntityListModel> {
        this.logger.debug('Fetching entities with pagination', {
            limit: paginationParams?.limit,
            cursor: paginationParams?.cursor,
            hasFilter: !!filter,
        });

        try {
            const paginatedResult = await this.entityDrizzleRepository.findEntitiesWithFilter(paginationParams, filter);
            const items = paginatedResult.data.map((entity) => this.entityPresenter.toListItemModel(entity));
            const model = new EntityListModel();
            model.setData(items, paginatedResult.meta);

            this.logger.debug('Entities retrieved successfully', {
                resultCount: items.length,
                hasNextPage: paginatedResult.meta.hasNextPage,
            });

            return model;
        } catch (error) {
            this.logger.error('Failed to fetch entities', {
                limit: paginationParams?.limit,
                cursor: paginationParams?.cursor,
                hasFilter: !!filter,
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined,
            });
            throw error;
        }
    }

    async globalSearch(resellerIds: string[], searchPhrase?: string) {
        this.logger.log('Performing global search', {
            resellerIdsCount: resellerIds.length,
            hasSearchPhrase: !!searchPhrase,
            searchPhraseLength: searchPhrase?.length || 0,
        });

        try {
            const result = await this.entityDrizzleRepository.globalSearch(resellerIds, searchPhrase);

            this.logger.log('Global search completed successfully', {
                resellerIdsCount: resellerIds.length,
                searchPhrase: searchPhrase ? '[REDACTED]' : undefined,
                resultCount: Array.isArray(result) ? result.length : 'unknown',
            });

            return result;
        } catch (error) {
            this.logger.error('Global search failed', {
                resellerIdsCount: resellerIds.length,
                hasSearchPhrase: !!searchPhrase,
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined,
            });
            throw error;
        }
    }

    async getCustomers(
        resellerIds: string[],
        endUserIds: string[] | null,
        paginationParams: PaginationParamsModel,
        filter?: EntityFilterModel,
    ): Promise<EntityListModel> {
        const paginatedResult = await this.entityDrizzleRepository.findCustomers(
            resellerIds,
            endUserIds,
            paginationParams,
            filter,
        );
        const items = paginatedResult.data.map((entity) => this.entityPresenter.toListItemModel(entity));
        const model = new EntityListModel();
        model.setData(items, paginatedResult.meta);

        return model;
    }

    async getContactsByEntityId(entityId: string): Promise<ContactListModel> {
        const entity = await this.entityDrizzleRepository.findById(entityId);

        if (!entity) {
            throw new EntityNotFoundException();
        }

        const primaryContactId = entity.data?.PrimaryContactId || null;

        if (!primaryContactId) {
            throw new NoPrimaryContactFoundException();
        }

        const contacts = await this.contactDrizzleRepository.findContactByIds([primaryContactId]);

        const model = this.contactListPresenter.toModel(contacts);

        return model;
    }

    async getAssetsByEntityId(
        resellerIds: string[],
        endUserIds: string[] | null,
        entityId: string,
        paginationParams?: PaginationParamsModel,
        filter?: EntityAssetsFilterModel,
    ): Promise<AssetListModel> {
        const res = this.entityDrizzleRepository.checkIfEntityExistsByEntityId(resellerIds, endUserIds, entityId);

        if (!res) {
            throw new EntityNotFoundByIdException(entityId);
        }

        const result = await this.assetViewDrizzleRepository.findAssetsByEntityId(
            resellerIds,
            endUserIds,
            entityId,
            paginationParams,
            filter,
        );

        if (!result) {
            return new AssetListModel();
        }

        const model = new AssetListModel();

        if (Array.isArray(result)) {
            const items = result.map((entity) => this.entityPresenter.toAssetModel(entity));
            model.setData(items);
        } else {
            const items = result.data.map((entity) => this.entityPresenter.toAssetModel(entity));
            model.setData(items);
            model.setMeta(result.meta);
        }

        return model;
    }

    async exportAssetsByEntityId(
        resellerIds: string[],
        endUserIds: string[] | null,
        entityId: string,
        format: ExportFormat,
        filter?: EntityAssetsFilterModel,
    ): Promise<{ data: Buffer; filename: string }> {
        this.logger.log('Starting asset export by entity ID', {
            entityId,
            format,
            resellerIdsCount: resellerIds.length,
            endUserIdsCount: endUserIds?.length || 0,
            hasFilter: !!filter,
        });

        try {
            const assetList = await this.getAssetsByEntityId(resellerIds, endUserIds, entityId, undefined, filter);
            const assets = assetList.getData();

            if (!assets || assets.length === 0) {
                this.logger.warn('No assets found for export', { entityId, format });
                throw new NoAssetsFoundForExportException();
            }

            const exportData = assets.map((asset) => ({
                Name: asset.name || '',
                SerialNumber: asset.serialNumber || '',
                ProductSku: asset.productSku || '',
                ItemsCount: asset.itemsCount || 0,
                ...(asset.serviceGroupSku ? { ServiceGroupSku: asset.serviceGroupSku } : {}),
                ...(asset.serviceLevelSku ? { ServiceLevelSku: asset.serviceLevelSku } : {}),
                ...(asset.resellerPriceFinalSum ? { RetailPriceSum: asset.resellerPriceFinalSum } : {}),
                // ...(asset.distributorPriceFinalSum ? { DistributorPriceSum: asset.distributorPriceFinalSum } : {}), // Hidden because of AH-101
                ...(asset.endCustomerPriceFinalSum ? { EndCustomerPriceSum: asset.endCustomerPriceFinalSum } : {}),
            }));

            const result = this.exportService.exportData(
                exportData,
                format,
                `assets-entity-${entityId}-${new Date().toISOString()}`,
            );

            this.logger.log('Asset export completed successfully', {
                entityId,
                format,
                assetCount: assets.length,
                filename: `assets-entity-${entityId}-${new Date().toISOString()}`,
            });

            return result;
        } catch (error) {
            this.logger.error('Asset export failed', {
                entityId,
                format,
                resellerIdsCount: resellerIds.length,
                endUserIdsCount: endUserIds?.length || 0,
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined,
            });
            throw error;
        }
    }

    async findByContractId(
        resellerIds: string[],
        endUserIds: string[] | null,
        contractId: string,
    ): Promise<EntityListModel> {
        const contract = await this.contractDrizzleRepository.findContractById(resellerIds, endUserIds, contractId);

        if (!contract) {
            throw new ContractNotFoundByIdException(contractId);
        }

        return await this.findEntitiesByQuoteOrContract(contract);
    }

    async findByResellerIdAndQuoteId(
        resellerIds: string[],
        endUserIds: string[] | null,
        quoteId: string,
    ): Promise<EntityListModel> {
        const quote = await this.quoteDrizzleRepository.findQuoteByResellerIdAndQuoteId(
            resellerIds,
            endUserIds,
            quoteId,
        );

        if (!quote) {
            throw new QuoteNotFoundByIdException(quoteId);
        }

        return await this.findEntitiesByQuoteOrContract(quote);
    }

    async getDistinctVendorNames(
        filter: EntityDistinctNamesFilterModel,
        resellerIds: string[],
        endUserIds: string[] | null,
    ): Promise<string[]> {
        return await this.entityDrizzleRepository.getDistinctVendorNames(filter, resellerIds, endUserIds);
    }

    private async findEntitiesByQuoteOrContract(entity: Quote | Contract | QuoteDrizzle): Promise<EntityListModel> {
        const ids = [entity.vendorId, entity.distributorId, entity.resellerId, entity.endUserId].filter((id) => !!id);
        const priorityMap = new Map(ids.map((id, index) => [id, index]));

        const entities = await this.entityDrizzleRepository.findByIds(ids);
        entities.sort((a, b) => {
            const aIndex = priorityMap.get(a.id) ?? Infinity;
            const bIndex = priorityMap.get(b.id) ?? Infinity;
            return aIndex - bIndex;
        });
        const items = entities.map((entity) => this.entityPresenter.toListItemModel(entity));
        const model = new EntityListModel();
        model.setData(items, { totalCount: entities.length, hasNextPage: false, nextCursor: null });

        return model;
    }
}
