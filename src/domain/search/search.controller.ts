import { Controller, Get, Query, Res } from '@nestjs/common';
import { Api<PERSON><PERSON>ation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';

import { RequestContext } from '../../auth/context.decorator';
import { BaseController } from '../common/controllers/base.controller';
import { Context } from '../common/interfaces';
import { SearchParamsModel } from './models/search.model';
import { SearchService } from './search.service';

@ApiTags('Search')
@Controller('search')
export class SearchController extends BaseController {
    constructor(private readonly searchService: SearchService) {
        super();
    }

    @Get()
    @ApiOperation({ summary: 'Global search across quotes, contracts, assets, and customers' })
    @ApiQuery({ name: 'query', required: true, description: 'Search query string' })
    @ApiQuery({ name: 'limit', required: false, description: 'Maximum number of results per category', type: Number })
    @ApiResponse({ status: 200, description: 'Returns search results from all categories based on permissions' })
    async globalSearch(@Query() params: SearchParamsModel, @RequestContext() context: Context, @Res() res: Response) {
        const results = await this.searchService.globalSearch(context, params.query);
        return this.sendOk(res, results);
    }
}
