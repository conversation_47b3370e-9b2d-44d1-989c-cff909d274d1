import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

import { ContractUiStatusEnum } from '../../contracts/enums';
import { QuoteUiStatusEnum } from '../../quotes/enums/quote-ui-status.enum';

export const SearchParamsSchema = extendApi(
    z.object({
        query: extendApi(z.string().min(3), {
            description: 'Search query is required',
        }),
    }),
    {
        title: 'SearchParams',
        description: 'Search query parameters',
    },
);

const QuoteSearchEntitySchema = extendApi(
    z.object({
        id: z.string(),
        name: z.string(),
        quoteNo: z.string(),
        groupId: z.string(),
        endUser: z.object({
            id: z.string(),
            name: z.string(),
        }),
        uiStatus: z.nativeEnum(QuoteUiStatusEnum),
        resellerTotalPrice: z.string(),
        startDate: z.string(),
        endDate: z.string(),
        expiresIn: z.unknown(),
        currency: z.string(),
    }),
    {
        title: 'QuoteSearchEntity',
        description: 'Quote search entity',
    },
);

const ContractSearchEntitySchema = extendApi(
    z.object({
        id: z.string(),
        contractNo: z.string(),
        sar: z.string(),
        groupId: z.string(),
        endUser: z.object({
            id: z.string(),
            name: z.string(),
        }),
        uiStatus: z.nativeEnum(ContractUiStatusEnum),
        resellerTotalPrice: z.string(),
        startDate: z.string(),
        endDate: z.string(),
        expiresIn: z.unknown(),
    }),
    {
        title: 'ContractSearchEntity',
        description: 'Contract search entity',
    },
);

const CustomerSearchEntitySchema = extendApi(
    z.object({
        id: z.string(),
        name: z.string(),
        address1: z.unknown(),
        address2: z.unknown(),
        city: z.unknown(),
        zip: z.unknown(),
    }),
    {
        title: 'CustomerSearchEntity',
        description: 'Customer search entity',
    },
);

const AssetSearchEntitySchema = extendApi(
    z.object({
        covered: z.unknown(),
        serialNumber: z.string(),
        endDate: z.string(),
        productSku: z.string(),
        endUser: z.object({
            id: z.string(),
            name: z.string(),
        }),
        product: z.object({
            description: z.string(),
        }),
    }),
    {
        title: 'AssetSearchEntity',
        description: 'Asset search entity',
    },
);

export const GlobalSearchResponseSchema = extendApi(
    z.object({
        quotes: extendApi(z.array(QuoteSearchEntitySchema), {
            description: 'Quote search results',
        }),
        contracts: extendApi(z.array(ContractSearchEntitySchema), {
            description: 'Contract search results',
        }),
        customers: extendApi(z.array(CustomerSearchEntitySchema), {
            description: 'Customer search results',
        }),
        assets: extendApi(z.array(AssetSearchEntitySchema), {
            description: 'Asset search results',
        }),
    }),
    {
        title: 'GlobalSearchResponse',
        description: 'Global search response',
    },
);
