import { Injectable, Logger } from '@nestjs/common';

import { hasPermission } from '../../helpers/permissions.helper';
import { Context } from '../common/interfaces';
import { ContractItemsService } from '../contracts/contract-items.service';
import { ContractsService } from '../contracts/contracts.service';
import { EntitiesService } from '../entities/entities.service';
import { QuotesService } from '../quotes';
import { ActionType, ResourceType } from '../users/entities';
import { GlobalSearchResponse } from './models/search.model';

@Injectable()
export class SearchService {
    private readonly logger = new Logger(SearchService.name);

    constructor(
        private readonly quotesService: QuotesService,
        private readonly contractsService: ContractsService,
        private readonly contractItemsService: ContractItemsService,
        private readonly entitiesService: EntitiesService,
    ) {}

    async globalSearch(context: Context, searchPhrase: string): Promise<GlobalSearchResponse> {
        this.logger.log('Global search started', {
            userId: context.user?.id,
            organizationId: context.organizationId,
            entityId: context.entityId,
            searchPhraseLength: searchPhrase?.length || 0,
            resellerIdsCount: context.resellerIds?.length || 0,
            endUserIdsCount: context.endUserIds?.length || 0,
        });

        const entityId = context.entityId;
        if (!entityId) {
            this.logger.warn('Global search aborted - no entity ID in context', {
                userId: context.user?.id,
                organizationId: context.organizationId,
            });
            return {
                quotes: [],
                contracts: [],
                customers: [],
                assets: [],
            };
        }

        try {
            const normalizedSearchPhrase = searchPhrase.toUpperCase();

            const features = {
                quotes: {
                    resourceType: ResourceType.QUOTE,
                    fetch: () =>
                        this.quotesService.globalSearch(
                            context.resellerIds,
                            context.endUserIds,
                            normalizedSearchPhrase,
                        ),
                },
                contracts: {
                    resourceType: ResourceType.CONTRACT,
                    fetch: () =>
                        this.contractsService.globalSearch(
                            context.resellerIds,
                            context.endUserIds,
                            normalizedSearchPhrase,
                        ),
                },
                customers: {
                    resourceType: ResourceType.ENTITY,
                    fetch: () => this.entitiesService.globalSearch(context.resellerIds, normalizedSearchPhrase),
                },
                assets: {
                    resourceType: ResourceType.ASSET,
                    fetch: () => this.contractItemsService.globalSearch(context.resellerIds, normalizedSearchPhrase),
                },
            };

            const enabledFeatures = Object.entries(features).filter(([_, { resourceType }]) =>
                hasPermission(context.permissions, resourceType, ActionType.READ),
            );

            this.logger.debug('Executing global search with permissions', {
                userId: context.user?.id,
                entityId: context.entityId,
                enabledFeaturesCount: enabledFeatures.length,
                enabledFeatures: enabledFeatures.map(([key]) => key),
            });

            const results = await Promise.all(
                Object.entries(features).map(async ([key, { resourceType, fetch }]) => {
                    if (hasPermission(context.permissions, resourceType, ActionType.READ)) {
                        try {
                            const startTime = Date.now();
                            const result = await fetch();
                            const duration = Date.now() - startTime;

                            this.logger.debug(`Global search ${key} completed`, {
                                userId: context.user?.id,
                                entityId: context.entityId,
                                feature: key,
                                resultCount: Array.isArray(result) ? result.length : 'unknown',
                                durationMs: duration,
                            });

                            return [key, result];
                        } catch (error) {
                            this.logger.error(`Global search ${key} failed`, {
                                userId: context.user?.id,
                                entityId: context.entityId,
                                feature: key,
                                error: error instanceof Error ? error.message : 'Unknown error',
                            });
                            return [key, []];
                        }
                    }
                    return [key, []];
                }),
            );

            const response = Object.fromEntries(results);
            const totalResults = Object.values(response).reduce(
                (sum: number, arr) => sum + (Array.isArray(arr) ? arr.length : 0),
                0,
            );

            this.logger.log('Global search completed successfully', {
                userId: context.user?.id,
                organizationId: context.organizationId,
                entityId: context.entityId,
                searchPhraseLength: searchPhrase?.length || 0,
                totalResults,
                enabledFeaturesCount: enabledFeatures.length,
            });

            return response;
        } catch (error) {
            this.logger.error('Global search failed', {
                userId: context.user?.id,
                organizationId: context.organizationId,
                entityId: context.entityId,
                searchPhraseLength: searchPhrase?.length || 0,
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined,
            });
            throw error;
        }
    }
}
