import { Global, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { SeedCommand } from './commands/seed.command';
import { DrizzleController } from './drizzle.controller';
import { DRIZZLE_PROVIDER, drizzleProvider, PG_POOL_PROVIDER, pgPoolProvider } from './drizzle.provider';
import { DrizzleService } from './drizzle.service';
import { SeederService } from './seeder/seeder.service';

@Global()
@Module({
    imports: [ConfigModule],
    controllers: [DrizzleController],
    providers: [pgPoolProvider, drizzleProvider, DrizzleService, SeederService, SeedCommand],
    exports: [DRIZZLE_PROVIDER, PG_POOL_PROVIDER, SeederService],
})
export class DrizzleModule {}
