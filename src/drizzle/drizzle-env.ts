// Helper for constructing DATABASE_URL from individual environment variables

export const getDatabaseUrl = (): string => {
    const host = process.env.DATABASE_HOST || 'localhost';
    const port = process.env.DATABASE_PORT || '5432';
    const dbName = process.env.DATABASE_NAME || 'app';
    const user = process.env.DATABASE_USER || 'user';
    const password = process.env.DATABASE_PASSWORD || 'password';

    return `postgresql://${user}:${password}@${host}:${port}/${dbName}`;
};
