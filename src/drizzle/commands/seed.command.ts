import { Logger } from '@nestjs/common';
import { Command, CommandRunner, Option } from 'nest-commander';

import { SeederService } from '../seeder/seeder.service';

interface SeedCommandOptions {
    seeder?: string;
}

@Command({
    name: 'db:seed',
    description: 'Seed the database with initial data',
})
export class SeedCommand extends CommandRunner {
    private readonly logger = new Logger(SeedCommand.name);

    constructor(private readonly seederService: SeederService) {
        super();
    }

    async run(passedParams: string[], options?: SeedCommandOptions): Promise<void> {
        try {
            if (options?.seeder) {
                this.logger.log(`Running specific seeder: ${options.seeder}`);
                await this.seederService.runSpecificSeeder(options.seeder);
            } else {
                this.logger.log('Running all seeders');
                await this.seederService.seed();
            }
            this.logger.log('Seeding completed successfully');
        } catch (error) {
            this.logger.error({ err: error }, 'Error during seeding');
            process.exit(1);
        }
    }

    @Option({
        flags: '-s, --seeder [seeder]',
        description: 'Specify a specific seeder to run',
    })
    parseSeeder(val: string): string {
        return val;
    }
}
