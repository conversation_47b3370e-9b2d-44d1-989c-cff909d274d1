import { toCamelCase } from 'drizzle-orm/casing';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { promises as fs } from 'fs';
import { createReadStream } from 'fs';
import { join } from 'path';
import * as readline from 'readline';

import * as schema from '../../../../drizzle/schema';
import {
    addresses,
    contacts,
    contractItems,
    contracts,
    entities,
    products,
    quoteItems,
    quotes,
} from '../../../../drizzle/schema';
import { DrizzleSeeder } from '../drizzle.seeder';

export class DumpSeeder extends DrizzleSeeder {
    private tables = {
        addresses: addresses,
        contacts: contacts,
        entities: entities,
        products: products,
        contracts: contracts,
        contract_items: contractItems,
        quotes: quotes,
        quote_items: quoteItems,
    };

    constructor(protected readonly db: NodePgDatabase<typeof schema>) {
        super(db);
    }

    async run(): Promise<void> {
        console.log('Running dump seeder...');

        await this.seedTables();

        console.log('Dump seeding completed');
    }

    private async seedTables() {
        for (const table of Object.keys(this.tables)) {
            console.log(`Seeding ${table}...`);

            // Read file for the current table by chunk of 1000 items
            const filePath = join(process.cwd(), 'dump', `${table}.jsonl`);

            try {
                // Check if file exists
                await fs.access(filePath);

                // Process file in chunks
                await this.processFileInChunks(filePath, table);

                console.log(`Completed seeding ${table}`);
            } catch (error) {
                console.error(`Error processing ${table}: ${error.message}`);
            }
        }
    }

    private async processFileInChunks(filePath: string, tableName: string) {
        const fileStream = createReadStream(filePath);
        const rl = readline.createInterface({
            input: fileStream,
            crlfDelay: Infinity,
        });

        let batch = [];
        let linesRead = 0;

        for await (const line of rl) {
            if (!line.trim()) continue; // Skip empty lines

            try {
                const data = JSON.parse(line);
                batch.push(data);
                linesRead++;

                // When batch size reaches 1000, insert and reset batch
                if (batch.length >= 1000) {
                    await this.insertBatch(tableName, batch);
                    console.log(`Inserted ${batch.length} records into ${tableName}. Total: ${linesRead}`);
                    batch = [];
                }
            } catch (error) {
                console.error(`Error parsing line in ${tableName}: ${error.message}`);
            }
        }

        // Insert any remaining records
        if (batch.length > 0) {
            await this.insertBatch(tableName, batch);
            console.log(`Inserted final ${batch.length} records into ${tableName}. Total: ${linesRead}`);
        }
    }

    private async insertBatch(tableName: string, records: any[]) {
        if (records.length === 0) return;
        try {
            // Convert all property names of all objects of records array to camelcase using camelCase()
            records = records.map((record) => {
                const newRecord: any = {};
                for (const key in record) {
                    if (Object.prototype.hasOwnProperty.call(record, key)) {
                        const camelCaseKey = toCamelCase(key);
                        newRecord[camelCaseKey] = record[key];
                    }
                }
                return newRecord;
            });

            await this.db.insert(this.tables[tableName]).values(records);
        } catch (error) {
            console.error(`Failed to insert batch into ${tableName}: ${error.message}`);
            throw error;
        }
    }
}
