import { NodePgDatabase } from 'drizzle-orm/node-postgres';

import * as schema from '../../../../drizzle/schema';
import { DrizzleSeeder } from '../drizzle.seeder';
import { DumpSeeder } from './dump.seeder';
import { OrganizationSeeder } from './organization.seeder';
import { OrganizationEntitySeeder } from './organization-entity.seeder';
import { UserSeeder } from './user.seeder';
import { UserRolesSeeder } from './user-roles.seeder';

export class DatabaseSeeder extends DrizzleSeeder {
    private readonly seeders = [UserSeeder, OrganizationSeeder, UserRolesSeeder, DumpSeeder, OrganizationEntitySeeder];

    constructor(protected readonly db: NodePgDatabase<typeof schema>) {
        super(db);
    }

    async run(): Promise<void> {
        this.logger.log('Running all seeders...');

        for (const Seeder of this.seeders) {
            const seeder = new Seeder(this.db);
            this.logger.log(`Running ${Seeder.name}...`);
            await seeder.run();
        }

        this.logger.log('All seeders completed successfully');
    }
}
