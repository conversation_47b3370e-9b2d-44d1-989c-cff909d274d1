import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { v4 as uuidv4 } from 'uuid';

import * as schema from '../../../../drizzle/schema';
import { organizationEntities } from '../../../domain/organizations/organization-entity.schema';
import { Dr<PERSON>zleSeeder } from '../drizzle.seeder';

export class OrganizationEntitySeeder extends DrizzleSeeder {
    private readonly ORGANIZATION_ID = 'c8256e57-932b-4117-9680-92d486b71996';
    private readonly ENTITY_ID = '9900301986';

    constructor(protected readonly db: NodePgDatabase<typeof schema>) {
        super(db);
    }

    async run(): Promise<void> {
        this.logger.log('Seeding organization-entity associations...');

        // Check if organization exists
        const organization = await this.db.query.organizations.findFirst({
            where: (fields, { eq }) => eq(fields.id, this.ORGANIZATION_ID),
        });

        // Check if entity exists
        const entity = await this.db.query.entities.findFirst({
            where: (fields, { eq }) => eq(fields.id, this.ENTITY_ID),
        });

        if (!organization || !entity) {
            this.logger.error('Organization or entity not found');
            return;
        }

        // Check if relation already exists
        const relation = await this.db.query.organizationEntities.findFirst({
            where: (fields, { and, eq }) =>
                and(eq(fields.organizationId, this.ORGANIZATION_ID), eq(fields.entityId, this.ENTITY_ID)),
        });

        if (!relation) {
            const now = new Date().toISOString();

            // Create the relation
            await this.db.insert(organizationEntities).values({
                id: uuidv4(),
                organizationId: this.ORGANIZATION_ID,
                entityId: this.ENTITY_ID,
                createdAt: now,
                updatedAt: now,
            });

            this.logger.log('Created organization-entity association');
        } else {
            this.logger.log('Organization-entity association already exists');
        }

        this.logger.log('Organization-entity seeding completed');
    }
}
