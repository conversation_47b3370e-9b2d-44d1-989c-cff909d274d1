import { eq } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

import * as schema from '../../../../drizzle/schema';
import { organizations } from '../../../domain/organizations/organization.schema';
import { DrizzleSeeder } from '../drizzle.seeder';

export class OrganizationSeeder extends DrizzleSeeder {
    constructor(protected readonly db: NodePgDatabase<typeof schema>) {
        super(db);
    }

    async run(): Promise<void> {
        this.logger.log('Seeding organizations...');

        // Create organizations
        const orgs = [
            {
                id: 'c8256e57-932b-4117-9680-92d486b71996',
                name: 'api GmbH',
            },
            {
                id: '0fd07b17-3314-40b5-ac32-7189051f773b',
                name: 'BitHawk AG',
                parentId: null,
                // parentId: 'c8256e57-932b-4117-9680-92d486b71996',
            },
            {
                id: 'a9233c9c-4634-493a-9f12-bef4d1cf3c5e',
                name: 'DTS Systeme GmbH',
            },
            {
                id: 'a9233c9c-4634-493a-9f12-bef4d1cf3c51',
                name: 'SUPER ADMIN',
            },
        ];

        const now = new Date().toISOString();

        // Create all organizations first
        for (const org of orgs) {
            const { parentId, ...orgData } = org;

            // Check if the organization already exists
            const existingOrg = await this.db.query.organizations.findFirst({
                where: (fields, { eq }) => eq(fields.id, org.id),
            });

            if (existingOrg) {
                // Update the organization
                await this.db
                    .update(organizations)
                    .set({
                        name: org.name,
                        updatedAt: now,
                    })
                    .where(eq(organizations.id, org.id));

                this.logger.log(`Updated organization: ${org.name}`);
            } else {
                // Insert new organization
                await this.db.insert(organizations).values({
                    id: org.id,
                    name: org.name,
                    createdAt: now,
                    updatedAt: now,
                    status: 'active',
                    emailable: false,
                });

                this.logger.log(`Created organization: ${org.name}`);
            }
        }

        // Set parent relationships after all organizations exist
        for (const org of orgs) {
            if (org.parentId) {
                await this.db.update(organizations).set({ parentId: org.parentId }).where(eq(organizations.id, org.id));

                this.logger.log(`Set parent for organization: ${org.name}`);
            }
        }

        this.logger.log('Organizations seeding completed');
    }
}
