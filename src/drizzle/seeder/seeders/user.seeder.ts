import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { eq } from 'drizzle-orm';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { v4 as uuidv4 } from 'uuid';

import * as schema from '../../../../drizzle/schema';
import { AppModule } from '../../../app.module';
import { KeycloakAdminClientService } from '../../../auth/services/keycloak-admin-client.service';
import { UserStatusEnum } from '../../../domain/users/enums/user-status.enum';
import { users } from '../../../domain/users/user.schema';
import { DrizzleSeeder } from '../drizzle.seeder';

export class UserSeeder extends DrizzleSeeder {
    constructor(protected readonly db: NodePgDatabase<typeof schema>) {
        super(db);
    }

    async run(): Promise<void> {
        this.logger.log('Seeding users...');

        const app = await NestFactory.createApplicationContext(AppModule, {
            logger: ['error'], // Optional: reduce logging noise
        });

        try {
            const keycloakAdminClientService = app.get(KeycloakAdminClientService);
            const configService = app.get(ConfigService);

            const keycloakUsersList = await keycloakAdminClientService.getUsers();
            const now = new Date().toISOString();

            for (const keycloakUser of keycloakUsersList) {
                let userId;
                if (keycloakUser.email === configService.get('KEYCLOAK_USER_EMAIL')) {
                    userId = configService.get('APP_TEST_USER_ID');
                } else {
                    userId = uuidv4();
                }

                // Check if user already exists
                const existingUser = await this.db.query.users.findFirst({
                    where: (fields, { eq }) => eq(fields.keycloakId, keycloakUser.id),
                });

                if (existingUser) {
                    // Update the user
                    await this.db
                        .update(users)
                        .set({
                            email: keycloakUser.email,
                            firstName: keycloakUser.firstName,
                            lastName: keycloakUser.lastName,
                            phone: keycloakUser.attributes?.phone || null,
                            isActive: true,
                            status: UserStatusEnum.ACTIVE,
                            updatedAt: now,
                        })
                        .where(eq(users.keycloakId, keycloakUser.id));

                    this.logger.log(`Updated user: ${keycloakUser.email}`);
                } else {
                    // Insert new user
                    await this.db.insert(users).values({
                        id: userId,
                        keycloakId: keycloakUser.id,
                        email: keycloakUser.email,
                        firstName: keycloakUser.firstName,
                        lastName: keycloakUser.lastName,
                        phone: keycloakUser.attributes?.phone || null,
                        isActive: true,
                        status: UserStatusEnum.ACTIVE,
                        createdAt: now,
                        updatedAt: now,
                        lastLoginAt: null,
                        locale: 'en',
                        timezone: 'UTC',
                    });

                    this.logger.log(`Created user: ${keycloakUser.email}`);
                }
            }

            this.logger.log('Users seeding completed');
        } catch (error) {
            this.logger.error({ err: error }, 'Error seeding users');
            throw error;
        } finally {
            await app.close();
        }
    }
}
