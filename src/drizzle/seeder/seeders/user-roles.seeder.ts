import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { v4 as uuidv4 } from 'uuid';

import * as schema from '../../../../drizzle/schema';
import { AppModule } from '../../../app.module';
import { userRoles } from '../../../domain/users/user-role.schema';
import { DrizzleSeeder } from '../drizzle.seeder';

export class UserRolesSeeder extends DrizzleSeeder {
    constructor(protected readonly db: NodePgDatabase<typeof schema>) {
        super(db);
    }

    async run(): Promise<void> {
        this.logger.log('Seeding user roles...');

        const app = await NestFactory.createApplicationContext(AppModule, {
            logger: ['error'],
        });

        try {
            const configService = app.get(ConfigService);
            const userId = configService.get('APP_TEST_USER_ID');

            // Find the user
            const user = await this.db.query.users.findFirst({
                where: (fields, { eq }) => eq(fields.id, userId),
            });

            if (!user) {
                this.logger.warn(`User with ID ${userId} not found. Skipping role assignment.`);
                return;
            }

            // Get all roles
            const allRoles = await this.db.query.roles.findMany();

            const now = new Date().toISOString();

            // Assign all roles to the user
            for (const role of allRoles) {
                // Check if role assignment already exists
                const existingUserRole = await this.db.query.userRoles.findFirst({
                    where: (fields, { and, eq }) =>
                        and(eq(fields.userId, user.id as string), eq(fields.roleId, role.id as string)),
                });

                if (!existingUserRole) {
                    await this.db.insert(userRoles).values({
                        id: uuidv4(),
                        userId: user.id as string,
                        roleId: role.id as string,
                        createdAt: now,
                    });

                    this.logger.log(`Assigned role '${role.name}' to user ${user.email}`);
                } else {
                    this.logger.log(`User ${user.email} already has role '${role.name}'`);
                }
            }

            this.logger.log('User roles seeding completed');
        } catch (error) {
            this.logger.error({ err: error }, 'Error seeding user roles');
        } finally {
            await app.close();
        }
    }
}
