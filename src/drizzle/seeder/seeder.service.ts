import { Inject, Injectable, Logger } from '@nestjs/common';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

import * as schema from '../../../drizzle/schema';
import { DRIZZLE_PROVIDER } from '../drizzle.provider';
import { DatabaseSeeder } from './seeders/database.seeder';

@Injectable()
export class SeederService {
    private readonly logger = new Logger(SeederService.name);
    private readonly seeders = [DatabaseSeeder];

    constructor(
        @Inject(DRIZZLE_PROVIDER)
        private readonly db: NodePgDatabase<typeof schema>,
    ) {}

    async seed() {
        this.logger.log('Starting database seeding...');

        // Use a transaction for all seeding operations
        await this.db.transaction(async (tx) => {
            for (const Seeder of this.seeders) {
                const seeder = new Seeder(tx);
                this.logger.log(`Running ${Seeder.name}...`);
                await seeder.run();
            }
        });

        this.logger.log('Database seeding completed successfully');
    }

    async runSpecificSeeder(seederName: string) {
        const SeederClass = this.seeders.find((s) => s.name.toLowerCase() === seederName.toLowerCase());

        if (!SeederClass) {
            throw new Error(`Seeder ${seederName} not found`);
        }

        this.logger.log(`Running ${SeederClass.name}...`);
        const seeder = new SeederClass(this.db);
        await seeder.run();
        this.logger.log(`${SeederClass.name} completed successfully`);
    }
}
