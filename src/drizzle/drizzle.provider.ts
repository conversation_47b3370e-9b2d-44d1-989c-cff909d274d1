import { ConfigService } from '@nestjs/config';
import { drizzle } from 'drizzle-orm/node-postgres';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';

import * as schema from '../../drizzle/schema';
import { getDatabaseUrl } from './drizzle-env';

export const DRIZZLE_PROVIDER = 'DRIZZLE_PROVIDER';
export const PG_POOL_PROVIDER = 'PG_POOL_PROVIDER';

export const pgPoolProvider = {
    provide: PG_POOL_PROVIDER,
    inject: [ConfigService],
    useFactory: async (configService: ConfigService) => {
        const connectionString = configService.get<string>('DATABASE_URL') || getDatabaseUrl();
        return new Pool({
            connectionString,
        });
    },
};

export const drizzleProvider = {
    provide: DRIZZLE_PROVIDER,
    inject: [PG_POOL_PROVIDER],
    useFactory: async (pool: Pool) => {
        return drizzle(pool, { schema }) as NodePgDatabase<typeof schema>;
    },
};
