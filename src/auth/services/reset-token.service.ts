import { Injectable, Logger } from '@nestjs/common';
import { createHash, randomBytes } from 'crypto';
import { DateTime } from 'luxon';

import { TokenHasExpiredException } from '../exceptions/token-has-expired.exception';
import { TokenNotFoundException } from '../exceptions/token-not-found.exception';
import { ResetTokenRepository } from '../repositories';

@Injectable()
export class ResetTokenService {
    private readonly logger = new Logger(ResetTokenService.name);

    private readonly DEFAULT_TOKEN_EXPIRATION_IN_MINUTES = 30;

    constructor(private readonly resetTokenRepository: ResetTokenRepository) {}

    async createResetToken({ userId }: { userId: string }) {
        const token = randomBytes(32).toString('hex');
        const hashedToken = this.hashToken(token);

        const resetToken = await this.resetTokenRepository.createResetToken({
            userId,
            token: hashedToken,
            expiresAt: DateTime.now().plus({ minutes: this.DEFAULT_TOKEN_EXPIRATION_IN_MINUTES }).toJSDate(),
        });

        return {
            resetToken,
            token,
        };
    }

    async getToken({ token }: { token: string }) {
        const hashedToken = this.hashToken(token);

        const resetToken = await this.resetTokenRepository.findOne({
            token: hashedToken,
        });
        if (!resetToken) {
            throw new TokenNotFoundException();
        }

        const expiresAt = DateTime.fromJSDate(resetToken.expiresAt);
        const now = DateTime.now();

        const isValid = now < expiresAt;

        if (!isValid) {
            throw new TokenHasExpiredException();
        }

        return resetToken;
    }

    async deleteToken(id: string): Promise<void> {
        const token = await this.resetTokenRepository.findOne({
            id: id,
        });

        if (!token) {
            throw new TokenNotFoundException();
        }

        await this.resetTokenRepository.nativeDelete({
            id: token.id,
        });
    }

    private hashToken(token: string): string {
        return createHash('sha256').update(token).digest('hex');
    }
}
