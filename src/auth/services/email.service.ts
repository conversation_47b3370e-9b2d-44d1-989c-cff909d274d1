import { Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { EmailTemplateEnum } from '../../domain/common/enums';
import { EmailTemplateRepository } from '../../domain/common/repositories';
import { EmailService as CommonEmailService } from '../../domain/common/services';

interface PasswordResetParams {
    email: string;
    uri: string;
    locale: string;
}

@Injectable()
export class EmailService {
    private readonly logger = new Logger('AuthEmailService');

    constructor(
        @Inject(CommonEmailService)
        private readonly emailService: CommonEmailService,
        private readonly configService: ConfigService,
        private readonly emailTemplateRepository: EmailTemplateRepository,
    ) {}

    public async passwordReset({ email, uri, locale }: PasswordResetParams) {
        try {
            this.logger.log(`Sending password reset email to ${email}`);

            try {
                await this.emailService.send({
                    toEmails: [email],
                    locale,
                    templateName: EmailTemplateEnum.PASSWORD_RESET,
                    tags: ['Auth'],
                    params: {
                        url: uri,
                    },
                });
            } catch (error) {
                this.logger.error({ err: error, email }, `Error sending password reset email to ${email}`);
                throw error;
            }
        } catch (error) {
            this.logger.error({ err: error, email }, `Error sending password reset email to ${email}`);
        }
    }
}
