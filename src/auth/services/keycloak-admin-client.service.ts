import KcAdminClient from '@keycloak/keycloak-admin-client';
import UserRepresentation from '@keycloak/keycloak-admin-client/lib/defs/userRepresentation';
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { RequiredActionAlias } from 'keycloak-admin/lib/defs/requiredActionProviderRepresentation';

import { PasswordAndConfirmationMismatchException } from '../exceptions/password-and-confirmation-mismatch.exception';

@Injectable()
export class KeycloakAdminClientService implements OnModuleInit {
    private readonly logger = new Logger(KeycloakAdminClientService.name);
    private client: KcAdminClient;
    private lastAuthTime: number = 0;
    private readonly TOKEN_REFRESH_INTERVAL = 240 * 1000;
    private readonly MAX_RETRIES = 3;

    constructor(private configService: ConfigService) {}

    /**
     * Hack to make ES modules to work in NestJS
     * @see https://stackoverflow.com/a/78267056/563049
     *
     * NOTE: @keycloak/keycloak-admin-client in nestjs
     * https://github.com/nestjs/nest/issues/7021
     * https://github.com/keycloak/keycloak-nodejs-admin-client/issues/523
     */
    private dynamicKeycloakImport = async () => new Function("return import('@keycloak/keycloak-admin-client')")();

    async onModuleInit() {
        await this.initAdmin();
    }

    private async initAdmin() {
        const dynamicKeycloakAdminClient = (await this.dynamicKeycloakImport()).default;

        const keycloakBaseUrl = this.configService.get('KEYCLOAK_BASE_URL');

        const keycloakHost = this.configService.get('KEYCLOAK_ORIGINAL_HOST');
        const keycloakPort = this.configService.get('KEYCLOAK_PORT');
        const keycloakRealm = this.configService.get('KEYCLOAK_REALM');

        this.client = new dynamicKeycloakAdminClient({
            baseUrl: keycloakBaseUrl,
            realmName: keycloakRealm,
        });

        await this.authenticate();
    }

    private async authenticate() {
        await this.client.auth({
            grantType: 'client_credentials',
            clientId: this.configService.get('KEYCLOAK_CLIENT_ID'),
            clientSecret: this.configService.get('KEYCLOAK_CLIENT_SECRET'),
        });
        this.lastAuthTime = Date.now();
    }

    private async ensureAuthenticated() {
        const now = Date.now();
        if (now - this.lastAuthTime >= this.TOKEN_REFRESH_INTERVAL) {
            await this.authenticate();
        }
    }

    private async executeWithRetry<T>(operation: () => Promise<T>): Promise<T> {
        let lastError;
        for (let i = 0; i < this.MAX_RETRIES; i++) {
            try {
                await this.ensureAuthenticated();
                return await operation();
            } catch (error) {
                lastError = error;
                if (error?.response?.status === 401) {
                    await this.authenticate();
                    continue;
                }
                throw error;
            }
        }
        throw lastError;
    }

    public async getClient(): Promise<KcAdminClient> {
        await this.ensureAuthenticated();
        return this.client;
    }

    public async changePassword({ keycloakUserId, newPassword, confirmPassword }): Promise<void> {
        if (newPassword !== confirmPassword) {
            throw new PasswordAndConfirmationMismatchException();
        }

        try {
            return this.executeWithRetry(async () => {
                await this.client.users.update(
                    {
                        id: keycloakUserId,
                        realm: this.configService.get('KEYCLOAK_REALM'),
                    },
                    {
                        credentials: [
                            {
                                type: 'password',
                                temporary: false,
                                value: newPassword,
                            },
                        ],
                    },
                );
            });
        } catch (e) {
            this.logger.error({ err: e }, 'Error while changing password');
            throw e;
        }
    }

    public async getUsers(): Promise<UserRepresentation[]> {
        const keycloakRealm = this.configService.get('KEYCLOAK_REALM');
        return await this.executeWithRetry(() =>
            this.client.users.find({
                realm: keycloakRealm,
            }),
        );
    }

    public async getUser(keycloakUserId: string): Promise<UserRepresentation | null> {
        const keycloakRealm = this.configService.get('KEYCLOAK_REALM');
        try {
            return await this.executeWithRetry(() =>
                this.client.users.findOne({
                    realm: keycloakRealm,
                    id: keycloakUserId,
                }),
            );
        } catch (e) {
            return null;
        }
    }

    public async getUserByEmail(email: string): Promise<UserRepresentation | null> {
        const keycloakRealm = this.configService.get('KEYCLOAK_REALM');
        const normalizedEmail = email.toLowerCase();
        try {
            const users = await this.executeWithRetry(() =>
                this.client.users.find({ email: normalizedEmail, realm: keycloakRealm }),
            );
            return users[0];
        } catch (e: unknown) {
            return null;
        }
    }

    public async updatePasswordEmail(keycloakId: string): Promise<void> {
        try {
            await this.executeWithRetry(() =>
                this.client.users.executeActionsEmail({
                    id: keycloakId,
                    actions: [RequiredActionAlias.UPDATE_PASSWORD],
                    lifespan: 900, // Link expires in 15 minutes
                }),
            );
        } catch (e) {
            this.logger.error({ err: e }, 'Error while forcing password reset');
            throw e;
        }
    }

    public async updateUser(userId: string, payload: UserRepresentation): Promise<UserRepresentation> {
        await this.executeWithRetry(() =>
            this.client.users.update(
                {
                    realm: this.configService.get('KEYCLOAK_REALM'),
                    id: userId,
                },
                {
                    ...payload,
                    requiredActions: payload.requiredActions || [],
                },
            ),
        );

        // Get latest user data
        return await this.getUser(userId);
    }

    public async signIn({ email, password }: { email: string; password: string }) {
        const normalizedEmail = email.toLowerCase();
        const url = `${this.configService.get('KEYCLOAK_BASE_URL')}/realms/${this.configService.get('KEYCLOAK_REALM')}/protocol/openid-connect/token`;
        const response = await axios.post(
            url,
            new URLSearchParams({
                client_id: this.configService.get('KEYCLOAK_CLIENT_ID'),
                client_secret: this.configService.get('KEYCLOAK_CLIENT_SECRET'),
                grant_type: 'password',
                username: normalizedEmail,
                password: password,
            }).toString(),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } },
        );

        return response.data;
    }

    public async getClientId(): Promise<string> {
        const keycloakRealm = this.configService.get('KEYCLOAK_REALM');
        const clientId = this.configService.get('KEYCLOAK_CLIENT_ID');

        const clients = await this.executeWithRetry(() =>
            this.client.clients.find({
                clientId: clientId,
                realm: keycloakRealm,
            }),
        );

        if (!clients || clients.length === 0) {
            throw new Error('Client not found');
        }

        return clients[0].id;
    }
}
