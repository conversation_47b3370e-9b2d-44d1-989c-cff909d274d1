import { BadRequestException } from '@nestjs/common';
import { I18nContext } from 'nestjs-i18n';

import { I18nPath } from '../../../i18n/generated/i18n.generated';

export class InvalidOrganizationIdException extends BadRequestException {
    constructor(organizationId: string) {
        super(
            I18nContext.current().service.t<I18nPath>('errors.ORGANIZATION.INVALID_ORGANIZATION_ID', {
                args: {
                    id: organizationId,
                },
            }),
        );
    }
}
