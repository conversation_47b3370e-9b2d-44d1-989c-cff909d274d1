import { HttpStatus, UnauthorizedException } from '@nestjs/common';
import { I18nContext } from 'nestjs-i18n';

import { I18nPath } from '../../../i18n/generated/i18n.generated';

export class UserRequiresPasswordResetException extends UnauthorizedException {
    constructor(errorCode: string = 'AUTH.USER_REQUIRES_PASSWORD_RESET') {
        const message = I18nContext.current().t<I18nPath>('errors.AUTH.USER_REQUIRES_PASSWORD_RESET');

        super({
            message,
            errorCode,
            statusCode: HttpStatus.UNAUTHORIZED,
        });
    }
}
