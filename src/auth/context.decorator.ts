import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { isUUID } from 'class-validator';

import { RequestInterface } from '../domain/common/interfaces';
import { InvalidOrganizationIdException } from './exceptions/invalid-organization-id.exception';
import { OrganizationIdNotProvidedInHeadersException } from './exceptions/organization-id-not-provided-in-headers.exception';

export interface RequestContextOptions {
    requireOrganization?: boolean;
}

export const RequestContext = createParamDecorator(
    async (options: RequestContextOptions = {}, ctx: ExecutionContext) => {
        const request = ctx.switchToHttp().getRequest<RequestInterface>();
        const organizationId = request.headers['x-organization-id'] as string;

        if (organizationId && !isUUID(organizationId)) {
            throw new InvalidOrganizationIdException(organizationId);
        }

        if (options.requireOrganization && !organizationId) {
            throw new OrganizationIdNotProvidedInHeadersException();
        }

        return request.context;
    },
);
