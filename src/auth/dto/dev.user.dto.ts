import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsString } from 'class-validator';

export class DevUserDto {
    @ApiProperty({
        description: 'Email address of the user to authenticate',
        example: process.env.KEYCLOAK_USER_EMAIL,
        format: 'email',
        required: true,
    })
    @IsEmail({}, { message: 'Please provide a valid email address' })
    @IsNotEmpty({ message: 'Email is required' })
    email: string;

    @ApiProperty({
        description: 'Role ID to assign to the user',
        example: '1',
        required: true,
    })
    @IsString({ message: 'Role ID must be a string' })
    @IsNotEmpty({ message: 'Role ID is required' })
    roleId: string;
}
