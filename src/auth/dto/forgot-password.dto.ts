import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

export const ForgotPasswordSchema = extendApi(
    z.object({
        email: extendApi(z.string().email().describe('Email')),
    }),
    {
        title: 'ForgotPasswordSchema',
        description: 'Sign in model',
    },
);

export class ForgotPasswordDto extends createZodDto(ForgotPasswordSchema) {}
