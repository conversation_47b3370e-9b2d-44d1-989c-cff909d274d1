import { createZodDto } from '@anatine/zod-nestjs';
import { extendApi } from '@anatine/zod-openapi';
import { z } from 'zod';

export const SignInSchema = extendApi(
    z.object({
        email: extendApi(
            z
                .string()
                .email()
                .describe('Email')
                .transform((val) => val.toLowerCase()),
        ),
        password: extendApi(z.string().describe('Password')),
    }),
    {
        title: 'SignInSchema',
        description: 'Sign in model',
    },
);

export class SignInDto extends createZodDto(SignInSchema) {}
