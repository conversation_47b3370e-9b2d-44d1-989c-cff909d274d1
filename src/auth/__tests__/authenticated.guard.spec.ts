import { faker } from '@faker-js/faker';
import { ExecutionContext } from '@nestjs/common';

import { mockRequestFactory } from '../../../test/mocks/mock-request.factory';
import { InvalidOrganizationIdFormatException } from '../../domain/common/exceptions/invalid-organization-id-format.exception';
import { AuthenticatedGuard } from '../authenticated.guard';
import { AuthenticationTokenMissingException } from '../exceptions/authentication-token-missing.exception';
import { FailedToAuthenticateUserContextException } from '../exceptions/failed-to-authenticate-user-context.exception';

class AuthenticatedGuardMockDeps {
    readonly userService = {
        getUserByKeycloakId: jest.fn(),
    };

    readonly permissionsService = {
        getUserPermissionsInOrganizationByKeycloakId: jest.fn(),
    };

    readonly organizationService = {
        getAllowedEndUserIdsByKeycloakIdAndResellerId: jest.fn(),
        getEntityIdsByOrganizationId: jest.fn(),
        getAdvPermFlagOrganizationId: jest.fn(),
    };

    readonly configService = {
        get: jest.fn(),
    };

    readonly reflector = {
        get: jest.fn(),
    };

    readonly jwtService = {
        verifyAsync: jest.fn(),
    };
}

const createMockExecutionContext = (req: Partial<Request>): ExecutionContext =>
    ({
        switchToHttp: () => ({
            getRequest: () => req,
        }),
        getHandler: jest.fn(),
    }) as unknown as ExecutionContext;

jest.mock('../jwt-utils', () => ({
    formatRsaPublicKey: jest.fn(() => '-----BEGIN PUBLIC KEY-----MOCKED-----END PUBLIC KEY-----'),
}));
jest.mock('nestjs-i18n', () => ({
    I18nContext: {
        current: jest.fn(() => ({
            t: jest.fn((key: string) => key),
        })),
    },
}));

describe('AuthenticatedGuard', () => {
    let guard: AuthenticatedGuard;
    let deps: AuthenticatedGuardMockDeps;

    beforeEach(() => {
        deps = new AuthenticatedGuardMockDeps();
        guard = new AuthenticatedGuard(
            deps.userService as any,
            deps.permissionsService as any,
            deps.organizationService as any,
            deps.configService as any,
            deps.reflector as any,
            deps.jwtService as any,
        );
    });

    it('should allow request if guard is disabled', async () => {
        deps.reflector.get.mockReturnValue(true);
        const ctx = createMockExecutionContext(
            mockRequestFactory({
                headers: {
                    'x-correlation-id': faker.string.alphanumeric(),
                },
            }),
        );
        const result = await guard.canActivate(ctx);
        expect(result).toBe(true);
    });

    it('should throw Unauthorized if no Authorization header', async () => {
        const ctx = createMockExecutionContext(mockRequestFactory());
        await expect(guard.canActivate(ctx)).rejects.toThrow(AuthenticationTokenMissingException);
    });

    it('should throw Unauthorized if token verification fails', async () => {
        deps.jwtService.verifyAsync.mockRejectedValue(new Error('Invalid token'));
        deps.configService.get.mockReturnValue(faker.string.alpha());

        const ctx = createMockExecutionContext(
            mockRequestFactory({
                headers: {
                    authorization: 'Token',
                },
            }),
        );

        await expect(guard.canActivate(ctx)).rejects.toThrow(AuthenticationTokenMissingException);
    });

    it('should throw BadRequest if organizationId is not a UUID', async () => {
        const mockToken = faker.string.alphanumeric(20);
        const mockSub = faker.string.uuid();

        deps.configService.get.mockReturnValue(faker.string.alphanumeric(128));
        deps.jwtService.verifyAsync.mockResolvedValue({ sub: mockSub });
        deps.reflector.get.mockReturnValue(false);

        const ctx = createMockExecutionContext(
            mockRequestFactory({
                headers: {
                    authorization: `Bearer ${mockToken}`,
                },
                query: {
                    organizationId: 'invalid-uuid',
                },
            }),
        );

        await expect(guard.canActivate(ctx)).rejects.toThrow(InvalidOrganizationIdFormatException);
    });

    it('should throw Unauthorized if user is not found', async () => {
        deps.jwtService.verifyAsync.mockResolvedValue({ sub: faker.string.uuid() });
        deps.configService.get.mockReturnValue(faker.string.alphanumeric());
        deps.organizationService.getEntityIdsByOrganizationId.mockResolvedValue([faker.string.uuid()]);
        deps.organizationService.getAdvPermFlagOrganizationId.mockResolvedValue(true);
        deps.userService.getUserByKeycloakId.mockResolvedValue(null);

        const ctx = createMockExecutionContext(
            mockRequestFactory({
                headers: {
                    authorization: `Bearer ${faker.string.alphanumeric()}`,
                },
                query: {
                    organizationId: faker.string.uuid(),
                },
            }),
        );

        await expect(guard.canActivate(ctx)).rejects.toThrow(FailedToAuthenticateUserContextException);
    });

    it('should pass and build context when token and user are valid', async () => {
        const mockUser = { id: faker.string.uuid() };
        const mockPermissions = [faker.string.alpha()];
        const mockResellerIds = [faker.string.uuid()];
        const mockEndUserIds = [faker.string.uuid()];
        const organizationId = faker.string.uuid();

        deps.jwtService.verifyAsync.mockResolvedValue({ sub: faker.string.numeric() });
        deps.configService.get.mockReturnValue(faker.string.alpha());
        deps.userService.getUserByKeycloakId.mockResolvedValue(mockUser);
        deps.permissionsService.getUserPermissionsInOrganizationByKeycloakId.mockResolvedValue(mockPermissions);
        deps.organizationService.getEntityIdsByOrganizationId.mockResolvedValue(mockResellerIds);
        deps.organizationService.getAdvPermFlagOrganizationId.mockResolvedValue(true);
        deps.organizationService.getAllowedEndUserIdsByKeycloakIdAndResellerId.mockResolvedValue(mockEndUserIds);

        const request: any = mockRequestFactory({
            headers: {
                authorization: `Bearer ${faker.string.alphanumeric()}`,
            },
            query: {
                organizationId: organizationId,
            },
        });

        const ctx = createMockExecutionContext(request);

        const result = await guard.canActivate(ctx);
        expect(result).toBe(true);
        expect(request.context).toEqual({
            user: mockUser,
            organizationId: organizationId,
            permissions: mockPermissions,
            entityId: mockResellerIds[0],
            hasAdvancedPermissions: true,
            resellerIds: mockResellerIds,
            endUserIds: mockEndUserIds,
        });
    });
});
