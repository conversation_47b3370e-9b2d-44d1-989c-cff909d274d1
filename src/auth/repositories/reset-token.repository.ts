import { Injectable } from '@nestjs/common';

import { BaseRepository } from '../../domain/common/repositories/base.repository';
import { ResetToken } from '../entities';

@Injectable()
export class ResetTokenRepository extends BaseRepository<ResetToken> {
    async createResetToken({
        userId,
        token,
        expiresAt,
    }: {
        userId: string;
        token: string;
        expiresAt: Date;
    }): Promise<ResetToken> {
        const entry = new ResetToken();
        entry.userId = userId;
        entry.token = token;
        entry.expiresAt = expiresAt;

        await this.em.persistAndFlush(entry);

        return entry;
    }
}
