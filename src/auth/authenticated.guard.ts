import { CanActivate, ExecutionContext, Injectable, Logger, SetMetadata } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Reflector } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { isUUID } from 'class-validator';
import { Request } from 'express';

import { InvalidOrganizationIdFormatException } from '../domain/common/exceptions/invalid-organization-id-format.exception';
import { Context } from '../domain/common/interfaces';
import { OrganizationsService } from '../domain/organizations/services/organizations.service';
import { UserNotFoundException } from '../domain/users/exceptions/user-not-found.exception';
import { UserService } from '../domain/users/services/users.service';
import { PermissionsService } from '../rbac/permissions.service';
import { AuthenticationTokenMissingException } from './exceptions/authentication-token-missing.exception';
import { FailedToAuthenticateUserContextException } from './exceptions/failed-to-authenticate-user-context.exception';
import { FailedToFetchUserDataForContextException } from './exceptions/failed-to-fetch-user-data-for-context.exception';
import { InvalidOrExpiredAuthTokenException } from './exceptions/invalid-or-expired-auth-token.exception';
import { formatRsaPublicKey } from './jwt-utils';

interface JwtPayload {
    sub: string;
    exp?: number;
    iat?: number;

    [key: string]: any;
}

interface AuthenticatedRequest extends Request {
    user: JwtPayload;
    access_token: string;
    organizationId?: string;
    context?: Context;
}

type User = any;
type Permission = any;
type ResellerId = string | null;
type HasAdvancedPermissions = boolean;

export const DisableAuthenticatedGuard = () => SetMetadata('disableAuthenticatedGuard', true);

@Injectable()
export class AuthenticatedGuard implements CanActivate {
    private readonly logger = new Logger(AuthenticatedGuard.name);
    private cachedPublicKey: string | null = null;

    constructor(
        private userService: UserService,
        private permissionsService: PermissionsService,
        private organizationService: OrganizationsService,
        private configService: ConfigService,
        private reflector: Reflector,
        private jwtService: JwtService,
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest<AuthenticatedRequest>();
        const correlationId = request.headers['x-correlation-id'] as string;
        const method = request.method;
        const url = request.url;

        this.logger.log(`Authentication check started for ${method} ${url}`, { correlationId });

        if (this.isGuardDisabled(context)) {
            this.logger.debug('Authentication guard is disabled for this route', { correlationId, method, url });
            return true;
        }

        const token = this.extractTokenFromHeader(request);
        if (!token) {
            this.logger.warn('Authentication failed: No token found in the request', { correlationId, method, url });
            throw new AuthenticationTokenMissingException();
        }

        const payload = await this.verifyToken(token);
        this.logger.debug('Token verified successfully', { correlationId, userId: payload.sub });

        request.user = payload;
        request.access_token = token;

        const organizationId = this.extractOrganizationId(request);
        if (organizationId) {
            this.logger.debug('Organization ID found in request', { correlationId, organizationId });
            this.validateOrganizationId(organizationId);
        }

        await this.buildUserContext(request, payload.sub, organizationId);

        this.logger.log(`Authentication successful for user ${payload.sub}`, {
            correlationId,
            userId: payload.sub,
            organizationId,
            method,
            url,
        });

        return true;
    }

    private async buildUserContext(
        request: AuthenticatedRequest,
        keycloakId: string,
        organizationId?: string,
    ): Promise<void> {
        const correlationId = request.headers['x-correlation-id'] as string;
        this.logger.debug('Building user context', { correlationId, keycloakId, organizationId });

        try {
            const [user, permissions, resellerIds, hasAdvancedPermissions] = await this.fetchContextData(
                keycloakId,
                organizationId,
            );

            if (!user) {
                this.logger.error('User not found in database', { correlationId, keycloakId });
                throw new UserNotFoundException();
            }

            this.logger.debug('User context data fetched', {
                correlationId,
                userId: user.id,
                permissionsCount: permissions.length,
                hasAdvancedPermissions,
            });

            let endUserIds: string[] | null = null;
            if (hasAdvancedPermissions) {
                endUserIds = await this.organizationService.getAllowedEndUserIdsByKeycloakIdAndResellerId(
                    keycloakId,
                    resellerIds,
                );
                this.logger.debug('Advanced permissions: fetched end user IDs', {
                    correlationId,
                    endUserIdsCount: endUserIds?.length || 0,
                });
            }

            const internalContext: Context = {
                user,
                organizationId,
                permissions,
                entityId: resellerIds[0],
                hasAdvancedPermissions: hasAdvancedPermissions,
                resellerIds: resellerIds,
                endUserIds: endUserIds,
            };

            request.context = internalContext;
            this.logger.debug('User context built successfully', { correlationId, userId: user.id });
        } catch (error) {
            this.logger.error('Failed to build user context', {
                correlationId,
                keycloakId,
                organizationId,
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined,
            });
            throw new FailedToAuthenticateUserContextException();
        }
    }

    private isGuardDisabled(context: ExecutionContext): boolean {
        return this.reflector.get<boolean>('disableAuthenticatedGuard', context.getHandler()) === true;
    }

    private validateOrganizationId(organizationId: string): void {
        if (!isUUID(organizationId)) {
            this.logger.error('Invalid organization ID format', { organizationId });
            throw new InvalidOrganizationIdFormatException(organizationId);
        }
    }

    private async verifyToken(token: string): Promise<JwtPayload> {
        try {
            const publicKey = this.getPublicKey();
            return await this.jwtService.verifyAsync<JwtPayload>(token, {
                secret: publicKey,
                algorithms: ['RS256'],
            });
        } catch (err: unknown) {
            this.logger.error('Invalid or expired token', err);
            throw new InvalidOrExpiredAuthTokenException();
        }
    }

    private async fetchContextData(
        keycloakId: string,
        organizationId?: string,
    ): Promise<[User, Permission[], ResellerId[], HasAdvancedPermissions]> {
        try {
            const promises = [
                this.userService.getUserByKeycloakId(keycloakId),
                this.permissionsService.getUserPermissionsInOrganizationByKeycloakId(keycloakId, organizationId),
                organizationId
                    ? this.organizationService.getEntityIdsByOrganizationId(organizationId)
                    : Promise.resolve(null),
                organizationId
                    ? this.organizationService.getAdvPermFlagOrganizationId(organizationId, keycloakId)
                    : Promise.resolve(false),
            ];

            return Promise.all(promises) as Promise<[User, Permission[], ResellerId[], HasAdvancedPermissions]>;
        } catch (error) {
            this.logger.error(`Failed to fetch context data for user ${keycloakId}`, error);
            throw new FailedToFetchUserDataForContextException();
        }
    }

    private extractTokenFromHeader(request: Request): string | undefined {
        const [type, token] = request.headers['authorization']?.split(' ') ?? [];
        return type === 'Bearer' ? token : undefined;
    }

    private getPublicKey(): string {
        if (this.cachedPublicKey) {
            return this.cachedPublicKey;
        }

        const publicKey = this.configService.get<string>('KEYCLOAK_REALM_RSA_PUBLIC_KEY');
        if (!publicKey) {
            this.logger.error('KEYCLOAK_REALM_RSA_PUBLIC_KEY is not defined');
            throw new Error('KEYCLOAK_REALM_RSA_PUBLIC_KEY is not defined');
        }

        try {
            this.cachedPublicKey = formatRsaPublicKey(publicKey);
            return this.cachedPublicKey;
        } catch (error) {
            this.logger.error('Failed to format RSA public key', error);
            throw error;
        }
    }

    private extractOrganizationId(request: AuthenticatedRequest): string | undefined {
        return (
            request.params?.organizationId ||
            request.query?.organizationId ||
            request.body?.organizationId ||
            (request.headers['x-organization-id'] as string)
        );
    }
}
