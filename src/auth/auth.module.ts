import { MikroOrmModule } from '@mikro-orm/nestjs';
import { DynamicModule, Module, Type } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule, JwtService } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';

import { MetricsModule } from '../domain/metrics/metrics.module';
import { OrganizationsModule } from '../domain/organizations/organizations.module';
import { UserService } from '../domain/users/services/users.service';
import { UsersModule } from '../domain/users/users.module';
import { RbacModule } from '../rbac/rbac.module';
import { AuthController, DevAuthController } from './controllers';
import { ResetToken } from './entities';
import { formatRsaPublicKey } from './jwt-utils';
import { buildOpenIdClient, OidcStrategy } from './oidc.strategy';
import { EmailService, KeycloakAdminClientService, ResetTokenService } from './services';
import { SessionSerializer } from './session.serializer';

const OidcStrategyFactory = {
    provide: 'OidcStrategy',
    useFactory: async () => {
        const client = await buildOpenIdClient(); // secret sauce! build the dynamic client before injecting it into the strategy for use in the constructor super call.
        return new OidcStrategy(client);
    },
    inject: [],
};

@Module({})
export class AuthModule {
    static register(): DynamicModule {
        const controllers: Type<AuthController | DevAuthController>[] = [AuthController];

        if (process.env.NODE_ENV === 'local') {
            controllers.push(DevAuthController);
        }

        return {
            module: AuthModule,
            imports: [
                ConfigModule.forRoot(),
                MikroOrmModule.forFeature([ResetToken]),
                PassportModule.register({
                    session: true,
                    defaultStrategy: 'oidc',
                }),
                JwtModule.registerAsync({
                    imports: [ConfigModule],
                    inject: [ConfigService],
                    global: true,
                    useFactory: (configService: ConfigService) => {
                        const publicKeyRaw = configService.get<string>('KEYCLOAK_REALM_RSA_PUBLIC_KEY');
                        if (!publicKeyRaw) {
                            throw new Error('KEYCLOAK_REALM_RSA_PUBLIC_KEY is not defined');
                        }

                        const formattedKey = formatRsaPublicKey(publicKeyRaw);

                        return {
                            secret: formattedKey,
                            verifyOptions: {
                                algorithms: ['RS256'],
                            },
                        };
                    },
                }),
                RbacModule,
                MetricsModule,
                UsersModule,
                OrganizationsModule,
            ],
            controllers: controllers,
            providers: [
                OidcStrategyFactory,
                SessionSerializer,
                JwtService,
                KeycloakAdminClientService,
                UserService,
                ResetTokenService,
                EmailService,
            ],
            exports: [],
        };
    }
}
