import { Body, Controller, Post, Res } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ApiBody, ApiOperation } from '@nestjs/swagger';
import { Response } from 'express';

import { BaseController } from '../../domain/common/controllers/base.controller';
import { MetricsService } from '../../domain/metrics/metrics.service';
import { UserNotFoundException } from '../../domain/users/exceptions/user-not-found.exception';
import { UserNotFoundOnKeycloakException } from '../../domain/users/exceptions/user-not-found-on-keycloak.exception';
import { UserService } from '../../domain/users/services/users.service';
import { DisablePermissionsGuard } from '../../rbac/permissions.decorator';
import { DisableAuthenticatedGuard } from '../authenticated.guard';
import { DevTokenRequestDto } from '../dto/dev.token.dto';
import { DevUserDto } from '../dto/dev.user.dto';
import { buildOpenIdClient } from '../oidc.strategy';
import { KeycloakAdminClientService } from '../services/keycloak-admin-client.service';

@Controller('auth/dev')
export class DevAuthController extends BaseController {
    constructor(
        private keycloakAdminClientService: KeycloakAdminClientService,
        private configService: ConfigService,
        private metricsService: MetricsService,
        private userService: UserService,
    ) {
        super();
    }

    @DisableAuthenticatedGuard()
    @DisablePermissionsGuard()
    @Post('token')
    @ApiOperation({ summary: 'Get development authentication tokens' })
    @ApiBody({
        type: DevTokenRequestDto,
        description: 'Email of the user to authenticate',
        examples: {
            example1: {
                summary: 'Standard Request',
                description: 'Request authentication tokens for a development user',
                value: {
                    email: process.env.KEYCLOAK_USER_EMAIL || '<EMAIL>',
                },
            },
        },
    })
    async getDevToken(@Body() body: DevTokenRequestDto, @Res() res: Response) {
        const keycloakRealm = this.configService.get('KEYCLOAK_REALM');
        const adminClient = await this.keycloakAdminClientService.getClient();

        // Find the user
        const users = await adminClient.users.find({
            realm: keycloakRealm,
            email: body.email,
            max: 1,
        });

        if (!users || users.length === 0) {
            throw new UserNotFoundException();
        }

        const user = users[0];

        // Set a temporary password
        const tempPassword = 'temp-' + Math.random().toString(36).slice(-8);
        await adminClient.users.resetPassword({
            realm: keycloakRealm,
            id: user.id,
            credential: {
                temporary: false, // Changed to false to avoid requiring password change
                type: 'password',
                value: tempPassword,
            },
        });

        // Get token using password grant
        const client = await buildOpenIdClient();
        const tokenSet = await client.grant({
            grant_type: 'password',
            username: body.email,
            password: tempPassword,
            client_id: this.configService.get('KEYCLOAK_CLIENT_ID'),
            client_secret: this.configService.get('KEYCLOAK_CLIENT_SECRET'),
            scope: 'openid profile email offline_access',
        });

        // Reset password to a random value for security
        await adminClient.users.resetPassword({
            realm: keycloakRealm,
            id: user.id,
            credential: {
                temporary: false,
                type: 'password',
                value: this.configService.get('KEYCLOAK_USER_PASSWORD'),
            },
        });

        await this.updateMetrics();

        return this.sendOk(res, {
            access_token: tokenSet.access_token,
            refresh_token: tokenSet.refresh_token,
            id_token: tokenSet.id_token,
            expires_in: tokenSet.expires_in,
        });
    }

    @DisableAuthenticatedGuard()
    @DisablePermissionsGuard()
    @Post('link-user')
    async linkUser(@Body() body: DevUserDto, @Res() res: Response) {
        const keycloakRealm = this.configService.get('KEYCLOAK_REALM');
        const adminClient = await this.keycloakAdminClientService.getClient();
        // TODO: Find the user in keycloak
        const keycloakUser = await adminClient.users.find({
            realm: keycloakRealm,
            email: body.email,
            max: 1,
        });

        if (!keycloakUser || keycloakUser.length === 0) {
            throw new UserNotFoundOnKeycloakException();
        }

        const keycloakUserId = keycloakUser[0].id;
        // TODO: Find the user in the database
        const user = await this.userService.findOrCreateUser(keycloakUserId, body.email);
        //TODO: assign user to a role
        await this.userService.assignRoleToUser(user.id, body.roleId);

        return this.sendOk(res, { message: 'User linked successfully' });
    }

    private async updateMetrics() {
        await Promise.all([
            this.metricsService.updateSessionCount().catch((error) => {
                console.error('Failed to update session count:', error);
            }),
            this.metricsService.updateOfflineSessionCount().catch((error) => {
                console.error('Failed to update offline session count:', error);
            }),
        ]);
    }
}
