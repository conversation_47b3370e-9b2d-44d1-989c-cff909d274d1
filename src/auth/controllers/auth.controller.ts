import { BadRequestException, Body, Controller, Get, Logger, NotFoundException, Post, Res } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Response } from 'express';

import { BaseController } from '../../domain/common/controllers/base.controller';
import { ErrorProcessingRequestException } from '../../domain/common/exceptions/error-processing-request.exception';
import { MetricsService } from '../../domain/metrics/metrics.service';
import { OrganizationsService } from '../../domain/organizations/services';
import { UserService } from '../../domain/users/services';
import { DisablePermissionsGuard } from '../../rbac/permissions.decorator';
import { AccessToken } from '../access-token.decorator';
import { DisableAuthenticatedGuard } from '../authenticated.guard';
import { ForgotPasswordDto, ResetPasswordDto, SignInDto } from '../dto';
import { InvalidCredentialsException } from '../exceptions/invalid-credentials.exception';
import { InvalidRefreshTokenException } from '../exceptions/invalid-refresh-token.exception';
import { SessionIsNotActiveException } from '../exceptions/session-is-not-active.exception';
import { UserDoesntHaveAnyOrganizationsException } from '../exceptions/user-doesnt-have-any-organizations.exception';
import { UserNotFoundOrInactiveException } from '../exceptions/user-not-found-or-inactive.exception';
import { UserRequiresPasswordResetException } from '../exceptions/user-requires-password-reset.exception';
import { buildOpenIdClient } from '../oidc.strategy';
import { EmailService, ResetTokenService } from '../services';
import { KeycloakAdminClientService } from '../services/keycloak-admin-client.service';

@Controller('auth')
export class AuthController extends BaseController {
    private readonly logger = new Logger(AuthController.name);

    constructor(
        private readonly keycloakAdminClientService: KeycloakAdminClientService,
        private readonly resetTokenService: ResetTokenService,
        private readonly configService: ConfigService,
        private readonly metricsService: MetricsService,
        private readonly userService: UserService,
        private readonly emailService: EmailService,
        private readonly organizationService: OrganizationsService,
    ) {
        super();
    }

    @DisableAuthenticatedGuard()
    @DisablePermissionsGuard()
    @Get('get-login-url')
    async getLoginUrl(@Res() res: Response) {
        const client = await buildOpenIdClient();
        const url = client.authorizationUrl({
            redirect_uri: this.configService.get('OAUTH2_CLIENT_REGISTRATION_LOGIN_REDIRECT_URI'),
            scope: this.configService.get('OAUTH2_CLIENT_REGISTRATION_LOGIN_SCOPE'),
        });
        return this.sendOk(res, { authorizationUrl: url });
    }

    @DisableAuthenticatedGuard()
    @DisablePermissionsGuard()
    @Post('sign-in')
    async signIn(@Res() res: Response, @Body() { email, password }: SignInDto) {
        this.logger.log({ email }, 'Sign-in attempt started');

        try {
            const response = await this.keycloakAdminClientService.signIn({ email, password });
            this.logger.log({ email }, 'Keycloak authentication successful');

            const user = await this.userService.getUserByEmail(email);
            this.logger.log({ email, userId: user.id, keycloakId: user.keycloakId }, 'User found in database');

            const organizations = await this.organizationService.getOrganizationsByUserId(user.keycloakId);
            this.logger.log({ email, organizationCount: organizations.length }, 'User organizations retrieved');

            if (organizations.length === 0) {
                this.logger.warn({ email, userId: user.id }, 'User has no organizations - sign-in denied');
                throw new UserDoesntHaveAnyOrganizationsException();
            }

            this.logger.log({ email, userId: user.id }, 'Sign-in successful');
            return this.sendOk(res, response);
        } catch (err) {
            if (err instanceof UserDoesntHaveAnyOrganizationsException) {
                throw err;
            }

            this.logger.log({ email }, 'Primary authentication failed, checking migrated user flow');
            await this.handleMigratedUserPasswordReset(email);

            this.logger.warn({ email }, 'Sign-in failed - invalid credentials');
            throw new InvalidCredentialsException();
        }
    }

    private async handleMigratedUserPasswordReset(email: string): Promise<void> {
        try {
            const user = await this.userService.getUserByEmail(email);

            if (!user?.isMigrated || !user.isActive) {
                this.logger.log(
                    { email, isMigrated: user?.isMigrated, isActive: user?.isActive },
                    'User not eligible for migrated user flow',
                );
                return;
            }

            this.logger.log({ email, userId: user.id }, 'Checking migrated user in Keycloak');
            const keycloakUser = await this.keycloakAdminClientService.getUserByEmail(email);

            if (!keycloakUser) {
                this.logger.log({ email }, 'Migrated user not found in Keycloak');
                return;
            }

            const requiresPasswordUpdate = keycloakUser.requiredActions?.includes('UPDATE_PASSWORD') || false;

            if (!requiresPasswordUpdate) {
                this.logger.log({ email }, 'Migrated user does not require password update');
                return;
            }

            this.logger.log({ email, userId: user.id }, 'Sending password reset email for migrated user');
            await this.sendPasswordResetEmail(email, user.id, user.locale);

            this.logger.log({ email, userId: user.id }, 'Password reset email sent for migrated user');
            throw new UserRequiresPasswordResetException();
        } catch (error) {
            if (error instanceof UserRequiresPasswordResetException) {
                throw error;
            }

            this.logger.error({ err: error, email }, 'Error handling migrated user password reset flow');
        }
    }

    private async sendPasswordResetEmail(email: string, userId: string, locale: string): Promise<void> {
        const { token } = await this.resetTokenService.createResetToken({ userId });
        const uriBase = this.configService.get('FRONTEND_RESET_PASSWORD_URI');
        const uri = `${uriBase}?token=${token}`;

        this.logger.log({ email, userId, locale }, 'Sending password reset email');
        await this.emailService.passwordReset({ email, uri, locale });
    }

    @DisableAuthenticatedGuard()
    @DisablePermissionsGuard()
    @Post('forgot-password')
    async forgotPassword(@Res() res: Response, @Body() body: ForgotPasswordDto) {
        try {
            const keycloakUser = await this.keycloakAdminClientService.getUserByEmail(body.email);

            if (!keycloakUser) {
                this.logger.warn(`Keycloak user not found: ${body.email}`);
                return this.sendOk(res, {
                    sent: 'ok',
                });
            }

            const user = await this.userService.getUserByKeycloakId(keycloakUser.id);
            if (!user) {
                this.logger.warn(`User not found by keycloak id: ${keycloakUser.id}`);
                return this.sendOk(res, {
                    sent: 'ok',
                });
            }
            const { token } = await this.resetTokenService.createResetToken({
                userId: user.id,
            });

            const uriBase = this.configService.get('FRONTEND_RESET_PASSWORD_URI');
            const uri = `${uriBase}?token=${token}`;
            try {
                await this.emailService.passwordReset({ email: body.email, uri, locale: user.locale });
            } catch (error: unknown) {
                this.logger.error({ err: error }, 'Internal error');
                throw new ErrorProcessingRequestException();
            }

            return this.sendOk(res, {
                sent: 'ok',
            });
        } catch (error: unknown) {
            this.logger.error({ err: error }, 'Internal error');
            throw new ErrorProcessingRequestException();
        }
    }

    @DisableAuthenticatedGuard()
    @DisablePermissionsGuard()
    @Post('reset-password')
    async resetPassword(@Res() res: Response, @Body() { token, newPassword }: ResetPasswordDto) {
        try {
            const resetToken = await this.resetTokenService.getToken({ token: token });
            const user = await this.userService.getUserById(resetToken.userId);
            if (!user || !user.isActive) {
                throw new UserNotFoundOrInactiveException();
            }

            await this.keycloakAdminClientService.updateUser(user.keycloakId, {
                credentials: [{ type: 'password', value: newPassword, temporary: false }],
            });

            const response = await this.keycloakAdminClientService.signIn({
                email: user.email,
                password: newPassword,
            });

            await this.resetTokenService.deleteToken(resetToken.id);

            if (user.isMigrated) {
                await this.userService.markUserMigrationComplete(user.id);
            }

            return this.sendOk(res, response);
        } catch (error: unknown) {
            if (error instanceof BadRequestException || error instanceof NotFoundException) {
                throw error;
            }
            this.logger.error({ err: error }, 'Internal error');
            throw new ErrorProcessingRequestException();
        }
    }

    @DisablePermissionsGuard()
    @Post('revoke')
    async revoke(@Res() res: Response, @AccessToken() accessToken: string | null) {
        const client = await buildOpenIdClient();
        await client.revoke(accessToken);
        return this.sendOk(res, { result: true });
    }

    @DisablePermissionsGuard()
    @DisableAuthenticatedGuard()
    @Post('refresh')
    async refresh(@Res() res: Response, @Body() body: { refresh_token: string }) {
        try {
            const client = await buildOpenIdClient();
            const tokenSet = await client.refresh(body.refresh_token);

            return this.sendOk(res, { tokenSet });
        } catch (e) {
            this.logger.error({ err: e }, 'Failed to refresh token');
            throw new InvalidRefreshTokenException();
        }
    }

    @DisableAuthenticatedGuard()
    @DisablePermissionsGuard()
    @Post('authorize')
    async authorize(@Res() res: Response, @Body() query: { session_state: string; iss: string; code: string }) {
        const client = await buildOpenIdClient();
        const loginUri = this.configService.get('OAUTH2_CLIENT_REGISTRATION_LOGIN_REDIRECT_URI');
        const tokenSet = await client.callback(loginUri, query);
        const introspection = await client.introspect(tokenSet.access_token);

        if (!introspection.active) {
            throw new SessionIsNotActiveException();
        }

        await this.updateMetrics();
        return this.sendOk(res, { tokenSet });
    }

    @DisablePermissionsGuard()
    @Post('get-logout-url')
    async getLogoutUrl(@Res() res: Response, @Body() query: { id_token: string }) {
        const client = await buildOpenIdClient();
        const url = client.endSessionUrl({
            id_token_hint: query.id_token,
            post_logout_redirect_uri: this.configService.get('OAUTH2_CLIENT_REGISTRATION_LOGOUT_REDIRECT_URI'),
        });
        return this.sendOk(res, { endSessionUrl: url });
    }

    private async updateMetrics() {
        await Promise.all([
            this.metricsService.updateSessionCount().catch((error) => {
                console.error('Failed to update session count:', error);
            }),
            this.metricsService.updateOfflineSessionCount().catch((error) => {
                console.error('Failed to update offline session count:', error);
            }),
        ]);
    }
}
