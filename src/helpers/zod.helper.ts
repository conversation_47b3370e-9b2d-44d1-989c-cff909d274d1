import { z } from 'zod';

type MetaProperties = {
    [name: string]: string | string[] | number | boolean | MetaProperties;
};

export const addMetaProperties = (schema: z.ZodTypeAny, properties: MetaProperties) => {
    // Type assertion to tell TypeScript that metaOpenApi exists
    if (!(schema as any).metaOpenApi) {
        (schema as any).metaOpenApi = {};
    }

    // Merge properties into metaOpenApi
    (schema as any).metaOpenApi = Object.assign({}, (schema as any).metaOpenApi || {}, properties);

    return schema;
};
