import { Controller, Get, Logger, Request } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as postgres from 'postgres';

import { DisableAuthenticatedGuard } from './auth/authenticated.guard';
import { RequestInterface } from './domain/common/interfaces/request.interface';
import { ActionType, ResourceType } from './domain/users/entities/permission.entity';
import { DisablePermissionsGuard, RequireResourceAction } from './rbac/permissions.decorator';

@Controller()
export class AppController {
    private readonly logger = new Logger(AppController.name);

    constructor(private readonly configService: ConfigService) {}

    @DisableAuthenticatedGuard()
    @DisablePermissionsGuard()
    @Get()
    getHello() {
        this.logger.debug('Health check endpoint accessed');
        return { message: 'Hello World!' };
    }

    // TODO: Remove this endpoint
    @RequireResourceAction(
        { resource: ResourceType.QUOTE, action: ActionType.CREATE },
        { resource: ResourceType.QUOTE, action: ActionType.READ },
    )
    @Get('whoAmI')
    async whoAmI(@Request() req: RequestInterface) {
        this.logger.log('WhoAmI endpoint accessed', {
            userId: req.user?.sub,
            organizationId: req.context?.organizationId,
        });

        try {
            const host = this.configService.get<string>('DATABASE_HOST');
            const port = this.configService.get<number>('DATABASE_PORT');
            const database = this.configService.get<string>('DATABASE_NAME');
            const username = this.configService.get<string>('DATABASE_USER');
            const password = this.configService.get<string>('DATABASE_PASSWORD');

            const sql = postgres(`postgres://${username}:${password}@${host}:${port}/${database}`);

            // Get the current timestamp
            const dbResponse = await sql`SELECT NOW()`;
            const date = dbResponse[0].now;

            this.logger.debug('Database timestamp retrieved successfully', {
                userId: req.user?.sub,
                timestamp: date,
            });

            return {
                timestamp: date,
                user: req.user,
            };
        } catch (error) {
            this.logger.error('Failed to retrieve database timestamp in whoAmI', {
                userId: req.user?.sub,
                organizationId: req.context?.organizationId,
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined,
            });
            throw error;
        }
    }
}
