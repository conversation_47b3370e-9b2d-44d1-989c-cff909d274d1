import { Body, Controller, Headers, Logger, Post, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';

import { DisableAuthenticatedGuard } from '../auth/authenticated.guard';
import { EventActionEnum } from '../domain/common/enums/event-actions.enum';
import { EventNameEnum } from '../domain/common/enums/event-names.enum';
import { DbChanges, GenericDbEvent } from '../domain/common/interfaces/generic-db-event.interface';
import { DisablePermissionsGuard } from '../rbac/permissions.decorator';

interface DatabaseEventPayload {
    action: 'CREATED' | 'UPDATED' | 'DELETED';
    entity: string;
    entityId: string;
    changes: DbChanges;
}

@Controller('webhooks')
export class WebhooksController {
    private readonly logger = new Logger(WebhooksController.name);
    private readonly apiKey: string;

    constructor(
        private readonly eventEmitter: EventEmitter2,
        private readonly configService: ConfigService,
    ) {
        this.apiKey = this.configService.get<string>('DB_LISTENER_API_KEY', '');
    }

    @Post('database-events')
    @DisableAuthenticatedGuard()
    @DisablePermissionsGuard()
    async handleDatabaseEvent(
        @Body() payload: DatabaseEventPayload,
        @Headers('x-api-key') apiKey: string,
    ): Promise<{ success: boolean }> {
        // Validate API key
        if (!apiKey || apiKey !== this.apiKey) {
            this.logger.error('Invalid or missing API key');
            throw new UnauthorizedException('Invalid API key');
        }

        try {
            this.logger.log(`Received database event: ${payload.entity}.${payload.action}`);

            const dbEvent: GenericDbEvent = {
                eventName: this.getEventName(payload.entity, payload.action),
                changes: payload.changes,
                action: this.mapAction(payload.action),
                entity: payload.entity,
                entityId: payload.entityId,
                timestamp: new Date(),
            };

            const eventName = this.toKebabCase(`db.watcher.${payload.entity}.${payload.action.toLowerCase()}`);

            this.logger.log(`Emitting event: ${eventName}`);
            this.eventEmitter.emit(eventName, dbEvent);

            return { success: true };
        } catch (error) {
            this.logger.error('Failed to process database event', error);
            throw error;
        }
    }

    private getEventName(entity: string, action: string): EventNameEnum {
        const entityLower = entity.toLowerCase();
        const actionLower = action.toLowerCase();

        if (entityLower === 'quote') {
            switch (actionLower) {
                case 'created':
                    return EventNameEnum.DB_WATCHER_QUOTE_CREATED;
                case 'updated':
                    return EventNameEnum.DB_WATCHER_QUOTE_UPDATED;
                case 'deleted':
                    return EventNameEnum.DB_WATCHER_QUOTE_DELETED;
            }
        }

        if (entityLower === 'contract') {
            switch (actionLower) {
                case 'created':
                    return EventNameEnum.DB_WATCHER_CONTRACT_CREATED;
                case 'updated':
                    return EventNameEnum.DB_WATCHER_CONTRACT_UPDATED;
                case 'deleted':
                    return EventNameEnum.DB_WATCHER_CONTRACT_DELETED;
            }
        }

        // Default fallback
        return EventNameEnum.DB_WATCHER_WILDCARD;
    }

    private mapAction(action: string): EventActionEnum {
        switch (action.toUpperCase()) {
            case 'CREATED':
                return EventActionEnum.CREATED;
            case 'UPDATED':
                return EventActionEnum.UPDATED;
            case 'DELETED':
                return EventActionEnum.DELETED;
            default:
                return EventActionEnum.UPDATED;
        }
    }

    private toKebabCase(str: string): string {
        return str
            .trim()
            .replace(/([a-z0-9])([A-Z])/g, '$1-$2')
            .replace(/([A-Z])([A-Z][a-z])/g, '$1-$2')
            .toLowerCase();
    }
}
