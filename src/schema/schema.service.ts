import $RefParser from '@apidevtools/json-schema-ref-parser';
import { Injectable } from '@nestjs/common';
import { OpenAPIObject } from '@nestjs/swagger';
import { openapiSchemaToJsonSchema } from '@openapi-contrib/openapi-schema-to-json-schema';
import * as _ from 'lodash';

@Injectable()
export class SchemaService {
    private document: any;

    initialize(openApiObject: OpenAPIObject) {
        this.document = openApiObject;
    }

    getFullSchema(): any {
        return this.document;
    }

    getEndpointSchema(path: string): OpenAPIObject {
        console.log('Looking for endpoint schema for path:', path);

        // Normalize the path to match OpenAPI format
        const normalizedPath = path.startsWith('/') ? path : `/${path}`;
        console.log('Normalized path:', normalizedPath);

        // First check for exact match
        if (this.document?.paths?.[normalizedPath]) {
            console.log('Found exact match for path:', normalizedPath);
            return {
                paths: {
                    [normalizedPath]: this.document.paths[normalizedPath],
                },
            } as OpenAPIObject;
        }

        // If no exact match, try to match with path parameters
        const pathSegments = normalizedPath.split('/').filter(Boolean);
        console.log('Path segments:', pathSegments);

        // Find all potential matching paths from the document
        const documentPaths = Object.keys(this.document?.paths || {});

        // First try to match paths with the same number of segments
        for (const docPath of documentPaths) {
            const docPathSegments = docPath.split('/').filter(Boolean);

            // Skip if segment count doesn't match
            if (docPathSegments.length !== pathSegments.length) {
                continue;
            }

            let isMatch = true;

            for (let i = 0; i < docPathSegments.length; i++) {
                const docSegment = docPathSegments[i];
                const pathSegment = pathSegments[i];

                // If document segment is a parameter (e.g., {entityId})
                if (docSegment.startsWith('{') && docSegment.endsWith('}')) {
                    // This is a parameter, so it matches any value in the path
                    continue;
                }

                // Otherwise, segments must match exactly
                if (docSegment !== pathSegment) {
                    isMatch = false;
                    break;
                }
            }

            if (isMatch) {
                console.log('Found matching path:', docPath);
                return {
                    paths: {
                        [docPath]: this.document.paths[docPath],
                    },
                } as OpenAPIObject;
            }
        }

        // If no match with same segment count, try to find the closest match
        let bestMatchPath = null;
        let bestMatchScore = -1;

        for (const docPath of documentPaths) {
            const docPathSegments = docPath.split('/').filter(Boolean);

            // Try to match as many segments as possible from the beginning
            let matchScore = 0;
            let isPartialMatch = true;

            for (let i = 0; i < Math.min(pathSegments.length, docPathSegments.length); i++) {
                const docSegment = docPathSegments[i];
                const pathSegment = pathSegments[i];

                // If document segment is a parameter
                if (docSegment.startsWith('{') && docSegment.endsWith('}')) {
                    matchScore += 1;
                    continue;
                }

                // Check for exact match
                if (docSegment === pathSegment) {
                    matchScore += 2;
                } else {
                    isPartialMatch = false;
                    break;
                }
            }

            // If we have a partial match and it's better than our current best
            if (isPartialMatch && matchScore > bestMatchScore) {
                bestMatchPath = docPath;
                bestMatchScore = matchScore;
            }
        }

        if (bestMatchPath) {
            console.log('Found best partial match:', bestMatchPath, 'with score:', bestMatchScore);
            return {
                paths: {
                    [bestMatchPath]: this.document.paths[bestMatchPath],
                },
            } as OpenAPIObject;
        }

        console.log('No matching path found for:', normalizedPath);
        return null;
    }

    async getJsonSchema(schemaName?: string): Promise<any> {
        if (!this.document) {
            return null;
        }

        // If a specific schema name is provided
        if (schemaName && this.document.components?.schemas?.[schemaName]) {
            const schema = this.document.components.schemas[schemaName];
            const jsonSchema = openapiSchemaToJsonSchema(schema);
            jsonSchema.$schema = 'http://json-schema.org/draft-07/schema#';
            jsonSchema.title = schemaName;

            // Create a complete schema document with components for dereferencing
            const fullSchema: any = {
                $schema: 'http://json-schema.org/draft-07/schema#',
                title: schemaName,
                ...jsonSchema,
                components: this.document.components,
            };

            // Dereference all $refs
            try {
                const dereferencedSchema = await $RefParser.dereference(fullSchema, {
                    dereference: {
                        circular: 'ignore',
                    },
                });

                // Remove the components property from the result
                delete (dereferencedSchema as any).components;
                return dereferencedSchema;
            } catch (err) {
                console.error('Error dereferencing schema:', err);
                return jsonSchema;
            }
        }

        // Return all schemas
        const result = {};
        if (this.document.components?.schemas) {
            for (const [name, schema] of Object.entries(this.document.components.schemas)) {
                const jsonSchema = openapiSchemaToJsonSchema(schema);
                jsonSchema.$schema = 'http://json-schema.org/draft-07/schema#';
                jsonSchema.title = name;

                // Create a complete schema document with components for dereferencing
                const fullSchema: any = {
                    $schema: 'http://json-schema.org/draft-07/schema#',
                    title: name,
                    ...jsonSchema,
                    components: this.document.components,
                };

                // Dereference all $refs
                try {
                    const dereferencedSchema = await $RefParser.dereference(fullSchema, {
                        dereference: {
                            circular: 'ignore',
                        },
                    });

                    delete (dereferencedSchema as any).components;
                    result[name] = dereferencedSchema;
                } catch (err) {
                    console.error(`Error dereferencing schema ${name}:`, err);
                    result[name] = jsonSchema;
                }
            }
        }

        return result;
    }

    async getJsonSchemasByPaths(): Promise<any> {
        if (!this.document || !this.document.paths) {
            return null;
        }

        const result = {};

        const fullDocument = JSON.parse(JSON.stringify(this.document));

        for (const path in this.document.paths) {
            result[path] = {};
            const pathItem = this.document.paths[path];

            for (const method in pathItem) {
                if (['get', 'post', 'put', 'delete', 'patch', 'options', 'head'].includes(method)) {
                    const operation = pathItem[method];
                    result[path][method] = {
                        operationId: operation.operationId || `${method}${path.replace(/\W+/g, '_')}`,
                        summary: operation.summary || '',
                        schemas: {
                            parameters: await this.convertParametersToJsonSchema(
                                operation.parameters || [],
                                fullDocument,
                            ),
                            request: null,
                            responses: {},
                        },
                    };

                    if (operation.requestBody?.content?.['application/json']?.schema) {
                        const requestSchema = operation.requestBody.content['application/json'].schema;
                        const jsonSchema = openapiSchemaToJsonSchema(requestSchema);
                        jsonSchema.$schema = 'http://json-schema.org/draft-07/schema#';
                        jsonSchema.title = `${method.toUpperCase()} ${path} Request`;

                        const fullSchema: any = {
                            $schema: 'http://json-schema.org/draft-07/schema#',
                            title: jsonSchema.title,
                            ...jsonSchema,
                            components: this.document.components,
                        };

                        try {
                            const dereferencedSchema = await $RefParser.dereference(fullSchema, {
                                dereference: {
                                    circular: 'ignore',
                                },
                            });

                            // Remove the components property from the result
                            delete (dereferencedSchema as any).components;
                            result[path][method].schemas.request = dereferencedSchema;
                        } catch (err) {
                            console.error('Error dereferencing request schema:', err);
                            result[path][method].schemas.request = jsonSchema;
                        }
                    }

                    // Extract response schemas
                    for (const statusCode in operation.responses) {
                        const response = operation.responses[statusCode];
                        if (response.content?.['application/json']?.schema) {
                            const jsonSchema = openapiSchemaToJsonSchema(response.content['application/json'].schema);
                            jsonSchema.$schema = 'http://json-schema.org/draft-07/schema#';
                            jsonSchema.title = `${method.toUpperCase()} ${path} Response ${statusCode}`;

                            // Create a complete schema document with components for dereferencing
                            const fullSchema: any = {
                                $schema: 'http://json-schema.org/draft-07/schema#',
                                title: jsonSchema.title,
                                ...jsonSchema,
                                components: this.document.components,
                            };

                            try {
                                const dereferencedSchema = await $RefParser.dereference(fullSchema, {
                                    dereference: {
                                        circular: 'ignore',
                                    },
                                });

                                // Remove the components property from the result
                                delete (dereferencedSchema as any).components;
                                result[path][method].schemas.responses[statusCode] = dereferencedSchema;
                            } catch (err) {
                                console.error(`Error dereferencing response schema for status ${statusCode}:`, err);
                                result[path][method].schemas.responses[statusCode] = jsonSchema;
                            }
                        }
                    }
                }
            }
        }

        return result;
    }

    async getJsonSchemaForPath(path: string): Promise<any> {
        // console.log('Looking for schema for path:', path);
        // console.log('Available paths:', Object.keys(this.document?.paths || {}));

        // First try exact match
        if (this.document?.paths?.[path]) {
            // console.log('Found exact match for path:', path);
            return this.processPathItem(path, this.document.paths[path]);
        }

        const pathSegments = path.split('/').filter(Boolean);
        // console.log('Path segments:', pathSegments);

        // Find all potential matching paths from the document
        const documentPaths = Object.keys(this.document?.paths || {});

        // First try to match paths with the same number of segments
        for (const docPath of documentPaths) {
            const docPathSegments = docPath.split('/').filter(Boolean);

            // Skip if segment count doesn't match
            if (docPathSegments.length !== pathSegments.length) {
                continue;
            }

            let isMatch = true;
            let matchScore = 0; // Higher score means better match

            for (let i = 0; i < docPathSegments.length; i++) {
                const docSegment = docPathSegments[i];
                const pathSegment = pathSegments[i];

                // If document segment is a parameter (e.g., {entityId})
                if (docSegment.startsWith('{') && docSegment.endsWith('}')) {
                    // This is a parameter, so it matches any value in the path
                    matchScore += 1;
                    continue;
                }

                // Otherwise, segments must match exactly
                if (docSegment !== pathSegment) {
                    isMatch = false;
                    break;
                }

                // Exact segment match gets a higher score
                matchScore += 2;
            }

            if (isMatch) {
                // console.log('Found matching path:', docPath, 'with score:', matchScore);
                return this.processPathItem(docPath, this.document.paths[docPath]);
            }
        }

        // If no match with same segment count, try to find the closest match
        // This handles cases where the path might be a parent path
        let bestMatchPath = null;
        let bestMatchScore = -1;

        for (const docPath of documentPaths) {
            const docPathSegments = docPath.split('/').filter(Boolean);

            // Try to match as many segments as possible from the beginning
            let matchScore = 0;
            let isPartialMatch = true;

            for (let i = 0; i < Math.min(pathSegments.length, docPathSegments.length); i++) {
                const docSegment = docPathSegments[i];
                const pathSegment = pathSegments[i];

                // If document segment is a parameter
                if (docSegment.startsWith('{') && docSegment.endsWith('}')) {
                    matchScore += 1;
                    continue;
                }

                // Check for exact match
                if (docSegment === pathSegment) {
                    matchScore += 2;
                } else {
                    isPartialMatch = false;
                    break;
                }
            }

            // If we have a partial match and it's better than our current best
            if (isPartialMatch && matchScore > bestMatchScore) {
                bestMatchPath = docPath;
                bestMatchScore = matchScore;
            }
        }

        if (bestMatchPath) {
            // console.log('Found best partial match:', bestMatchPath, 'with score:', bestMatchScore);
            return this.processPathItem(bestMatchPath, this.document.paths[bestMatchPath]);
        }

        // console.log('No matching path found for:', path);
        return null;
    }

    private async processPathItem(path: string, pathItem: any): Promise<any> {
        const result = {};
        for (const m in pathItem) {
            if (['get', 'post', 'put', 'delete', 'patch', 'options', 'head'].includes(m)) {
                const operation = pathItem[m];
                result[m] = {
                    operationId: operation.operationId || `${m}${path.replace(/\W+/g, '_')}`,
                    summary: operation.summary || '',
                    schemas: {
                        parameters: await this.convertParametersToJsonSchema(operation.parameters || [], this.document),
                        request: null,
                        responses: {},
                    },
                };

                // Process request body if present
                if (operation.requestBody) {
                    const content = operation.requestBody.content;
                    if (content && content['application/json'] && content['application/json'].schema) {
                        const schema = content['application/json'].schema;
                        result[m].schemas.request = await this.convertSchemaToJsonSchema(schema, this.document);
                    }
                }

                // Process responses
                if (operation.responses) {
                    for (const statusCode in operation.responses) {
                        const response = operation.responses[statusCode];
                        const content = response.content;
                        if (content && content['application/json'] && content['application/json'].schema) {
                            const schema = content['application/json'].schema;
                            result[m].schemas.responses[statusCode] = {
                                description: response.description || '',
                                schema: await this.convertSchemaToJsonSchema(schema, this.document),
                            };
                        } else {
                            result[m].schemas.responses[statusCode] = {
                                description: response.description || '',
                                schema: null,
                            };
                        }
                    }
                }
            }
        }

        return result;
    }

    private async convertParametersToJsonSchema(parameters: any[], fullDocument: any): Promise<any> {
        if (!parameters || !Array.isArray(parameters)) {
            return {};
        }

        const result = {};

        for (const param of parameters) {
            if (!param.name) continue;

            // Create base parameter object with metadata
            const paramSchema: any = {
                in: param.in,
                required: param.required || false,
                description: param.description || '',
                $schema: 'http://json-schema.org/draft-07/schema#',
                title: _.startCase(param?.name) ?? '',
            };

            if (param.schema) {
                const jsonSchema = openapiSchemaToJsonSchema(param.schema);

                Object.assign(paramSchema, jsonSchema);

                const fullSchema: any = {
                    $schema: 'http://json-schema.org/draft-07/schema#',
                    title: paramSchema.title,
                    ...paramSchema,
                    components: fullDocument.components,
                };

                // Dereference all $refs
                try {
                    const dereferencedSchema = await $RefParser.dereference(fullSchema, {
                        dereference: {
                            circular: 'ignore',
                        },
                    });

                    delete (dereferencedSchema as any).components;
                    result[param.name] = dereferencedSchema;
                } catch (err) {
                    console.error(`Error dereferencing parameter schema ${param.name}:`, err);
                    result[param.name] = paramSchema;
                }
            } else if (param.type) {
                paramSchema.type = param.type;

                if (param.format) {
                    paramSchema.format = param.format;
                }

                if (param.enum) {
                    paramSchema.enum = param.enum;
                }

                result[param.name] = paramSchema;
            } else {
                result[param.name] = paramSchema;
            }
        }

        return result;
    }

    private async convertSchemaToJsonSchema(schema: any, document: any): Promise<any> {
        const jsonSchema = openapiSchemaToJsonSchema(schema);
        jsonSchema.$schema = 'http://json-schema.org/draft-07/schema#';
        jsonSchema.title = schema.title || 'Untitled Schema';

        const fullSchema: any = {
            $schema: 'http://json-schema.org/draft-07/schema#',
            title: jsonSchema.title,
            ...jsonSchema,
            components: document.components,
        };

        try {
            const dereferencedSchema = await $RefParser.dereference(fullSchema, {
                dereference: {
                    circular: 'ignore',
                },
            });

            delete (dereferencedSchema as any).components;
            return dereferencedSchema;
        } catch (err) {
            console.error('Error dereferencing schema:', err);
            return jsonSchema;
        }
    }
}
