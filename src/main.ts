import './instrument';

import { patchNestjsSwagger, ZodValidationPipe } from '@anatine/zod-nestjs';
import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { apiReference } from '@scalar/nestjs-api-reference';
import { Logger } from 'nestjs-pino';
import * as passport from 'passport';

import { AppModule } from './app.module';
import { HttpMetricsInterceptor } from './common/interceptors/http-metrics.interceptor';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';
import { HttpExceptionFilter } from './domain/common/filters/http-exception.filter';
import { registerHandlebarsHelpers } from './domain/common/helpers/handlebars.helpers';
import { OrganizationRepository } from './domain/organizations/repositories/organization.repository';
import { SchemaService } from './schema/schema.service';

async function bootstrap() {
    const app = await NestFactory.create<NestExpressApplication>(AppModule, {
        bufferLogs: true,
    });

    app.useLogger(app.get(Logger));

    app.useGlobalPipes(new ZodValidationPipe());
    app.useGlobalFilters(new HttpExceptionFilter());

    const httpMetricsInterceptor = app.get(HttpMetricsInterceptor);

    app.useGlobalInterceptors(new LoggingInterceptor(), httpMetricsInterceptor);

    // Enable CORS for all origins
    app.enableCors({
        origin: '*',
        credentials: true,
    });

    app.use(passport.initialize());

    const organizationRepo = app.get(OrganizationRepository);
    app.use((req, res, next) => {
        (req as any).organizationRepository = organizationRepo;
        next();
    });

    initSwagger(app);
    registerHandlebarsHelpers();

    app.listen(3000, '::');
}

const initSwagger = (app: NestExpressApplication) => {
    const config = new DocumentBuilder()
        .setTitle('Asset Hub API')
        .setDescription('API documentation for the Asset Hub')
        .setVersion('1.0')
        .addBearerAuth(
            {
                type: 'http',
                scheme: 'bearer',
                bearerFormat: 'JWT',
                name: 'JWT',
                description: 'Enter JWT token',
                in: 'header',
            },
            'JWT-auth',
        )
        .build();
    patchNestjsSwagger();
    const document = SwaggerModule.createDocument(app, config);
    app.use('/reference', apiReference({ spec: { content: document } }));

    const schemaService = app.get(SchemaService);
    schemaService.initialize(document);
};

bootstrap();
