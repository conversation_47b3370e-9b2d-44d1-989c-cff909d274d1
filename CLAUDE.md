# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

### Docker Environment
```bash
make up          # Start all services (PostgreSQL, Keycloak, MinIO, Prometheus, Grafana)
make terminal    # Access the app container
make down        # Stop and remove containers
make wipe        # Complete reset (removes .env, databases, containers)
```

**Note**: The application now includes built-in initialization that automatically:
- Waits for database and Keycloak to be ready
- Installs dependencies (local development only)
- Sets up Keycloak realm keys and test user (local development only)
- Runs database migrations
- Seeds the database (local development only)

No separate init container is needed.

### Development
```bash
npm run start:dev     # Start in watch mode
npm run build         # Build for production
npm run lint          # Run ESLint
npm run lint:fix      # Auto-fix ESLint issues
npm run format        # Format code with Prettier
npm run test          # Run unit tests
npm run test:e2e      # Run end-to-end tests
```

### Database Management
```bash
# MikroORM (primary ORM)
npm run migrate              # Run pending migrations
npm run migration:create     # Create new migration

# Drizzle ORM (secondary ORM)
npm run drizzle:migrate      # Run Drizzle migrations
npm run drizzle:generate     # Generate Drizzle migrations
npm run drizzle:studio       # Open Drizzle Studio
npm run seed                 # Seed database (uses Drizzle seeders)
```

## Architecture Overview

### Domain-Driven Design Structure
The codebase follows DDD principles with domains organized under `src/domain/`:
- **assets** - Asset management and enrichment
- **contracts** - Contract handling with items
- **entities** - Core business entities (customers, organizations)
- **quotes** - Quote management with approval workflow
- **users** - User management with RBAC
- **organizations** - Multi-tenancy support
- **files** - File storage and management

### Key Architectural Patterns

1. **Controller-Service-Repository Pattern**
   - Controllers handle HTTP requests
   - Services contain business logic
   - Repositories handle data access

2. **Dual ORM Strategy**
   - MikroORM for entities and migrations (primary)
   - Drizzle ORM for schema management, repositories, and database seeding
   - Migration path moving toward Drizzle

3. **Authentication & Authorization**
   - Keycloak for OIDC authentication
   - RBAC implementation with permissions guard
   - Multi-tenancy via organizations

4. **Data Validation**
   - Zod schemas for all validation
   - DTOs created using `createZodDto` from `@anatine/zod-nestjs`
   - Schemas and models often combined in `.dto.ts` files

## Important Development Rules

### Data Layer
- **Never use EntityManager (em) directly** - Always use specific repositories
- Create repository methods as needed rather than using em
- Drizzle repositories extend `BaseDrizzleRepository`

### Repository Pattern
- Repository naming: `EntityNameRepository` or `EntityNameDrizzleRepository`
- Standard methods: `getById`, `findBy*`, `create`, `update`, `delete`
- Use cursor-based pagination with `PaginatedResponseModel`

### DTO/Model Creation
When creating new DTOs:
```typescript
// Define schema
export const ContractSchema = z.object({
  id: z.string().uuid(),
  // ... fields
});

// Create model from schema
export class ContractModel extends createZodDto(ContractSchema) {}
```

### API Documentation
- Use `@anatine/zod-openapi` for OpenAPI extensions
- Add `extendApi` decorators to endpoints
- Document all request/response models

## External Services

- **PostgreSQL** - Main database (v16.4)
- **Keycloak** - Authentication server
- **MinIO/Azure Storage** - Object storage
- **Brevo** - Email service
- **Freshdesk** - Support ticket integration
- **Prometheus/Grafana** - Monitoring
- **Sentry** - Error tracking

## Testing Approach

- Jest for all testing
- Unit tests alongside source files (`*.spec.ts`)
- E2E tests in `test/` directory
- Run specific test: `npm test -- path/to/test.spec.ts`

## Environment Configuration

- Local development uses Docker Compose
- Environment variables in `.env` file
- Environments: local, test, dev, staging, production
- Check `.env` for Keycloak admin credentials