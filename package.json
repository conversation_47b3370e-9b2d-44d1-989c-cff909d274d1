{"name": "backend-sparrow", "version": "1.39.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\" \"test/**/*.ts\"", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "lint:fix": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "start": "nest start", "start:debug": "nest start --debug --watch", "start:dev": "nest start --watch", "start:prod": "node dist/src/main.js", "test": "jest", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:watch": "jest --watch", "postinstall": "patch-package", "drizzle:pull": "drizzle-kit pull", "drizzle:generate": "drizzle-kit generate", "drizzle:migrate": "drizzle-kit migrate", "drizzle:migrate:prod": "drizzle-kit migrate --config ./dist/drizzle.config.js", "drizzle:seed": "ts-node -r tsconfig-paths/register src/cli.ts db:seed", "seed": "ts-node -r tsconfig-paths/register src/cli.ts db:seed", "drizzle:studio": "drizzle-kit studio", "drizzle:check": "drizzle-kit check"}, "dependencies": {"@anatine/zod-nestjs": "^2.0.10", "@anatine/zod-openapi": "^2.2.7", "@apidevtools/json-schema-ref-parser": "^11.9.3", "@azure/storage-blob": "^12.27.0", "@getbrevo/brevo": "^2.2.0", "@keycloak/keycloak-admin-client": "^26.0.5", "@mikro-orm/cli": "^6.4.5", "@mikro-orm/core": "^6.4.5", "@mikro-orm/migrations": "^6.4.5", "@mikro-orm/nestjs": "^6.1.0", "@mikro-orm/postgresql": "^6.4.5", "@mikro-orm/seeder": "^6.4.5", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.3", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^8.0.7", "@openapi-contrib/openapi-schema-to-json-schema": "^5.1.0", "@scalar/nestjs-api-reference": "^0.3.168", "@sentry/nestjs": "^9.15.0", "@sentry/profiling-node": "^9.15.0", "@types/luxon": "^3.6.0", "@types/pg": "^8.11.11", "@willsoto/nestjs-prometheus": "^6.0.1", "axios": "^1.7.7", "cache-manager": "^6.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "content-disposition": "^0.5.4", "csv-parse": "^5.6.0", "csv-stringify": "^6.5.2", "drizzle-kit": "^0.31.1", "drizzle-orm": "^0.43.1", "exponential-backoff": "^3.1.2", "freshdesk-api": "^3.1.0", "hbs": "^4.2.0", "keycloak-admin": "^1.14.22", "luxon": "^3.6.1", "nest-commander": "^3.17.0", "nestjs-i18n": "^10.5.1", "nestjs-minio-client": "^2.2.0", "nestjs-pino": "^4.4.0", "openid-client": "^5.7.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-oauth2": "^1.8.0", "pg": "^8.15.6", "pino-http": "^10.5.0", "pino-loki": "^2.6.0", "pino-pretty": "^13.0.0", "postgres": "^3.4.4", "prom-client": "^15.1.3", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "uuid": "^10.0.0", "xlsx": "^0.18.5", "zod": "^3.23.8"}, "devDependencies": {"@faker-js/faker": "^9.8.0", "@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.4.19", "@types/content-disposition": "^0.5.8", "@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/multer": "^1.4.12", "@types/node": "^20.16.11", "@types/nodemailer": "^6.4.16", "@types/passport-jwt": "^4.0.1", "@types/pino-http": "^5.8.4", "@types/supertest": "^6.0.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.19.0", "@typescript-eslint/parser": "^8.19.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-simple-import-sort": "^12.1.1", "jest": "^29.7.0", "lint-staged": "^15.3.0", "patch-package": "^8.0.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.4.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.6.3", "vitest": "^2.1.3"}, "overrides": {"@anatine/zod-nestjs": {"@nestjs/swagger": "^8.0.0"}}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coveragePathIgnorePatterns": ["/node_modules/", "\\.dto\\.ts$", "\\.schema\\.ts$", "\\.seeder\\.ts$", "\\.enum\\.ts$", "\\.index\\.ts$"], "coverageDirectory": "../coverage", "testEnvironment": "node", "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}, "moduleNameMapper": {"^src/(.*)$": "<rootDir>/$1"}}, "mikro-orm": {"configPaths": ["./src/configs/mikro-orm.config.ts", "./dist/configs/mikro-orm.config.js"]}}