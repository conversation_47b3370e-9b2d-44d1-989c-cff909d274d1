{"branches": ["main"], "plugins": [["@semantic-release/commit-analyzer", {"preset": "angular", "releaseRules": [{"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "docs", "release": "patch"}, {"type": "style", "release": "patch"}, {"type": "refactor", "release": "patch"}, {"type": "perf", "release": "patch"}, {"type": "test", "release": "patch"}, {"type": "build", "release": "patch"}, {"type": "ci", "release": "patch"}, {"type": "chore", "release": "patch"}], "parserOpts": {"noteKeywords": ["BREAKING CHANGE", "BREAKING CHANGES", "BREAKING"]}}], "@semantic-release/release-notes-generator", ["@semantic-release/changelog", {"changelogFile": "CHANGELOG.md"}], ["@semantic-release/npm", {"npmPublish": false}], ["@semantic-release/git", {"assets": ["package.json", "CHANGELOG.md"], "message": "chore(release): ${nextRelease.version}\n\n${nextRelease.notes}"}], "@semantic-release/github"]}