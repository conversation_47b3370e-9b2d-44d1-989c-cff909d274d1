# Task Completion Checklist

When you complete any coding task, ALWAYS run these commands:

## 1. Code Quality Checks (MANDATORY)
```bash
npm run lint          # Check for linting errors
npm run lint:fix      # Auto-fix linting issues if needed
npm run format        # Format code with Prettier
```

## 2. Run Tests (if applicable)
```bash
npm run test          # Run unit tests for changed code
npm run test:e2e      # Run E2E tests if endpoints were changed
```

## 3. Build Verification
```bash
npm run build         # Ensure the project builds successfully
```

## 4. Git Status Check
```bash
git status            # Review all changes before committing
```

## Important Notes:
- NEVER commit unless explicitly asked by the user
- Always verify lint and typecheck pass before considering task complete
- If lint/typecheck commands are missing, ask user and suggest adding to CLAUDE.md
- The project includes automatic initialization that handles:
  - Database readiness checks
  - Dependency installation (local dev only)
  - Keycloak setup (local dev only)
  - Database migrations
  - Database seeding (local dev only)