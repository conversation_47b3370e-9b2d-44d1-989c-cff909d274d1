# Code Style and Conventions

## TypeScript/JavaScript Style
- **Single quotes** for strings
- **Trailing commas** always
- **Tab width**: 4 spaces
- **Semicolons**: Always use
- **Print width**: 120 characters
- **Arrow functions**: Always use parentheses
- **Import sorting**: Enforced by ESLint (simple-import-sort)

## Architecture Patterns
1. **Domain-Driven Design (DDD)** structure under `src/domain/`
2. **Controller-Service-Repository Pattern**:
   - Controllers handle HTTP requests
   - Services contain business logic
   - Repositories handle data access
3. **Dual ORM Strategy**:
   - MikroORM for entities and migrations (primary)
   - Drizzle ORM for schema management, repositories, and seeding

## Important Rules
- **NEVER use EntityManager (em) directly** - Always use specific repositories
- Repository naming: `EntityNameRepository` or `EntityNameDrizzleRepository`
- Standard repository methods: `getById`, `findBy*`, `create`, `update`, `delete`
- Use cursor-based pagination with `PaginatedResponseModel`

## Data Validation
- Use Zod schemas for all validation
- Create DTOs using `createZodDto` from `@anatine/zod-nestjs`
- Schemas and models often combined in `.dto.ts` files

## API Documentation
- Use `@anatine/zod-openapi` for OpenAPI extensions
- Add `extendApi` decorators to endpoints
- Document all request/response models

## Testing Conventions
- Test files in `__tests__` folders colocated with code
- Unit tests: `*.spec.ts`
- E2E tests: `*.e2e-spec.ts`
- Coverage targets: Services/Controllers/Guards: 100%, Repositories: 85-90%
- Global coverage threshold: 80% (branches, functions, lines, statements)

## Security
- Never expose or log secrets/keys
- Never commit secrets to repository
- Follow security best practices