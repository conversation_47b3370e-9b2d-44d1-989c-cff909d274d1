# Suggested Commands for AssetHub Backend

## Docker Environment
```bash
make up          # Start all services (PostgreSQL, Keycloak, MinIO, Prometheus, Grafana)
make terminal    # Access the app container
make down        # Stop and remove containers
make wipe        # Complete reset (removes .env, databases, containers)
make dup         # Start in daemon mode
make nup         # Start with no cache
```

## Development Commands
```bash
npm run start:dev     # Start in watch mode
npm run build         # Build for production
npm run start:prod    # Start production build
```

## Code Quality Commands (Run these after completing tasks!)
```bash
npm run lint          # Run ESLint
npm run lint:fix      # Auto-fix ESLint issues
npm run format        # Format code with Prettier
npm run format:check  # Check formatting without fixing
```

## Testing Commands
```bash
npm run test          # Run unit tests
npm run test:e2e      # Run end-to-end tests
npm run test:cov      # Run tests with coverage
npm run test:watch    # Run tests in watch mode
```

## Database Commands
### MikroORM (primary ORM)
```bash
npm run migrate              # Run pending migrations
npm run migration:create     # Create new migration
```

### Drizzle ORM (secondary ORM)
```bash
npm run drizzle:migrate      # Run Drizzle migrations
npm run drizzle:generate     # Generate Drizzle migrations
npm run drizzle:studio       # Open Drizzle Studio
npm run seed                 # Seed database (uses Drizzle seeders)
```

## System Commands (Darwin/macOS)
- Standard Unix commands work (ls, cd, grep, find, etc.)
- Use `rg` (ripgrep) instead of grep for better performance