# AssetHub Backend Project Structure

## Root Directory
- `docker/` - Docker configuration files
- `test/` - E2E tests
- `src/` - Main source code
- `drizzle/` - Drizzle ORM migrations
- `i18n/` - Internationalization files
- `scripts/` - Utility scripts
- `templates/` - Template files

## Source Code Structure (`src/`)
```
src/
├── domain/           # Domain-driven design modules
│   ├── assets/       # Asset management and enrichment
│   ├── contracts/    # Contract handling with items
│   ├── entities/     # Core business entities (customers, organizations)
│   ├── quotes/       # Quote management with approval workflow
│   ├── users/        # User management with RBAC
│   ├── organizations/# Multi-tenancy support
│   ├── files/        # File storage and management
│   ├── contacts/     # Contact management
│   ├── products/     # Product management
│   ├── addresses/    # Address management
│   ├── auth/         # Authentication domain logic
│   ├── import/       # Import functionality
│   ├── search/       # Search functionality
│   ├── metrics/      # Metrics and monitoring
│   ├── reporting/    # Reporting features
│   └── feature-flags/# Feature flag management
├── auth/             # Authentication configuration
├── rbac/             # Role-based access control
├── common/           # Shared utilities and components
├── config/           # Application configuration
├── configs/          # Configuration files
├── helpers/          # Helper functions
├── schema/           # Database schemas
├── drizzle/          # Drizzle ORM specific files
├── webhooks/         # Webhook handlers
├── main.ts           # Application entry point
├── app.module.ts     # Root module
├── cli.ts            # CLI commands entry point
└── instrument.ts     # Instrumentation setup
```

## Key Configuration Files
- `package.json` - Dependencies and scripts
- `tsconfig.json` - TypeScript configuration
- `.eslintrc.js` - ESLint rules
- `.prettierrc` - Prettier formatting rules
- `docker-compose.yml` - Docker services configuration
- `Makefile` - Common development commands
- `CLAUDE.md` - Project-specific AI assistant instructions
- `.env.example` - Environment variable template