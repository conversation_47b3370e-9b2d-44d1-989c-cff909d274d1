# AssetHub Backend Overview

## Purpose
AssetHub Backend is a comprehensive asset management system with multi-tenant support. It handles:
- Asset management and enrichment
- Contract handling with items
- Quote management with approval workflow
- Multi-tenancy via organizations
- User management with RBAC
- File storage and management

## Tech Stack
- **Framework**: NestJS v10 (Node.js)
- **Language**: TypeScript
- **Primary ORM**: MikroORM v6 (migrating to Driz<PERSON>)
- **Secondary ORM**: Drizzle ORM (for schema, repositories, and seeding)
- **Database**: PostgreSQL v16.4
- **Authentication**: Keycloak (OIDC)
- **Object Storage**: MinIO/Azure Storage
- **Email Service**: Brevo
- **Monitoring**: Prometheus + Grafana
- **Error Tracking**: Sentry
- **Validation**: Zod schemas with @anatine/zod-nestjs
- **Documentation**: OpenAPI/Swagger
- **Testing**: Jest
- **Package Manager**: npm

## Environments
- local (Docker-based development)
- test
- dev
- staging
- production